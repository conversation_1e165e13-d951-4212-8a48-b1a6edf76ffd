<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ydwl-live</artifactId>
        <groupId>com.ydwl</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>yyx-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-social</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-job</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-generator</artifactId>
        </dependency>

        <!--  demo模块  -->
        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-demo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-transcoding</artifactId>
            <version>${revision}</version>
        </dependency>


        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-live</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- skywalking 整合 logback -->
<!--        <dependency>-->
<!--            <groupId>org.apache.skywalking</groupId>-->
<!--            <artifactId>apm-toolkit-logback-1.x</artifactId>-->
<!--            <version>${与你的agent探针版本保持一致}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.skywalking</groupId>-->
<!--            <artifactId>apm-toolkit-trace</artifactId>-->
<!--            <version>${与你的agent探针版本保持一致}</version>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
