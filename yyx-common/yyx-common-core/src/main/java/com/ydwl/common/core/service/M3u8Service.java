package com.ydwl.common.core.service;

import com.ydwl.common.core.domain.dto.OssDTO;

import java.util.List;

/**
 * 通用 OSS服务
 *
 * <AUTHOR> Yi
 */
public interface M3u8Service {


    /**
     * 封面图获取签名
     * @param url 链接
     * @return 带签名的链接
     */
    String getCoverSignUrl(String url);


    /**
     * m3u8获取签名 这里前端弄的太麻烦了，这个生成一个特殊的链接可以链式获取到TS文件，Ts也是带上签名的
     * @param url 链接
     * @param expiredTime 过期时间(s) 这个不知道设置多长合适，后面测试一下
     * @return 带签名的链接
     */
    String getM3u8SignUrl(String url , long expiredTime);
}
