<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ydwl</groupId>
    <artifactId>yyx-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        yyx-common-bom common依赖项
    </description>

    <properties>
        <revision>5.4.0</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 幂等 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 调度模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 邮件服务 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- OSS -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-ratelimiter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- satoken -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 短信模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-sms</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-social</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 翻译模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库加解密模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- WebSocket模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- SSE模块 -->
            <dependency>
                <groupId>com.ydwl</groupId>
                <artifactId>yyx-common-sse</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>
