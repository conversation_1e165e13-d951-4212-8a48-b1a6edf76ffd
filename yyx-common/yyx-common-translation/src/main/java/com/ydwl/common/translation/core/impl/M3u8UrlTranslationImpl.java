package com.ydwl.common.translation.core.impl;

import com.ydwl.common.core.service.M3u8Service;
import com.ydwl.common.translation.annotation.TranslationType;
import com.ydwl.common.translation.constant.TransConstant;
import com.ydwl.common.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;

/**
 * OSS翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.M3U8VIDEO_URL_TO_PUBLIC_URL)
public class M3u8UrlTranslationImpl implements TranslationInterface<String> {

    private final M3u8Service service;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof String url) {
            return service.getM3u8SignUrl(url, 3600);
        }
        return null;
    }

}
