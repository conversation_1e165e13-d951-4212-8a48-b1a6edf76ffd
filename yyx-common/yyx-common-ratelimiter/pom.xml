<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ydwl</groupId>
        <artifactId>yyx-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yyx-common-ratelimiter</artifactId>

    <description>
        yyx-common-ratelimiter 限流功能
    </description>

    <dependencies>
        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-redis</artifactId>
        </dependency>
    </dependencies>

</project>
