2025-06-08 17:11:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:11:58 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 30001 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:11:58 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:12:00 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:12:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:12:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:12:00 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@63e40188
2025-06-08 17:12:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:12:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:12:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:12:01 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:12:01 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:12:01 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:12:01 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:12:02 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:12:02 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:12:02 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:12:02 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:12:02 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@28cb9120
2025-06-08 17:12:02 [main] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:12:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:12:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:12:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:12:02 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:12:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:14:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:14:13 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 30494 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:14:13 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:14:15 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:14:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:14:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:14:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1d6dc2b8
2025-06-08 17:14:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:14:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:14:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:14:17 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:14:17 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:14:18 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:14:18 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:14:18 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:14:18 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:14:18 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:14:19 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:14:19 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@69eb86b4
2025-06-08 17:14:19 [main] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:14:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:14:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:14:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:14:19 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:14:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:17:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:17:04 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 31091 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:17:04 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:17:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:17:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:17:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:17:06 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@457d3f54
2025-06-08 17:17:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:17:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:17:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:17:06 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:17:06 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:17:07 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:17:07 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:17:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:17:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:17:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:17:07 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:17:07 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30feffc
2025-06-08 17:17:08 [main] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:17:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:17:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:17:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:17:08 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:17:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:18:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:18:52 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 32340 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:18:52 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:18:54 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:18:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:18:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:18:54 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@399a5a52
2025-06-08 17:18:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:18:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:18:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:18:55 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:18:55 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:18:55 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:18:55 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:18:56 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:18:56 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:18:56 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:18:56 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:18:56 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@70325d20
2025-06-08 17:18:56 [main] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:18:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:18:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:18:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:18:56 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:18:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:30:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:30:39 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 33243 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:30:39 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:30:40 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:30:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:30:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:30:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@18d09283
2025-06-08 17:30:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:30:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:30:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:30:41 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:30:41 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:30:41 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:30:42 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:30:42 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:30:42 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:30:42 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:30:42 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:30:42 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@323e8306
2025-06-08 17:30:44 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 17:30:44 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 17:30:44 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 17:30:44 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 17:30:44 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 5.93 seconds (process running for 6.142)
2025-06-08 17:30:44 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 17:30:45 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 17:30:45 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 17:30:45 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 17:30:45 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 17:30:45 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 17:30:54 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 17:30:54 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-08 17:30:55 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[177]毫秒
2025-06-08 17:30:55 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-06-08 17:30:55 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1]毫秒
2025-06-08 17:30:55 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 17:30:55 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:30:55 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[45]毫秒
2025-06-08 17:30:55 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:30:56 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[843]毫秒
2025-06-08 17:30:58 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:30:58 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:30:58 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[13]毫秒
2025-06-08 17:31:00 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:31:00 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:31:00 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[14]毫秒
2025-06-08 17:31:02 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:31:02 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:31:02 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[14]毫秒
2025-06-08 17:31:06 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:31:06 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 17:31:06 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 5, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:31:06 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[11]毫秒
2025-06-08 17:31:06 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[19]毫秒
2025-06-08 17:31:08 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:31:08 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 4, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:31:08 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[16]毫秒
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:31:25 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:32:37 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:32:37 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 33993 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:32:37 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:32:39 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:32:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:32:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:32:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@188532da
2025-06-08 17:32:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:32:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:32:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:32:40 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:32:40 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:32:41 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:32:41 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:32:41 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:32:41 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:32:41 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:32:42 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:32:42 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7b44b63d
2025-06-08 17:32:47 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 17:32:47 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 17:32:47 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 17:32:47 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 17:32:47 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 10.436 seconds (process running for 11.006)
2025-06-08 17:32:47 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 17:32:47 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 17:32:47 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 17:32:47 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 17:32:47 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 17:32:47 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 17:32:48 [RMI TCP Connection(3)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 17:34:05 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:34:05 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[58]毫秒
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:34:39 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:34:42 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:34:42 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 34206 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:34:42 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:34:44 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:34:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:34:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:34:44 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@396519b
2025-06-08 17:34:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:34:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:34:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:34:45 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:34:45 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:34:45 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:34:45 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:34:45 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:34:45 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:34:45 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:34:46 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:34:46 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2db2cd5
2025-06-08 17:34:50 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 17:34:50 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 17:34:50 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 17:34:50 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 17:34:50 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 8.707 seconds (process running for 9.205)
2025-06-08 17:34:50 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 17:34:50 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 17:34:50 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 17:34:51 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 17:34:51 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 17:34:51 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 17:34:51 [RMI TCP Connection(4)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 17:38:38 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:38:39 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 516 ms
2025-06-08 17:38:39 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[640]毫秒
2025-06-08 17:39:26 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:39:26 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[4]毫秒
2025-06-08 17:39:58 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:39:58 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[5]毫秒
2025-06-08 17:40:01 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:40:01 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[1]毫秒
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:40:45 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:40:48 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:40:48 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 34382 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:40:48 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:40:49 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:40:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:40:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:40:50 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7e4c0bc7
2025-06-08 17:40:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:40:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:40:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:40:50 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:40:51 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:40:51 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:40:51 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:40:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:40:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:40:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:40:52 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:40:52 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38499e48
2025-06-08 17:40:56 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 17:40:56 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 17:40:56 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 17:40:56 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 17:40:56 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 8.888 seconds (process running for 9.264)
2025-06-08 17:40:56 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 17:40:56 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 17:40:56 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 17:40:57 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 17:40:57 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 17:40:57 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 17:40:57 [RMI TCP Connection(3)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 17:41:08 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:41:08 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 339 ms
2025-06-08 17:41:08 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[400]毫秒
2025-06-08 17:41:09 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:41:09 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[1]毫秒
2025-06-08 17:41:09 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:41:09 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[2]毫秒
2025-06-08 17:41:09 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:41:09 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[2]毫秒
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:41:19 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:42:20 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:42:20 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 34772 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:42:20 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:43:05 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:43:05 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 35012 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:43:05 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:43:07 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:43:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:43:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:43:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5d1eb214
2025-06-08 17:43:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:43:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:43:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:43:08 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:43:08 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:43:08 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:43:08 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:43:09 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:43:09 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:43:09 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:43:09 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:43:09 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7b44b63d
2025-06-08 17:43:14 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 17:43:14 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 17:43:14 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 17:43:14 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 17:43:14 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 9.021 seconds (process running for 9.415)
2025-06-08 17:43:14 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 17:43:14 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 17:43:14 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 17:43:14 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 17:43:14 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 17:43:15 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 17:43:15 [RMI TCP Connection(4)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 17:43:19 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:43:19 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 276 ms
2025-06-08 17:43:19 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[328]毫秒
2025-06-08 17:43:21 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:43:21 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[2]毫秒
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:44:03 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:47:36 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:47:36 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 35343 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:47:36 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:47:38 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:47:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:47:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:47:38 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1c3400df
2025-06-08 17:47:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:47:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:47:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:47:39 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:47:39 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:47:39 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:47:39 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:47:40 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:47:40 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:47:40 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:47:41 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:47:41 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7c974942
2025-06-08 17:47:46 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 17:47:46 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 17:47:46 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 17:47:46 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 17:47:46 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 10.154 seconds (process running for 10.498)
2025-06-08 17:47:46 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 17:47:46 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 17:47:46 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 17:47:46 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 17:47:46 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 17:47:46 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 17:47:46 [RMI TCP Connection(2)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 17:48:30 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:48:32 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 2234 ms
2025-06-08 17:48:32 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[2281]毫秒
2025-06-08 17:48:45 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],无参数
2025-06-08 17:48:45 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /v3/api-docs/5.%E7%9B%B4%E6%92%AD%E6%A8%A1%E5%9D%97],耗时:[18]毫秒
2025-06-08 17:49:53 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 17:49:53 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:49:53 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[58]毫秒
2025-06-08 17:49:53 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:49:54 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[1062]毫秒
2025-06-08 17:49:55 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:49:55 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:49:55 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[19]毫秒
2025-06-08 17:50:03 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:50:03 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:50:03 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[17]毫秒
2025-06-08 17:50:20 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 17:50:20 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:50:20 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 6, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:50:20 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[27]毫秒
2025-06-08 17:50:20 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[54]毫秒
2025-06-08 17:50:23 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:50:23 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 5, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:50:23 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[13]毫秒
2025-06-08 17:50:32 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:50:32 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 4, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:50:32 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[15]毫秒
2025-06-08 17:50:50 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 17:50:50 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 17:50:50 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 3, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 17:50:50 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[21]毫秒
2025-06-08 17:50:50 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:51:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:52:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:52:00 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 35478 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:52:00 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:52:02 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:52:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:52:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:52:03 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@40dc2ade
2025-06-08 17:52:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:52:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:52:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:52:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:52:04 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 35488 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:52:04 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:52:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:52:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:52:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:52:06 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7be9e9fc
2025-06-08 17:52:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:52:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:52:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:52:07 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:52:07 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:52:07 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:52:07 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:52:08 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:52:08 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:52:08 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:52:09 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:52:09 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7c974942
2025-06-08 17:52:13 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 17:52:13 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 17:52:13 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 17:52:13 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 17:52:13 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 9.908 seconds (process running for 10.285)
2025-06-08 17:52:13 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 17:52:13 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 17:52:13 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 17:52:14 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 17:52:14 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 17:52:14 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 17:52:14 [RMI TCP Connection(1)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 17:52:30 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 17:52:30 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveReplayServiceImpl - 生成m3u8签名URL: https://video-ydwl.oss-cn-beijing.aliyuncs.com/live-videos/54/2025/06/06/256a18a2-e540-4cec-9fc8-aee35b8db151.m3u8?OSSAccessKeyId=LTAI5tGA15ASheoXwvc7pYcN&Expires=1749462750&Signature=7HLqHLHp82v%2F2ZUMvZag4XDrxi4%3D&x-oss-process=hls/sign, 原始路径: live-videos/54/2025/06/06/256a18a2-e540-4cec-9fc8-aee35b8db151.m3u8
2025-06-08 17:52:30 [XNIO-1 task-2] INFO  c.ydwl.common.oss.factory.OssFactory - 创建OSS实例 =====> aliyun-video-ydwl
2025-06-08 17:52:30 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[354]毫秒
2025-06-08 17:53:37 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 17:53:37 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveReplayServiceImpl - 生成m3u8签名URL: https://video-ydwl.oss-cn-beijing.aliyuncs.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8?OSSAccessKeyId=LTAI5tGA15ASheoXwvc7pYcN&Expires=1749462817&Signature=u70HTzSoKPm35wMSIO7YyXNY9oo%3D&x-oss-process=hls/sign, 原始路径: live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 17:53:37 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[40]毫秒
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 17:56:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 17:56:11 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 17:56:11 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 35622 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 17:56:11 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 17:56:13 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 17:56:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 17:56:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 17:56:13 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5d1907fb
2025-06-08 17:56:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 17:56:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 17:56:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 17:56:14 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 17:56:14 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 17:56:14 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 17:56:15 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 17:56:16 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:56:16 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:56:16 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 17:56:16 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 17:56:16 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3701e6e4
2025-06-08 17:56:21 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 17:56:21 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 17:56:21 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 17:56:21 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 17:56:21 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 10.694 seconds (process running for 11.548)
2025-06-08 17:56:21 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 17:56:21 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 17:56:21 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 17:56:21 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 17:56:21 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 17:56:22 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 17:56:22 [RMI TCP Connection(3)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 17:57:03 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 17:57:03 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveReplayServiceImpl - 生成m3u8签名URL: https://video-ydwl.oss-cn-beijing.aliyuncs.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8?OSSAccessKeyId=LTAI5tGA15ASheoXwvc7pYcN&Expires=1749463023&Signature=tLwFNFwOuKG1ljxqt68ofWL9xnM%3D&x-oss-process=hls/sign, 原始路径: live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 17:57:03 [XNIO-1 task-2] INFO  c.ydwl.common.oss.factory.OssFactory - 创建OSS实例 =====> aliyun-video-ydwl
2025-06-08 17:57:03 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[794]毫秒
2025-06-08 18:00:14 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 18:00:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 18:00:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 18:00:17 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 35692 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 18:00:17 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 18:00:19 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 18:00:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 18:00:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 18:00:20 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7a79a5d6
2025-06-08 18:00:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 18:00:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 18:00:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 18:00:21 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 18:00:21 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 18:00:21 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 18:00:21 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 18:00:22 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:00:22 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:00:22 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:00:23 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 18:00:23 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e31c3ec
2025-06-08 18:00:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 18:00:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 18:00:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 18:00:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 18:00:28 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 11.914 seconds (process running for 12.865)
2025-06-08 18:00:28 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 18:00:28 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 18:00:28 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 18:00:29 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 18:00:29 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 18:00:29 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 18:00:30 [RMI TCP Connection(12)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 18:01:25 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:01:25 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveReplayServiceImpl - 生成m3u8签名URL: https://video-ydwl.oss-cn-beijing.aliyuncs.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8?OSSAccessKeyId=LTAI5tGA15ASheoXwvc7pYcN&Expires=1749463285&Signature=3x35Hf0%2F3rPNZwHkPHyHIH%2BdAa8%3D&x-oss-process=hls/sign, 原始路径: live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:01:26 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[164]毫秒
2025-06-08 18:16:28 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:16:28 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[42]毫秒
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 18:19:06 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 18:19:08 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 18:19:08 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 36187 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 18:19:08 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 18:19:11 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 18:19:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 18:19:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 18:19:11 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2bfe4df9
2025-06-08 18:19:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 18:19:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 18:19:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 18:19:12 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 18:19:12 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 18:19:13 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 18:19:13 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 18:19:14 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:19:14 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:19:14 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:19:14 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 18:19:15 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@328902d5
2025-06-08 18:19:20 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 18:19:20 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 18:19:20 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 18:19:20 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 18:19:20 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 11.755 seconds (process running for 13.084)
2025-06-08 18:19:20 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 18:19:20 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 18:19:20 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 18:19:20 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 18:19:20 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 18:19:20 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 18:19:21 [RMI TCP Connection(8)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 18:19:38 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:19:38 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[174]毫秒
2025-06-08 18:21:21 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:21:21 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:21:21 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[31]毫秒
2025-06-08 18:21:57 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:21:57 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:21:57 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[34]毫秒
2025-06-08 18:23:31 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:23:31 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:23:55 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:23:55 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[24109]毫秒
2025-06-08 18:24:00 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:24:00 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:24:05 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:25:27 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[86783]毫秒
2025-06-08 18:25:31 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:25:31 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:25:32 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:25:32 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[963]毫秒
2025-06-08 18:25:35 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:25:35 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:25:46 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:25:46 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[11011]毫秒
2025-06-08 18:25:46 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:25:46 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:25:47 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:25:47 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[1123]毫秒
2025-06-08 18:26:03 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:26:03 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:27:01 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:27:01 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[57886]毫秒
2025-06-08 18:27:27 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:27:27 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:28:27 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:28:27 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[59384]毫秒
2025-06-08 18:28:31 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:28:31 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:28:33 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:28:33 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[1766]毫秒
2025-06-08 18:29:16 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:29:16 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:29:17 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:29:17 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[1611]毫秒
2025-06-08 18:30:10 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:30:10 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:30:11 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:30:11 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[1012]毫秒
2025-06-08 18:30:55 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:30:55 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:31:07 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:31:07 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[12199]毫秒
2025-06-08 18:32:39 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:32:39 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:32:40 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:32:40 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[855]毫秒
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 18:36:01 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 18:37:30 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 18:37:30 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 36770 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 18:37:30 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 18:37:32 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 18:37:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 18:37:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 18:37:32 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@60c68c7b
2025-06-08 18:37:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 18:37:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 18:37:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 18:37:33 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 18:37:33 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 18:37:34 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 18:37:34 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 18:37:35 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:37:35 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:37:35 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:37:35 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 18:37:35 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@627d8516
2025-06-08 18:37:40 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 18:37:40 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 18:37:40 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 18:37:40 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 18:37:40 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 10.61 seconds (process running for 11.847)
2025-06-08 18:37:40 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 18:37:40 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 18:37:40 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 18:37:41 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 18:37:41 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 18:37:41 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 18:37:41 [RMI TCP Connection(5)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 18:38:52 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:38:52 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:38:53 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:38:53 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[1756]毫秒
2025-06-08 18:40:15 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:40:15 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:40:18 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:40:18 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[2990]毫秒
2025-06-08 18:41:05 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:41:05 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:41:27 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:41:27 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[22003]毫秒
2025-06-08 18:41:33 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:41:33 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:41:34 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:41:34 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[1321]毫秒
2025-06-08 18:41:54 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:41:54 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:41:54 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里允许签名成功
2025-06-08 18:41:54 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[786]毫秒
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 18:50:17 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 18:51:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 18:51:15 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 37585 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 18:51:15 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 18:51:17 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 18:51:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 18:51:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 18:51:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e7563f6
2025-06-08 18:51:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 18:51:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 18:51:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 18:51:18 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 18:51:18 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 18:51:18 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 18:51:18 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 18:51:19 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:51:19 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:51:19 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:51:20 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 18:51:20 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6e6fce47
2025-06-08 18:51:24 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 18:51:24 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 18:51:24 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 18:51:24 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 18:51:24 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 9.406 seconds (process running for 9.742)
2025-06-08 18:51:24 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 18:51:24 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 18:51:24 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 18:51:25 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 18:51:25 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 18:51:25 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 18:51:25 [RMI TCP Connection(4)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 18:51:32 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:51:32 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:51:32 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[137]毫秒
2025-06-08 18:52:01 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 18:52:01 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 18:52:01 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 18:52:01 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[52]毫秒
2025-06-08 18:52:02 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[787]毫秒
2025-06-08 18:52:04 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 18:52:04 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 18:52:04 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/auth/code:0:0:0:0:0:0:0:1:'
2025-06-08 18:52:04 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[30]毫秒
2025-06-08 18:52:04 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[33]毫秒
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 18:56:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 18:56:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 18:56:13 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 37692 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 18:56:13 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 18:56:15 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 18:56:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 18:56:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 18:56:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@10c1682b
2025-06-08 18:56:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 18:56:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 18:56:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 18:56:16 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 18:56:16 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 18:56:17 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 18:56:17 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 18:56:18 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:56:18 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:56:18 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:56:18 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 18:56:18 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b580b88
2025-06-08 18:56:23 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 18:56:23 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 18:56:23 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 18:56:23 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 18:56:23 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 10.362 seconds (process running for 10.719)
2025-06-08 18:56:23 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 18:56:23 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 18:56:23 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 18:56:23 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 18:56:24 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 18:56:24 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 18:56:24 [RMI TCP Connection(3)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 18:57:21 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:57:21 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:57:21 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[171]毫秒
2025-06-08 18:59:15 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 18:59:15 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 18:59:15 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[27]毫秒
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 18:59:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 18:59:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 18:59:46 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 37756 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 18:59:46 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 18:59:48 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 18:59:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 18:59:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 18:59:49 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@48cebb78
2025-06-08 18:59:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 18:59:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 18:59:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 18:59:49 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 18:59:49 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 18:59:50 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 18:59:50 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 18:59:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:59:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:59:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 18:59:51 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 18:59:51 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4ba380c7
2025-06-08 18:59:56 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 18:59:56 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 18:59:56 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 18:59:56 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 18:59:56 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 10.041 seconds (process running for 10.781)
2025-06-08 18:59:56 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 18:59:56 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 18:59:56 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 18:59:56 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 18:59:56 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 18:59:57 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 18:59:57 [RMI TCP Connection(8)-192.168.1.236] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 19:00:02 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:00:02 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:00:02 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[159]毫秒
2025-06-08 19:00:30 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:00:30 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:01:25 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[55146]毫秒
2025-06-08 19:03:25 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:03:25 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:03:25 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[32]毫秒
2025-06-08 19:08:04 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:08:04 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:08:04 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[32]毫秒
2025-06-08 19:09:40 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:09:40 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:09:40 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[21]毫秒
2025-06-08 19:11:20 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:11:20 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:13:04 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:13:04 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[104138]毫秒
2025-06-08 19:13:04 [XNIO-1 task-3] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:13:07 [XNIO-1 task-3] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[3100]毫秒
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 19:13:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-08 19:16:48 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 19:16:48 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 39913 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 19:16:48 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 19:16:50 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 19:16:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 19:16:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 19:16:51 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e22dadb
2025-06-08 19:16:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 19:16:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 19:16:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 19:16:52 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 19:16:52 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 19:16:52 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 19:16:52 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 19:16:53 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:16:53 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:16:53 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:16:54 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 19:16:54 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d84cb86
2025-06-08 19:16:56 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 19:16:56 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 19:16:56 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 19:16:56 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 19:16:56 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 7.996 seconds (process running for 8.487)
2025-06-08 19:16:56 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 19:16:56 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 19:16:56 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 19:16:56 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 19:16:56 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 19:16:56 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 19:17:03 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 19:17:03 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:17:03 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:17:03 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[356]毫秒
2025-06-08 19:21:21 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:21:21 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:21:21 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[31]毫秒
2025-06-08 19:22:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 19:22:46 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 40613 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 19:22:46 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 19:22:49 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 19:22:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 19:22:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 19:22:49 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7a91027c
2025-06-08 19:22:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 19:22:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 19:22:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 19:22:50 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 19:22:50 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 19:22:50 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 19:22:50 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 19:22:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:22:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:22:51 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:22:51 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 19:22:51 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d84cb86
2025-06-08 19:22:53 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 19:22:53 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 19:22:53 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 19:22:53 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 19:22:54 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 7.391 seconds (process running for 7.746)
2025-06-08 19:22:54 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 19:22:54 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 19:22:54 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 19:22:54 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 19:22:54 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 19:22:54 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 19:23:00 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 19:23:01 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:23:01 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:23:02 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[965]毫秒
2025-06-08 19:24:40 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 19:24:40 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 40808 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 19:24:40 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 19:24:42 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 19:24:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 19:24:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 19:24:42 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e22dadb
2025-06-08 19:24:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 19:24:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 19:24:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 19:24:43 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 19:24:43 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 19:24:43 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 19:24:43 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 19:24:45 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:24:45 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:24:45 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:24:45 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 19:24:45 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d84cb86
2025-06-08 19:24:47 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 19:24:47 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 19:24:47 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 19:24:47 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 19:24:47 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 7.557 seconds (process running for 7.838)
2025-06-08 19:24:47 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 19:24:47 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 19:24:47 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 19:24:47 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 19:24:48 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 19:24:48 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 19:24:54 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 19:24:54 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:24:54 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:24:54 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 待签名字符串: GET


1749385494
/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8?x-oss-process=hls/sign
2025-06-08 19:24:54 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 签名结果: WRKB7EzyouUxJTvkIq6NdhzCzF4%3D
2025-06-08 19:24:54 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 生成的签名URL: https://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8?OSSAccessKeyId=LTAI5tGA15ASheoXwvc7pYcN&Expires=1749385494&Signature=WRKB7EzyouUxJTvkIq6NdhzCzF4%3D&x-oss-process=hls/sign
2025-06-08 19:24:54 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[361]毫秒
2025-06-08 19:26:41 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 19:26:41 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 40930 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 19:26:41 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 19:26:45 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 19:26:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 19:26:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 19:26:45 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@9120cb5
2025-06-08 19:26:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 19:26:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 19:26:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 19:26:46 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 19:26:46 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 19:26:46 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 19:26:46 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 19:26:47 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:26:47 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:26:47 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:26:47 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 19:26:47 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d84cb86
2025-06-08 19:26:49 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 19:26:49 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 19:26:49 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 19:26:49 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 19:26:49 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 8.768 seconds (process running for 9.041)
2025-06-08 19:26:49 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 19:26:49 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 19:26:49 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 19:26:50 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 19:26:50 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 19:26:50 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 19:26:57 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 19:26:57 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:26:58 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:26:58 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 待签名字符串(hex): 4745540a0a0a313734393338353631380a2f6c6976652d766964656f732f35322f323032352f30362f30382f34653034346161342d626161302d343161662d623232312d3131383632313630323332642e6d3375383f782d6f73732d70726f636573733d686c732f7369676e
2025-06-08 19:26:58 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 签名结果: 5U%2BHpgrXXALWn4sLXRFxTcGCLoA%3D
2025-06-08 19:26:58 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 生成的签名URL: https://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8?OSSAccessKeyId=LTAI5tGA15ASheoXwvc7pYcN&Expires=1749385618&Signature=5U%2BHpgrXXALWn4sLXRFxTcGCLoA%3D&x-oss-process=hls/sign
2025-06-08 19:26:58 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[368]毫秒
2025-06-08 19:28:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 19:28:28 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 41087 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 19:28:28 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 19:28:30 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 19:28:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 19:28:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 19:28:30 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@9120cb5
2025-06-08 19:28:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 19:28:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 19:28:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 19:28:31 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 19:28:31 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 19:28:32 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 19:28:32 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 19:28:32 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:28:32 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:28:32 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:28:33 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 19:28:33 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d84cb86
2025-06-08 19:28:35 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 19:28:35 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 19:28:35 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 19:28:35 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 19:28:35 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 6.938 seconds (process running for 7.299)
2025-06-08 19:28:35 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 19:28:35 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 19:28:35 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 19:28:35 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 19:28:35 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 19:28:35 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 19:28:41 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 19:28:41 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:28:42 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:28:42 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 待签名字符串: /live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8-1749385722
2025-06-08 19:28:42 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 签名结果: cfbfade7673df17d6e4369c5de915738
2025-06-08 19:28:42 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 生成的签名URL: https://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8?auth_key=1749385722-0-0-cfbfade7673df17d6e4369c5de915738
2025-06-08 19:28:42 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[382]毫秒
2025-06-08 19:36:28 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:36:28 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 这里是待转换的urlhttps://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8
2025-06-08 19:36:28 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 待签名字符串: /live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8-1749386188
2025-06-08 19:36:28 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 签名结果: 1b49fbd27ccd780a30b1d6bb69c2788f
2025-06-08 19:36:28 [XNIO-1 task-2] INFO  c.y.l.s.impl.LiveOssServiceImpl - 生成的签名URL: https://mps.play.ycyyx.com/live-videos/52/2025/06/08/4e044aa4-baa0-41af-b221-11862160232d.m3u8?auth_key=1749386188-0-0-1b49fbd27ccd780a30b1d6bb69c2788f
2025-06-08 19:36:28 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[64]毫秒
2025-06-08 19:40:00 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:40:00 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[52]毫秒
2025-06-08 19:40:07 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 19:40:07 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 41664 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 19:40:07 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 19:40:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 19:40:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 19:40:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 19:40:10 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@9120cb5
2025-06-08 19:40:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 19:40:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 19:40:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 19:40:11 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 19:40:11 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 19:40:12 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 19:40:12 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 19:40:13 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:40:13 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:40:13 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:40:13 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 19:40:13 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d84cb86
2025-06-08 19:40:15 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 19:40:15 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 19:40:15 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 19:40:15 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 19:40:15 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 8.017 seconds (process running for 8.361)
2025-06-08 19:40:15 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 19:40:15 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 19:40:15 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 19:40:15 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 19:40:15 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 19:40:15 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 19:41:34 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 19:41:34 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 41758 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 19:41:34 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 19:41:35 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 19:41:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 19:41:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 19:41:36 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@27d44578
2025-06-08 19:41:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 19:41:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 19:41:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 19:41:37 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 19:41:37 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 19:41:37 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 19:41:37 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 19:41:38 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:41:38 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:41:38 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:41:38 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 19:41:38 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d84cb86
2025-06-08 19:41:40 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 19:41:40 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 19:41:40 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 19:41:40 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 19:41:40 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 7.014 seconds (process running for 7.323)
2025-06-08 19:41:40 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 19:41:40 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 19:41:40 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 19:41:41 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 19:41:41 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 19:41:41 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 19:41:46 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 19:41:46 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:41:47 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[375]毫秒
2025-06-08 19:42:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 19:42:44 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.7 with PID 41886 (/Users/<USER>/Desktop/直播项目/api/yyx-admin/target/classes started by yiyunxu in /Users/<USER>/Desktop/直播项目/api)
2025-06-08 19:42:44 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 19:42:46 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 19:42:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 19:42:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 19:42:46 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-06-08 19:42:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 19:42:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 19:42:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 19:42:47 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 19:42:47 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 19:42:48 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 19:42:48 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 19:42:49 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:42:49 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:42:49 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 19:42:49 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 19:42:49 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2d84cb86
2025-06-08 19:42:51 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 19:42:51 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 19:42:51 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 19:42:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 19:42:51 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 7.357 seconds (process running for 7.617)
2025-06-08 19:42:51 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 19:42:52 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 19:42:52 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 19:42:52 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 19:42:52 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 19:42:52 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 19:42:57 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 19:42:58 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /live/replay/test],无参数
2025-06-08 19:42:58 [XNIO-1 task-2] INFO  c.y.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /live/replay/test],耗时:[429]毫秒
2025-06-08 20:39:56 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 20:39:56 [main] INFO  com.ydwl.DromaraApplication - Starting DromaraApplication using Java 21.0.5 with PID 3291 (/Users/<USER>/Desktop/yyx-live/yyx-admin/target/classes started by ycyyx in /Users/<USER>/Desktop/yyx-live)
2025-06-08 20:39:56 [main] INFO  com.ydwl.DromaraApplication - The following 1 profile is active: "dev"
2025-06-08 20:40:02 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-08 20:40:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-06-08 20:40:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-08 20:40:03 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@70e8b198
2025-06-08 20:40:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-08 20:40:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-08 20:40:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-06-08 20:40:04 [main] INFO  c.y.common.redis.config.RedisConfig - 初始化 redis 配置
2025-06-08 20:40:04 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-06-08 20:40:05 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-06-08 20:40:05 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-06-08 20:40:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 20:40:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 20:40:07 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: /Users/<USER>/snailJob/worker
2025-06-08 20:40:08 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-06-08 20:40:08 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@99cb7c83
2025-06-08 20:40:15 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-06-08 20:40:15 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-06-08 20:40:15 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-08 20:40:15 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-06-08 20:40:15 [main] INFO  com.ydwl.DromaraApplication - Started DromaraApplication in 19.853 seconds (process running for 25.683)
2025-06-08 20:40:15 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE订阅监听器成功
2025-06-08 20:40:15 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.5.0
2025-06-08 20:40:20 [main] INFO  c.a.s.c.c.r.c.g.SnailJobGrpcClient - grpc client started connect to server
2025-06-08 20:40:21 [main] INFO  c.a.s.c.c.rpc.server.SnailGrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.client.common.rpc.server.SnailGrpcServer, port = 28080
2025-06-08 20:40:21 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.5.0
2025-06-08 20:40:21 [main] INFO  c.y.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.5.0
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.5.0
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-06-08 20:43:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
