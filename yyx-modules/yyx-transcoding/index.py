#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FC函数转码服务 
使用与MTS相同的接口格式，实现统一调用方式
"""

from __future__ import annotations

import logging
import oss2
import os
import json
import subprocess
import shutil
import math
import requests
import threading
import time
import concurrent.futures
import aiohttp
from functools import partial
from typing import Dict, Any, Optional, Tuple, List, Union, Callable
from dataclasses import dataclass
from pathlib import Path
import asyncio
import tempfile
from contextlib import asynccontextmanager

logging.getLogger("oss2.api").setLevel(logging.ERROR)
logging.getLogger("oss2.auth").setLevel(logging.ERROR)
LOGGER = logging.getLogger()

@dataclass
class UniversalResolutionInfo:
    """统一分辨率信息格式"""
    width: int
    height: int
    playlist_url: str
    bandwidth: int
    template_id: str = ""
    object_name: str = ""
    state: str = "Success"
    duration: str = "0"
    filesize: str = "0"
    fps: str = "30"
    bitrate: str = "0"

    def to_mts_output_dict(self) -> Dict[str, Any]:
        """转换为MTS标准输出格式"""
        return {
            "ObjectName": self.object_name or self.playlist_url,
            "Bucket": "",  # 将在外部设置
            "Location": "oss-cn-beijing",  # 将在外部设置
            "TemplateId": self.template_id,
            "State": self.state,
            "Code": "" if self.state == "Success" else "TranscodeError",
            "Message": "" if self.state == "Success" else "转码失败",
            "Width": str(self.width),
            "Height": str(self.height),
            "Duration": self.duration,
            "Filesize": self.filesize,
            "Bitrate": self.bitrate,
            "Fps": self.fps
        }

@dataclass
class UniversalProcessResult:
    """统一处理结果格式"""
    status: str
    output_path: str
    master_playlist: str
    master_playlist_url: str
    resolutions: Dict[str, UniversalResolutionInfo]
    biz_id: str
    message: Optional[str] = None
    emergency_mode: bool = False
    
    def to_mts_response_dict(self, bucket: str, location: str = "oss-cn-beijing") -> Dict[str, Any]:
        """转换为MTS标准响应格式"""
        outputs = []
        for resolution_key, resolution_info in self.resolutions.items():
            output = resolution_info.to_mts_output_dict()
            output["Bucket"] = bucket
            output["Location"] = location
            outputs.append(output)
        
        return {
            "RequestId": self.biz_id,
            "JobResult": {
                "Success": self.status == "success",
                "JobId": self.biz_id,
                "State": "Success" if self.status == "success" else "Fail",
                "UserData": self.biz_id,
                "Result": {
                    "status": self.status,
                    "output_path": self.output_path,
                    "master_playlist": self.master_playlist,
                    "master_playlist_url": self.master_playlist_url,
                    "resolutions": {k: v.__dict__ for k, v in self.resolutions.items()},
                    "emergency_mode": self.emergency_mode
                },
                "Outputs": outputs
            }
        }

def get_fileNameExt(filename: str) -> Tuple[str, str]:
    """获取文件名和扩展名"""
    path = Path(filename)
    return path.stem, path.suffix

def run_ffmpeg_command(cmd: List[str], request_id: str) -> Tuple[str, str]:
    """运行 FFmpeg 命令"""
    try:
        # 在命令开头添加日志级别设置
        if cmd[0] == 'ffmpeg':
            cmd = ['ffmpeg', '-loglevel', 'error'] + cmd[1:]
        elif cmd[0] == 'ffprobe':
            cmd = ['ffprobe', '-loglevel', 'error'] + cmd[1:]
            
        process = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        return process.stdout, process.stderr
    except subprocess.CalledProcessError as e:
        LOGGER.error(f"FFmpeg 命令执行失败: {e.stderr}")
        raise Exception(f"FFmpeg 命令执行失败: {e.stderr}")
    except Exception as e:
        LOGGER.error(f"执行 FFmpeg 命令时发生错误: {str(e)}")
        raise

async def send_callback(callback_url: str, result: Dict[str, Any], max_retries: int = 3) -> bool:
    """发送回调，使用指数退避重试机制"""
    if not callback_url:
        return True
        
    for attempt in range(max_retries):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(callback_url, json=result, timeout=10) as response:
                    response.raise_for_status()
                    return True
        except Exception as e:
            if attempt == max_retries - 1:
                raise ConnectionError(f"Failed to send callback after {max_retries} attempts: {str(e)}")
            await asyncio.sleep(2 ** attempt)
    return False

async def test_oss_connection(oss_client: oss2.Bucket, output_dir: str, max_retries: int = 3) -> bool:
    """测试OSS连接，使用指数退避重试机制"""
    for attempt in range(max_retries):
        try:
            result = oss_client.list_objects(prefix=output_dir, max_keys=1)
            return True
        except Exception as e:
            if attempt == max_retries - 1:
                raise ConnectionError(f"OSS client connection failed after {max_retries} attempts: {str(e)}")
            await asyncio.sleep(2 ** attempt)
    return False

async def download_file(url: str, local_path: Path, max_retries: int = 3) -> bool:
    """下载文件，使用指数退避重试机制"""
    for attempt in range(max_retries):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=30) as response:
                    response.raise_for_status()
                    total_size = int(response.headers.get('content-length', 0))
                    
                    with open(local_path, 'wb') as f:
                        downloaded = 0
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                            downloaded += len(chunk)
                            if total_size > 0 and downloaded % (total_size // 10) == 0:
                                LOGGER.info(f"Download progress: {(downloaded / total_size) * 100:.0f}%")

            return True
        except Exception as e:
            if attempt == max_retries - 1:
                raise ConnectionError(f"Failed to download file after {max_retries} attempts: {str(e)}")
            await asyncio.sleep(2 ** attempt)
    return False

def calculate_bitrate(width: int, height: int) -> Tuple[int, int]:
    """计算合适的码率，参考业界标准"""
    # 参考YouTube等平台的推荐码率
    bitrate_map = {
        (854, 480): 1000000,    # 480p: 1Mbps
        (1280, 720): 2500000,   # 720p: 2.5Mbps  
        (1920, 1080): 5000000,  # 1080p: 5Mbps
        (2560, 1440): 9000000,  # 1440p: 9Mbps
        (3840, 2160): 18000000, # 4K: 18Mbps
    }
    
    # 找到最接近的分辨率
    for (w, h), rate in bitrate_map.items():
        if width <= w and height <= h:
            return rate, rate * 2
    
    # 兜底计算
    base_bitrate = width * height * 0.15  # 提高基础码率
    return int(base_bitrate), int(base_bitrate * 2)

def get_optimized_encode_params(width: int, height: int, fps: float, 
                               has_hardware_encoder: bool = False) -> Dict[str, Any]:
    """根据分辨率、帧率和硬件能力优化编码参数"""
    
    # 检测硬件编码器可用性
    if has_hardware_encoder:
        if height <= 720:
            return {
                'codec': 'h264_nvenc',  # NVIDIA硬件编码
                'preset': 'p6',         # 更快的硬件预设
                'tune': 'hq',           # 高质量调优
                'rc': 'vbr',            # 可变码率
                'cq': '24',
                'spatial_aq': '1',
                'temporal_aq': '1'
            }
    
    # 根据分辨率动态调整
    if height <= 480:
        return {
            'preset': 'ultrafast',  # 480p使用最快预设
            'crf': '28',           # 稍高CRF节省时间
            'tune': 'fastdecode'   # 优化解码速度
        }
    elif height <= 720:
        return {
            'preset': 'veryfast',   # 平衡速度和质量
            'crf': '26',
            'tune': 'zerolatency'   # 低延迟调优
        }
    else:
        return {
            'preset': 'faster',     # 高分辨率稍慢但保证质量
            'crf': '24',
            'tune': 'film'          # 电影内容优化
        }

def build_ffmpeg_hls_cmd(input_path: str, resolution_dir: Path,
                           target_width: int, target_height: int,
                           maxrate: int, bufsize: int, segment_time: int,
                           original_fps: float = 30.0) -> List[str]:
    """构建直接输出HLS的FFmpeg命令"""
    encode_params = get_optimized_encode_params(target_width, target_height, original_fps)

    output_m3u8_path = resolution_dir / 'index.m3u8'
    # HLS分段文件名模式，相对于M3U8文件的位置
    output_segment_filename_pattern = 'segment_%03d.ts'

    cmd = [
        'ffmpeg', '-y', # run_ffmpeg_command 会自动添加 -loglevel error
        '-hwaccel', 'auto', # 自动硬件解码加速
        '-i', input_path,
        '-map', '0:v:0', '-map', '0:a:0?', # 映射主视频流和可选的音频流
    ]

    # 视频缩放和编码参数
    cmd.extend(['-vf', f'scale={target_width}:{target_height}:flags=lanczos']) # Lanczos缩放以获得较好质量
    cmd.extend([
        '-c:v', encode_params.get('codec', 'libx264'), # 视频编码器
        '-preset', encode_params['preset'],             # 编码预设
        '-crf', encode_params['crf'],                   # 固定质量因子
        '-maxrate', str(maxrate),                       # 最大码率
        '-bufsize', str(bufsize),                       # 码率控制器缓冲区大小
        '-g', str(int(segment_time * original_fps)),    # GOP大小（关键帧间隔）
        '-keyint_min', str(int(segment_time * original_fps)), # 最小关键帧间隔
        '-sc_threshold', '0',                           # 关闭基于场景变化的自动关键帧插入
    ])

    # 音频编码参数
    cmd.extend([
        '-c:a', 'aac', # 音频编码器
        '-b:a', '128k' if target_height >= 720 else '96k', # 音频码率
        '-ac', '2',     # 双声道
        '-ar', '48000', # 音频采样率
    ])

    # HLS输出选项
    cmd.extend([
        '-f', 'hls',                                    # 输出格式为HLS
        '-hls_time', str(segment_time),                 # 每个分片的目标时长
        '-hls_list_size', '0',                          # 播放列表中的最大分片数 (0表示保留所有分片)
        '-hls_playlist_type', 'vod',                    # 播放列表类型 (vod表示点播，会包含#EXT-X-ENDLIST)
        '-hls_segment_filename', str(resolution_dir / output_segment_filename_pattern), # TS分片文件路径和模式
        '-max_muxing_queue_size', '1024',               # HLS复用器队列大小，参考原分割命令
    ])

    # 性能优化和输出文件
    cmd.extend([
        '-threads', '0',                # 自动检测并使用所有CPU核心
        '-thread_type', 'frame+slice',  # 帧和切片并行处理，有助于某些编码器
        str(output_m3u8_path)           # M3U8播放列表输出路径
    ])
    return cmd

async def universal_process_video(
    request_id: str,
    oss_client: oss2.Bucket,
    input_path: str,
    shortname: str,
    output_dir: str,
    resolutions: Dict[str, Union[bool, str]],
    segment_time: int,
    callback_url: Optional[str],
    check_timeout: Optional[Callable[[], bool]],
    biz_id: str
) -> UniversalProcessResult:
    """
    统一视频处理函数
    与原始process_video功能相同，但返回统一格式结果
    """
    try:
        LOGGER.info(f"开始统一视频处理: {shortname}, BizId: {biz_id}")
        LOGGER.info(f"分辨率配置: {resolutions}")

        # 验证输入参数
        if not input_path or not output_dir or not resolutions:
            raise ValueError("缺少必要的输入参数")

        # 验证OSS客户端
        await test_oss_connection(oss_client, output_dir)

        # 验证输入文件是否存在
        try:
            object_key = f"{output_dir}/{shortname}.mp4"
            if not oss_client.object_exists(object_key):
                raise FileNotFoundError(f"源文件不存在: {object_key}")
        except Exception as e:
            LOGGER.error(f"源文件检查失败: {str(e)}")
            raise

        # 执行视频处理（复用原有的handle_hls逻辑）
        result = await handle_universal_hls(
            request_id, oss_client, input_path, shortname, 
            output_dir, resolutions, segment_time, check_timeout, biz_id
        )
        
        LOGGER.info(f"统一视频处理完成: {shortname}, BizId: {biz_id}")

        return result

    except Exception as e:
        error_message = str(e)
        LOGGER.error(f"统一视频处理失败: {error_message}, BizId: {biz_id}")

        raise

async def handle_universal_hls(
    request_id: str,
    oss_client: oss2.Bucket,
    input_path: str,
    shortname: str,
    output_dir: str,
    resolutions: Dict[str, Union[bool, str]],
    segment_time: int,
    check_timeout: Optional[Callable[[], bool]],
    biz_id: str
) -> UniversalProcessResult:
    """
    统一HLS处理函数
    基于原有handle_hls，但返回统一格式
    """
    start_time = time.time()

    try:
        with tempfile.TemporaryDirectory(prefix="transcode_universal_") as tmp_dir_name:
            ts_dir = Path(tmp_dir_name)
            master_playlist = ts_dir / f'{shortname}.m3u8'
            local_input_path = ts_dir / f'{shortname}.mp4'
            local_input_path_str = str(local_input_path)

            master_playlist.write_text('#EXTM3U\n#EXT-X-VERSION:3\n')

            resolution_info: Dict[str, UniversalResolutionInfo] = {}
            is_timeout = False

            # 下载视频文件
            LOGGER.info("开始下载视频文件")
            await download_file(input_path, local_input_path)
            
            if check_timeout and check_timeout():
                return UniversalProcessResult(
                    status="partial",
                    message="下载超时",
                    output_path=output_dir,
                    master_playlist="",
                    master_playlist_url="",
                    resolutions={},
                    biz_id=biz_id
                )

            if not local_input_path.exists() or local_input_path.stat().st_size == 0:
                raise Exception("下载的文件无效")

            # 获取视频信息
            probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0',
                        '-show_entries', 'stream=width,height,r_frame_rate,duration', '-of', 'json', local_input_path_str]
            stdout, _ = run_ffmpeg_command(probe_cmd, request_id)
            video_info = json.loads(stdout)
            original_width = int(video_info['streams'][0]['width'])
            original_height = int(video_info['streams'][0]['height'])
            
            # 解析帧率
            r_frame_rate_str = video_info['streams'][0].get('r_frame_rate', "30/1")
            if '/' in r_frame_rate_str:
                num, den = map(int, r_frame_rate_str.split('/'))
                original_fps = float(num / den) if den != 0 else 30.0
            else:
                original_fps = float(r_frame_rate_str)
            
            video_duration_str = video_info['streams'][0].get('duration', "0")
            try:
                video_duration_float = float(video_duration_str)
            except ValueError:
                video_duration_float = 0.0
            video_duration_formatted = f"{video_duration_float:.3f}"

            LOGGER.info(f"原始分辨率: {original_width}x{original_height}, 帧率: {original_fps:.2f}fps, 时长: {video_duration_formatted}s")
            
            if check_timeout and check_timeout():
                return UniversalProcessResult(
                    status="partial",
                    message="获取视频信息超时",
                    output_path=output_dir,
                    master_playlist="",
                    master_playlist_url="",
                    resolutions={},
                    biz_id=biz_id
                )
            
            # 获取需要处理的分辨率列表
            resolutions_to_process = [
                resolution for resolution, enabled in resolutions.items()
                if enabled == "true" or enabled is True
            ]
            resolutions_to_process.sort(key=lambda x: int(x.lower().replace('p', '')))
            
            async def process_universal_resolution(resolution: str, orig_width: int, orig_height: int, 
                                                   input_file_path: str, actual_fps: float, actual_duration: str) -> Tuple[str, Optional[UniversalResolutionInfo], bool]:
                try:
                    if check_timeout and check_timeout():
                        return resolution, None, True
                    
                    resolution_value = resolution.lower().replace('p', '')
                    resolution_dir = ts_dir / f'{resolution_value}p'
                    resolution_dir.mkdir(exist_ok=True)

                    target_height = int(resolution_value)
                    target_width = int((target_height / orig_height) * orig_width)
                    target_width = math.ceil(target_width / 2) * 2
                    LOGGER.info(f"处理分辨率: {target_width}x{target_height}")

                    # 计算码率和缓冲区大小
                    maxrate, bufsize = calculate_bitrate(target_width, target_height)
                    LOGGER.info(f"分辨率 {target_width}x{target_height} 的码率设置: maxrate={maxrate/1000}kbps, bufsize={bufsize/1000}kbps")

                    # 构建转码命令 (修改为直接输出HLS)
                    cmd = build_ffmpeg_hls_cmd(input_file_path, resolution_dir,
                                               target_width, target_height, maxrate, bufsize, segment_time, actual_fps)
                    
                    run_ffmpeg_command(cmd, request_id)
                    
                    if check_timeout and check_timeout():
                        return resolution, None, True

                    # 更新主播放列表
                    with open(master_playlist, 'a') as f:
                        f.write(f'#EXT-X-STREAM-INF:BANDWIDTH={maxrate},RESOLUTION={target_width}x{target_height}\n')
                        f.write(f'{resolution_value}p/index.m3u8\n')

                    # 上传文件并计算总大小
                    LOGGER.info(f"开始上传 {resolution_value}p 分辨率文件")
                    
                    files_to_upload_tuples: List[Tuple[Path, str]] = []
                    current_resolution_total_filesize = 0
                    for filepath_to_upload in resolution_dir.glob('*'):
                        if check_timeout and check_timeout():
                            return resolution, None, True
                        
                        if filepath_to_upload.name.endswith('.ts'):
                            # 对于TS文件，保持原始文件名，但放在分辨率目录下
                            filekey = f"{output_dir}/{resolution_value}p/{filepath_to_upload.name}"
                            current_resolution_total_filesize += filepath_to_upload.stat().st_size
                            files_to_upload_tuples.append((filepath_to_upload, filekey))
                        elif filepath_to_upload.name == 'index.m3u8':
                            # 对于index.m3u8，直接放在分辨率目录下
                            filekey = f"{output_dir}/{resolution_value}p/index.m3u8"
                            files_to_upload_tuples.append((filepath_to_upload, filekey))
                        # 其他文件直接跳过
                    
                    if files_to_upload_tuples:
                        loop = asyncio.get_running_loop()
                        upload_tasks = []
                        for local_path, oss_key in files_to_upload_tuples:
                            # Use partial to pass arguments to put_object_from_file for run_in_executor
                            upload_func = partial(oss_client.put_object_from_file, oss_key, str(local_path))
                            upload_tasks.append(loop.run_in_executor(None, upload_func))
                        
                        try:
                            await asyncio.gather(*upload_tasks)
                            LOGGER.info(f"已并发上传 {len(files_to_upload_tuples)} 个文件 for {resolution_value}p")
                            # Delete local files after successful upload
                            for local_path, _ in files_to_upload_tuples:
                                try:
                                    local_path.unlink()
                                except OSError as e_unlink:
                                    LOGGER.warning(f"删除本地文件 {local_path} 失败: {e_unlink}")
                        except Exception as e_upload:
                            LOGGER.error(f"并发上传文件失败 for {resolution_value}p: {str(e_upload)}")
                            raise # Re-raise to indicate failure for this resolution
                    
                    LOGGER.info(f"{resolution_value}p 文件上传完成, 总大小: {current_resolution_total_filesize} bytes")

                    # 创建统一格式的分辨率信息
                    info = UniversalResolutionInfo(
                        width=target_width,
                        height=target_height,
                        playlist_url=f"{output_dir}/{resolution_value}p/index.m3u8",
                        bandwidth=maxrate, # Use calculated maxrate
                        template_id=f"FC-{resolution_value}p-template",
                        object_name=f"{output_dir}/{resolution_value}p/index.m3u8",
                        state="Success",
                        duration=actual_duration,
                        filesize=str(current_resolution_total_filesize),
                        fps=f"{actual_fps:.2f}",
                        bitrate=str(maxrate // 1000)  # 转换为kbps
                    )

                    return resolution, info, False
                except Exception as e:
                    LOGGER.error(f"处理分辨率 {resolution} 失败: {str(e)}")
                    # Clean up intermediate file if it exists on error - No longer needed as there's no single intermediate file
                    return resolution, None, False

            # 处理分辨率（保持原有逻辑）
            # 首先处理最低分辨率，以尽快产出可用流
            if not resolutions_to_process:
                 raise Exception("没有配置有效的分辨率进行转码")

            lowest_resolution = resolutions_to_process[0]
            resolution, info, timeout_reached = await process_universal_resolution(
                lowest_resolution, original_width, original_height, local_input_path_str, original_fps, video_duration_formatted
            )
            
            if timeout_reached:
                is_timeout = True
            elif info:
                resolution_info[resolution] = info
                
                remaining_resolutions = resolutions_to_process[1:]
                if remaining_resolutions and not is_timeout:
                    # 并发处理剩余分辨率
                    tasks = []
                    for res_key in remaining_resolutions:
                        task = asyncio.create_task(process_universal_resolution(
                            res_key, original_width, original_height, local_input_path_str, original_fps, video_duration_formatted
                        ))
                        tasks.append(task)
                    
                    if tasks:
                        gathered_results = await asyncio.gather(*tasks, return_exceptions=True)
                        for result_item in gathered_results:
                            if isinstance(result_item, tuple):
                                res_key_processed, info_item, timeout_item = result_item
                                if timeout_item: # Check if any of the parallel tasks timed out
                                    is_timeout = True
                                    LOGGER.warning(f"并发任务 {res_key_processed} 超时。")
                                if info_item:
                                    resolution_info[res_key_processed] = info_item
                                elif not timeout_item: # Log error if not timeout and no info
                                    LOGGER.error(f"并发任务 {res_key_processed} 未返回有效信息且未超时。")
                            elif isinstance(result_item, Exception):
                                LOGGER.error(f"并发处理分辨率时发生错误: {result_item}")
                                # Consider if one failed task should mark overall as partial or fail.
                                # For now, we continue and report what succeeded.

            # 上传主播放列表
            if resolution_info and not is_timeout: # Only upload master if some resolutions succeeded and no overall timeout
                try:
                    master_playlist_key = f"{output_dir}/{shortname}.m3u8"
                    oss_client.put_object_from_file(master_playlist_key, str(master_playlist))
                    LOGGER.info("主播放列表上传完成")
                except Exception as e:
                    LOGGER.error(f"上传主播放列表失败: {str(e)}")
                    # If master playlist fails, the result is still partial/failed
                    # but individual streams might be available.
            elif not resolution_info:
                 LOGGER.warning("没有成功处理任何分辨率，不上传主播放列表。")

        return UniversalProcessResult(
            status="success",
            output_path=output_dir,
            master_playlist=f"{shortname}.m3u8",
            master_playlist_url=f"{output_dir}/{shortname}.m3u8",
            resolutions=resolution_info,
            biz_id=biz_id
        )

    except Exception as exc:
        LOGGER.error(f"处理失败: {str(exc)}")
        raise Exception(f"{request_id} 转码失败: {str(exc)}")
    finally:
        pass # Final cleanup logic is simplified due to TemporaryDirectory

    # 计算总耗时
    total_time = time.time() - start_time
    hours = int(total_time // 3600)
    minutes = int((total_time % 3600) // 60)
    seconds = int(total_time % 60)
    LOGGER.info(f"转码完成，总耗时: {hours}小时{minutes}分钟{seconds}秒")

    if is_timeout:
        return UniversalProcessResult(
            status="partial",
            message="处理超时",
            output_path=output_dir,
            master_playlist=f"{shortname}.m3u8" if resolution_info else "",
            master_playlist_url=f"{output_dir}/{shortname}.m3u8" if resolution_info else "",
            resolutions=resolution_info,
            biz_id=biz_id
        )
    else:
        return UniversalProcessResult(
            status="success",
            output_path=output_dir,
            master_playlist=f"{shortname}.m3u8",
            master_playlist_url=f"{output_dir}/{shortname}.m3u8",
            resolutions=resolution_info,
            biz_id=biz_id
        )

def build_universal_mts_callback(event_type: str, job_id: str, user_data: str, 
                                message: str = None, input_info: Dict = None, 
                                outputs: List[Dict] = None) -> Dict[str, Any]:
    """构建统一的MTS标准回调数据格式"""
    callback_data = {
        "Type": "Report",
        "EventType": event_type,  # Transcoding/TranscodeSuccess/TranscodeFail
        "JobId": job_id,
        "RequestId": job_id,
        "UserData": user_data,
        "State": "Success" if event_type == "TranscodeSuccess" else ("Fail" if event_type == "TranscodeFail" else "Transcoding"),
        "Code": "" if event_type != "TranscodeFail" else "FCTranscodeError",
        "Message": message or ("FC转码成功" if event_type == "TranscodeSuccess" else 
                              ("FC转码失败" if event_type == "TranscodeFail" else "FC转码进行中")),
        "TranscodeMethod": "fc"  # 标识转码方式
    }
    
    if input_info:
        callback_data["Input"] = input_info
        
    if outputs:
        callback_data["Outputs"] = outputs
        
    return callback_data

def build_universal_mts_response(status: str, request_id: str, user_data: str, 
                                data: Dict = None, error: str = None) -> Dict[str, Any]:
    """构建统一的MTS标准响应数据格式"""
    if status == "success":
        response = {
            "RequestId": request_id,
            "JobResult": {
                "Success": True,
                "JobId": request_id,
                "State": "Success",
                "UserData": user_data,
                "Result": data,
                "TranscodeMethod": "fc"  # 标识转码方式
            }
        }
    else:
        response = {
            "RequestId": request_id,
            "JobResult": {
                "Success": False,
                "JobId": request_id,
                "State": "Fail",
                "UserData": user_data,
                "Code": "FCTranscodeError",
                "Message": error or "FC转码失败",
                "TranscodeMethod": "fc"  # 标识转码方式
            }
        }
    
    return response

def convert_to_universal_mts_outputs(resolutions: Dict[str, UniversalResolutionInfo], 
                                   output_dir: str, bucket: str, 
                                   location: str) -> List[Dict[str, Any]]:
    """将分辨率信息转换为统一的MTS格式输出列表"""
    outputs = []
    
    for resolution_key, resolution_info in resolutions.items():
        # 生成模板ID
        template_id = f"FC-{resolution_key}-template"
        if "480" in resolution_key:
            template_id = "FC-480p-template"
        elif "720" in resolution_key:
            template_id = "FC-720p-template"
        elif "1080" in resolution_key:
            template_id = "FC-1080p-template"
        elif "2k" in resolution_key.lower():
            template_id = "FC-2k-template"
        elif "4k" in resolution_key.lower():
            template_id = "FC-4k-template"
        
        # 使用分辨率目录下的index.m3u8作为ObjectName
        resolution_value = resolution_key.lower().replace('p', '')
        object_name = f"{output_dir}/{resolution_value}p/index.m3u8"
        
        output = {
            "ObjectName": object_name,  # 使用分辨率目录下的播放列表
            "Bucket": bucket,
            "Location": location,
            "TemplateId": template_id,
            "State": resolution_info.state,
            "Code": "" if resolution_info.state == "Success" else "FCTranscodeError",
            "Message": "" if resolution_info.state == "Success" else "FC转码失败",
            "Width": str(resolution_info.width),
            "Height": str(resolution_info.height),
            "Duration": resolution_info.duration,
            "Filesize": resolution_info.filesize,
            "Bitrate": resolution_info.bitrate,
            "Fps": resolution_info.fps,
            "TranscodeMethod": "fc"  # 标识转码方式
        }
        outputs.append(output)
    
    return outputs

async def universal_async_handler(event: str, context: Any) -> Dict[str, Any]:
    """
    统一异步处理函数 - 使用MTS标准格式
    完全兼容MTS接口调用方式
    """
    evt = json.loads(event)

    # MTS标准格式参数解析
    input_info = evt["Input"]
    oss_bucket_name = input_info["Bucket"]
    object_key = input_info["Object"]
    output_location = input_info.get("Location", "oss-cn-beijing")
    
    # 从UserData中提取bizId
    user_data = evt.get("UserData", "")
    if not user_data:
        user_data = f"fc-{context.request_id}"
        
    callback_url = evt.get("CallbackUrl", "")
    
    # 从Outputs配置中解析分辨率和模板
    outputs = evt.get("Outputs", [])
    resolutions = {}
    for output in outputs:
        template_id = output.get("TemplateId", "")
        output_object = output.get("OutputObject", "")
        
        # 智能识别分辨率
        if "480" in template_id or "480p" in output_object.lower():
            resolutions["p480"] = True
        elif "720" in template_id or "720p" in output_object.lower():
            resolutions["p720"] = True
        elif "1080" in template_id or "1080p" in output_object.lower():
            resolutions["p1080"] = True
        elif "2k" in template_id.lower() or "2k" in output_object.lower():
            resolutions["p2k"] = True
        elif "4k" in template_id.lower() or "4k" in output_object.lower():
            resolutions["p4k"] = True
    
    # 如果没有从Outputs解析到分辨率，使用默认配置
    if not resolutions:
        resolutions = {"p480": True, "p720": True}
        
    segment_time = evt.get("SegmentTime", 4)
    emergency_mode = evt.get("EmergencyMode", False)
    
    LOGGER.info(f"使用统一MTS标准格式，Input: {input_info}, 解析的分辨率: {resolutions}, UserData: {user_data}")

    start_time = time.time()
    timeout = evt.get("Timeout", 36000)
    
    LOGGER.info(f"开始处理视频: {object_key}, UserData: {user_data}")
    LOGGER.info(f"最终分辨率配置: {resolutions}")

    shortname, _ = get_fileNameExt(object_key)
    
    # 使用环境变量获取凭证
    access_key_id = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_ID')
    access_key_secret = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET')
    security_token = os.environ.get('ALIBABA_CLOUD_SECURITY_TOKEN')
    
    if not all([access_key_id, access_key_secret]):
        raise Exception("缺少OSS认证环境变量")
    
    auth = oss2.StsAuth(access_key_id, access_key_secret, security_token)
    oss_client = oss2.Bucket(auth, 'oss-%s-internal.aliyuncs.com' %
                             context.region, oss_bucket_name)

    if not oss_client.object_exists(object_key):
        raise Exception(f"源文件不存在: {object_key}")

    input_path = oss_client.sign_url('GET', object_key, 6 * 3600)
    output_dir = os.path.dirname(object_key)

    try:
        # 发送开始处理回调（如果配置了）
        if evt.get("SendStartCallback", False) and callback_url:
            try:
                callback_data = build_universal_mts_callback(
                    "Transcoding", 
                    context.request_id, 
                    user_data, 
                    "FC视频处理开始",
                    input_info={
                        "Bucket": oss_bucket_name,
                        "Location": output_location,
                        "Object": object_key
                    }
                )
                await send_callback(callback_url, callback_data)
                LOGGER.info(f"开始处理回调发送成功，UserData: {user_data}")
            except Exception as e:
                LOGGER.error(f"发送开始回调失败: {str(e)}")
        
        # 应急模式处理
        if emergency_mode:
            try:
                LOGGER.info(f"启用应急模式处理，UserData: {user_data}")
                probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0',
                           '-show_entries', 'stream=width,height,bit_rate', '-of', 'json', input_path]
                stdout, _ = run_ffmpeg_command(probe_cmd, context.request_id)
                video_info = json.loads(stdout)
                
                original_width = int(video_info['streams'][0]['width'])
                original_height = int(video_info['streams'][0]['height'])
                bit_rate = video_info['streams'][0].get('bit_rate', str(original_width * original_height * 30))
                
                master_content = '#EXTM3U\n#EXT-X-VERSION:3\n'
                master_content += f'#EXT-X-STREAM-INF:BANDWIDTH={bit_rate},RESOLUTION={original_width}x{original_height}\n'
                master_content += f'{shortname}.mp4\n'
                
                master_playlist_key = os.path.join(output_dir, f'{shortname}.m3u8')
                oss_client.put_object(master_playlist_key, master_content)
                
                resolution_info = {
                    "original": UniversalResolutionInfo(
                        width=original_width,
                        height=original_height,
                        playlist_url=object_key,
                        bandwidth=int(bit_rate),
                        template_id="FC-original-template",
                        object_name=object_key,
                        state="Success"
                    )
                }
                
                result = UniversalProcessResult(
                    status="success",
                    output_path=output_dir,
                    master_playlist=f"{shortname}.m3u8",
                    master_playlist_url=f"{output_dir}/{shortname}.m3u8",
                    emergency_mode=True,
                    resolutions=resolution_info,
                    biz_id=user_data
                )
                
                if callback_url:
                    callback_data = build_universal_mts_callback(
                        "TranscodeSuccess",
                        context.request_id,
                        user_data,
                        "FC应急模式处理完成",
                        input_info={
                            "Bucket": oss_bucket_name,
                            "Location": output_location, 
                            "Object": object_key
                        },
                        outputs=convert_to_universal_mts_outputs(result.resolutions, output_dir, oss_bucket_name, output_location)
                    )
                    await send_callback(callback_url, callback_data)
                
                return build_universal_mts_response("success", context.request_id, user_data, result.to_mts_response_dict(oss_bucket_name, output_location))
            except Exception as e:
                LOGGER.error(f"应急模式处理失败: {str(e)}")
                # 应急模式失败时，继续正常处理流程
        
        # 超时检查函数
        def check_timeout() -> bool:
            elapsed = time.time() - start_time
            remaining = timeout - elapsed - 5
            if remaining <= 0:
                LOGGER.warning(f"函数即将超时，已运行 {elapsed:.2f} 秒")
                return True
            return False
        
        # 统一转码处理
        result = await universal_process_video(
            context.request_id, oss_client, input_path, shortname, 
            output_dir, resolutions, segment_time, callback_url, 
            check_timeout, user_data
        )
        
        # 构建并发送MTS兼容的回调数据
        if callback_url:
            if result.status == "success":
                callback_data = build_universal_mts_callback(
                    "TranscodeSuccess",
                    context.request_id,
                    user_data,
                    "FC转码处理完成",
                    input_info={
                        "Bucket": oss_bucket_name,
                        "Location": output_location,
                        "Object": object_key
                    },
                    outputs=convert_to_universal_mts_outputs(result.resolutions, output_dir, oss_bucket_name, output_location)
                )
            else:
                callback_data = build_universal_mts_callback(
                    "TranscodeFail",
                    context.request_id,
                    user_data,
                    result.message or "FC转码失败",
                    input_info={
                        "Bucket": oss_bucket_name,
                        "Location": output_location,
                        "Object": object_key
                    }
                )
            
            try:
                await send_callback(callback_url, callback_data)
                LOGGER.info(f"FC转码完成回调发送成功，UserData: {user_data}, 状态: {result.status}")
            except Exception as e:
                LOGGER.error(f"发送完成回调失败: {str(e)}")
        
        return build_universal_mts_response(result.status, context.request_id, user_data, result.to_mts_response_dict(oss_bucket_name, output_location))
        
    except Exception as e:
        error_message = str(e)
        LOGGER.error(f"FC视频处理失败: {error_message}, UserData: {user_data}")
        
        # 发送失败回调
        if callback_url:
            try:
                callback_data = build_universal_mts_callback(
                    "TranscodeFail",
                    context.request_id,
                    user_data,
                    error_message,
                    input_info={
                        "Bucket": oss_bucket_name,
                        "Location": output_location,
                        "Object": object_key
                    }
                )
                await send_callback(callback_url, callback_data)
                LOGGER.info(f"FC错误回调发送成功，UserData: {user_data}")
            except Exception as cb_e:
                LOGGER.error(f"发送错误回调失败: {str(cb_e)}")
            
        return build_universal_mts_response("error", context.request_id, user_data, None, error_message)

def handler(event: str, context: Any) -> Dict[str, Any]:
    """
    统一同步入口函数，用于阿里云函数计算环境
    兼容MTS调用格式
    """
    try:
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 运行异步处理函数
        result = loop.run_until_complete(universal_async_handler(event, context))
        
        # 关闭事件循环
        loop.close()
        
        return result
    except Exception as e:
        error_message = str(e)
        LOGGER.error(f"Error in universal handler: {error_message}")
        LOGGER.error("Stack trace:", exc_info=True)
        try:
            evt = json.loads(event)
            user_data = evt.get("UserData", "")
        except:
            user_data = ""
        return build_universal_mts_response("error", getattr(context, "request_id", ""), user_data, None, error_message)
