package com.ydwl.LiveTranscoding.service;

import com.aliyun.fc20230330.Client;
import com.aliyun.fc20230330.models.InvokeFunctionHeaders;
import com.aliyun.fc20230330.models.InvokeFunctionRequest;
import com.aliyun.fc20230330.models.InvokeFunctionResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.ydwl.LiveTranscoding.domain.dto.FcVideoFcTranscodeDto;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeInitResponseVo;
import com.ydwl.LiveTranscoding.config.AliyunService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * FC函数转码服务
 * 使用MTS标准格式进行视频转码
 */
@Slf4j
@Service
public class FcTranscodeService {

    @Value("${transcoding.fc.template.480p:FC-480p-template}")
    private String template480p;
    @Value("${transcoding.fc.template.720p:FC-720p-template}")
    private String template720p;
    @Value("${transcoding.fc.template.1080p:FC-1080p-template}")
    private String template1080p;
    @Value("${transcoding.fc.template.2k:FC-2k-template}")
    private String template2k;
    @Value("${transcoding.fc.template.4k:FC-4k-template}")
    private String template4k;

    @Value("${transcoding.fc.default-pipeline-id:default-pipeline}")
    private String defaultFcPipelineId;

    @Value("${transcoding.fc.function-name:video-turncode-ydwl-prod}") // 从 TestController.java 中获取的默认值
    private String fcFunctionName;

    /**
     * FC函数转码 - MTS标准格式
     * 
     * @param input MTS标准输入配置
     * @param outputs MTS标准输出配置列表
     * @param userData 用户数据（bizId）
     * @param callbackUrl 回调地址
     * @param pipelineId 管道ID（可选）
     * @return 转码响应
     * @throws Exception 调用异常
     */
    public TranscodeInitResponseVo transcode(
            FcVideoFcTranscodeDto.InputDTO input,
            List<FcVideoFcTranscodeDto.OutputDTO> outputs,
            String userData, 
            String callbackUrl,
            String pipelineId) throws Exception {
        
        log.info("调用FC转码服务，UserData: {}, 输入: {}, 输出数量: {}", 
                userData, input.getObject(), outputs != null ? outputs.size() : 0);

        FcVideoFcTranscodeDto vo = new FcVideoFcTranscodeDto();
        vo.setInput(input);
        vo.setOutputs(outputs);
        vo.setUserData(userData);
        vo.setCallbackUrl(callbackUrl);
        vo.setPipelineId(pipelineId != null ? pipelineId : defaultFcPipelineId);
        vo.setSegmentTime(4);
        vo.setEmergencyMode(false);
        vo.setTimeout(36000);
        vo.setSendStartCallback(true);

        return invokeFcFunction(vo, userData);
    }

    /**
     * 使用模板ID进行转码
     * 
     * @param bucket 输入存储桶
     * @param object 输入文件路径
     * @param userData 用户数据（bizId）
     * @param callbackUrl 回调地址
     * @param templateIds 转码模板ID列表
     * @param segmentTime HLS分片时间
     * @return 转码响应
     * @throws Exception 调用异常
     */
    public TranscodeInitResponseVo transcodeWithTemplates(String bucket, String object, 
                                                             String userData, String callbackUrl,
                                                             List<String> templateIds, Integer segmentTime) throws Exception {
        
        log.info("使用模板进行FC转码，UserData: {}, Bucket: {}, Object: {}, 模板: {}", 
                userData, bucket, object, templateIds);

        FcVideoFcTranscodeDto vo = FcVideoFcTranscodeDto.build(bucket, object, userData, callbackUrl, templateIds, segmentTime);
        
        return invokeFcFunction(vo, userData);
    }

    /**
     * 使用分辨率选择进行转码
     * 
     * @param bucket 输入存储桶
     * @param object 输入文件路径
     * @param userData 用户数据（bizId）
     * @param callbackUrl 回调地址
     * @param enable480p 是否启用480p
     * @param enable720p 是否启用720p
     * @param enable1080p 是否启用1080p
     * @param enable2k 是否启用2K
     * @param enable4k 是否启用4K
     * @param segmentTime HLS分片时间
     * @return 转码响应
     * @throws Exception 调用异常
     */
    public TranscodeInitResponseVo transcodeWithResolutions(String bucket, String object,
                                                               String userData, String callbackUrl,
                                                               boolean enable480p, boolean enable720p, 
                                                               boolean enable1080p, boolean enable2k,
                                                               boolean enable4k, Integer segmentTime) throws Exception {
        
        List<String> templateIds = new java.util.ArrayList<>();
        if (enable480p) templateIds.add(template480p);
        if (enable720p) templateIds.add(template720p);
        if (enable1080p) templateIds.add(template1080p);
        if (enable2k) templateIds.add(template2k);
        if (enable4k) templateIds.add(template4k);
        
        // 如果没有选择任何分辨率，默认使用720p
        if (templateIds.isEmpty()) {
            templateIds.add(template720p);
        }
        
        return transcodeWithTemplates(bucket, object, userData, callbackUrl, templateIds, segmentTime);
    }

    /**
     * 快速转码（使用默认设置）
     * 
     * @param bucket 输入存储桶
     * @param object 输入文件路径
     * @param userData 用户数据（bizId）
     * @param callbackUrl 回调地址
     * @return 转码响应
     * @throws Exception 调用异常
     */
    public TranscodeInitResponseVo quickTranscode(String bucket, String object,
                                                     String userData, String callbackUrl) throws Exception {
        
        // 默认使用720p和1080p
        return transcodeWithResolutions(bucket, object, userData, callbackUrl, false, true, true, false, false, 4);
    }

    /**
     * 智能转码：根据文件大小自动选择转码策略
     * 
     * @param bucket 输入存储桶
     * @param object 输入文件路径
     * @param userData 用户数据（bizId）
     * @param callbackUrl 回调地址
     * @param fileSize 文件大小（字节）
     * @return 转码响应
     * @throws Exception 调用异常
     */
    public TranscodeInitResponseVo smartTranscode(String bucket, String object,
                                                     String userData, String callbackUrl,
                                                     Long fileSize) throws Exception {
        
        log.info("智能转码调用，文件大小: {} bytes", fileSize);

        // 根据文件大小智能选择分辨率
        boolean enable480p = false;
        boolean enable720p = true;  // 720p 是基础配置
        boolean enable1080p = false;
        boolean enable2k = false;
        boolean enable4k = false;

        if (fileSize != null) {
            // 小于100MB：480p + 720p
            if (fileSize < 100 * 1024 * 1024L) {
                enable480p = true;
                enable1080p = false;
            }
            // 100MB-500MB：720p + 1080p
            else if (fileSize < 500 * 1024 * 1024L) {
                enable1080p = true;
            }
            // 500MB-2GB：720p + 1080p + 2K
            else if (fileSize < 2 * 1024 * 1024 * 1024L) {
                enable1080p = true;
                enable2k = true;
            }
            // 大于2GB：全分辨率
            else {
                enable480p = true;
                enable1080p = true;
                enable2k = true;
                enable4k = true;
            }
        }
        
        return transcodeWithResolutions(bucket, object, userData, callbackUrl, 
                                      enable480p, enable720p, enable1080p, enable2k, enable4k, 4);
    }

    /**
     * 执行FC函数调用
     * 
     * @param vo 转码参数
     * @param taskId 任务ID
     * @return 转码响应
     * @throws Exception 调用异常
     */
    private TranscodeInitResponseVo invokeFcFunction(FcVideoFcTranscodeDto vo, String taskId) throws Exception {
        try {
            Client fcClient = AliyunService.createFcClient();
            InvokeFunctionHeaders headers = new InvokeFunctionHeaders();
            headers.xFcInvocationType = "Async"; // 异步调用

            InvokeFunctionRequest invokeFunctionRequest = new InvokeFunctionRequest()
                .setBody(FcVideoFcTranscodeDto.convert(vo));

            InvokeFunctionResponse invokeFunctionResponse = fcClient.invokeFunctionWithOptions(
                fcFunctionName,
                invokeFunctionRequest,
                headers,
                new RuntimeOptions()
            );

            log.info("FC函数调用成功，状态码: {}, TaskId: {}", 
                    invokeFunctionResponse.getStatusCode(), taskId);

            // 构建响应对象
            TranscodeInitResponseVo response = new TranscodeInitResponseVo();
            response.setStatus(1L); // 1表示任务已提交
            response.setBizId(taskId);
            response.setMessage("转码任务已提交，结果请通过回调获取");

            return response;

        } catch (Exception e) {
            log.error("FC函数调用失败，TaskId: {}, 错误: {}", taskId, e.getMessage(), e);
            
            // 构建失败响应
            TranscodeInitResponseVo response = new TranscodeInitResponseVo();
            response.setStatus(-1L); // -1表示任务提交失败
            response.setBizId(taskId);
            response.setMessage("转码任务提交失败: " + e.getMessage());
            
            return response;
        }
    }
}
