package com.ydwl.LiveTranscoding.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 分辨率配置类
 * 用于统一管理转码输出的分辨率设置
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResolutionConfig {
    
    /**
     * 是否启用480p分辨率（854x480）
     * 适用于网络较差的用户
     */
    private boolean enable480p = false;
    
    /**
     * 是否启用720p分辨率（1280x720）
     * 标清画质，默认启用
     */
    private boolean enable720p = true;
    
    /**
     * 是否启用1080p分辨率（1920x1080）
     * 高清画质
     */
    private boolean enable1080p = false;
    
    /**
     * 是否启用2K分辨率（2560x1440）
     * 超高清画质
     */
    private boolean enable2k = false;
    
    /**
     * 是否启用4K分辨率（3840x2160）
     * 极高清画质
     */
    private boolean enable4k = false;
    
    // ========== 便捷方法 ==========
    
    /**
     * 获取启用的分辨率列表
     * 
     * @return 启用的分辨率标识列表
     */
    public List<String> getEnabledResolutions() {
        List<String> resolutions = new ArrayList<>();
        if (enable480p) resolutions.add("480p");
        if (enable720p) resolutions.add("720p");
        if (enable1080p) resolutions.add("1080p");
        if (enable2k) resolutions.add("2k");
        if (enable4k) resolutions.add("4k");
        return resolutions;
    }
    
    /**
     * 检查是否有启用的分辨率
     * 
     * @return true表示至少启用了一种分辨率
     */
    public boolean hasEnabledResolution() {
        return enable480p || enable720p || enable1080p || enable2k || enable4k;
    }
    
    /**
     * 获取启用的分辨率数量
     * 
     * @return 启用的分辨率数量
     */
    public int getEnabledCount() {
        int count = 0;
        if (enable480p) count++;
        if (enable720p) count++;
        if (enable1080p) count++;
        if (enable2k) count++;
        if (enable4k) count++;
        return count;
    }
    
    // ========== 预设配置 ==========
    
    /**
     * 标准配置：720p + 1080p
     * 
     * @return 标准分辨率配置
     */
    public static ResolutionConfig standard() {
        ResolutionConfig config = new ResolutionConfig();
        config.setEnable720p(true);
        config.setEnable1080p(true);
        return config;
    }
    
    /**
     * 基础配置：仅720p
     * 
     * @return 基础分辨率配置
     */
    public static ResolutionConfig basic() {
        ResolutionConfig config = new ResolutionConfig();
        config.setEnable720p(true);
        return config;
    }
    
    /**
     * 高质量配置：720p + 1080p + 2K
     * 
     * @return 高质量分辨率配置
     */
    public static ResolutionConfig high() {
        ResolutionConfig config = new ResolutionConfig();
        config.setEnable720p(true);
        config.setEnable1080p(true);
        config.setEnable2k(true);
        return config;
    }
    
    /**
     * 完整配置：所有分辨率
     * 
     * @return 完整分辨率配置
     */
    public static ResolutionConfig full() {
        ResolutionConfig config = new ResolutionConfig();
        config.setEnable480p(true);
        config.setEnable720p(true);
        config.setEnable1080p(true);
        config.setEnable2k(true);
        config.setEnable4k(true);
        return config;
    }
    
    /**
     * 移动端优化配置：480p + 720p
     * 
     * @return 移动端优化分辨率配置
     */
    public static ResolutionConfig mobile() {
        ResolutionConfig config = new ResolutionConfig();
        config.setEnable480p(true);
        config.setEnable720p(true);
        return config;
    }
    
    /**
     * 智能配置：根据文件大小自动选择
     * 
     * @param fileSize 文件大小（字节）
     * @return 智能分辨率配置
     */
    public static ResolutionConfig smart(Long fileSize) {
        if (fileSize == null) {
            return standard();
        }
        
        ResolutionConfig config = new ResolutionConfig();
        
        if (fileSize < 100 * 1024 * 1024L) { // 小于100MB
            config.setEnable480p(true);
            config.setEnable720p(true);
        } else if (fileSize < 500 * 1024 * 1024L) { // 100MB-500MB
            config.setEnable720p(true);
            config.setEnable1080p(true);
        } else if (fileSize < 2 * 1024 * 1024 * 1024L) { // 500MB-2GB
            config.setEnable720p(true);
            config.setEnable1080p(true);
            config.setEnable2k(true);
        } else { // 大于2GB
            config = full();
        }
        
        return config;
    }
    
    // ========== 校验方法 ==========
    
    /**
     * 校验分辨率配置
     * 
     * @throws IllegalArgumentException 配置无效时抛出异常
     */
    public void validate() {
        if (!hasEnabledResolution()) {
            throw new IllegalArgumentException("必须至少启用一种分辨率");
        }
    }
    
    /**
     * 转换为布尔数组（用于兼容现有接口）
     * 
     * @return [480p, 720p, 1080p, 2k, 4k]
     */
    public boolean[] toBooleanArray() {
        return new boolean[]{enable480p, enable720p, enable1080p, enable2k, enable4k};
    }
    
    /**
     * 从布尔数组创建配置（用于兼容现有接口）
     * 
     * @param resolutions [480p, 720p, 1080p, 2k, 4k]
     * @return 分辨率配置
     */
    public static ResolutionConfig fromBooleanArray(boolean[] resolutions) {
        if (resolutions == null || resolutions.length != 5) {
            throw new IllegalArgumentException("分辨率数组长度必须为5");
        }
        
        ResolutionConfig config = new ResolutionConfig();
        config.setEnable480p(resolutions[0]);
        config.setEnable720p(resolutions[1]);
        config.setEnable1080p(resolutions[2]);
        config.setEnable2k(resolutions[3]);
        config.setEnable4k(resolutions[4]);
        return config;
    }
    
    @Override
    public String toString() {
        return "ResolutionConfig{" +
                "启用的分辨率=" + getEnabledResolutions() +
                ", 总数=" + getEnabledCount() +
                '}';
    }
} 