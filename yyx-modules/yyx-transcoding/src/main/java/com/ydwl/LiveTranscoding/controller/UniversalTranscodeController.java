package com.ydwl.LiveTranscoding.controller;

import com.ydwl.LiveTranscoding.service.IUniversalTranscodeService;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeRequestVo;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeResponseVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 统一转码控制器
 * 提供统一的转码API接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/transcode")
@RequiredArgsConstructor
@Validated
public class UniversalTranscodeController {
    
    private final IUniversalTranscodeService transcodeService;
    
    /**
     * 标准转码接口
     * 
     * @param request 转码请求参数
     * @return 转码响应结果
     */
    @PostMapping("/universal")
    public TranscodeResponseVo transcode(@RequestBody TranscodeRequestVo request) {
        try {
            log.info("收到转码请求，BizId: {}, 文件: {}/{}", 
                    request.getBizId(), request.getBucket(), request.getObject());
            
            TranscodeResponseVo response = transcodeService.transcode(request);
            
            log.info("转码请求处理完成，BizId: {}, 状态: {}", 
                    request.getBizId(), response.getStatusDescription());
            
            return response;
            
        } catch (Exception e) {
            log.error("转码请求处理失败，BizId: {}", request.getBizId(), e);
            return TranscodeResponseVo.failure(request.getBizId(), "unknown", e.getMessage())
                    .withRequest(request);
        }
    }
    
    /**
     * 快速转码接口（使用默认配置）
     * 
     * @param bucket 存储桶名称
     * @param object 文件路径
     * @param bizId 业务ID
     * @param callbackUrl 回调地址（可选，如果不提供将使用配置文件中的默认回调地址）
     * @return 转码响应结果
     */
    @PostMapping("/quick")
    public TranscodeResponseVo quickTranscode(
            @RequestParam String bucket,
            @RequestParam String object,
            @RequestParam String bizId,
            @RequestParam(required = false) String callbackUrl) {
        try {
            log.info("收到快速转码请求，BizId: {}, 文件: {}/{}", bizId, bucket, object);
            
            TranscodeResponseVo response = transcodeService.quickTranscode(bucket, object, bizId, callbackUrl);
            
            log.info("快速转码请求处理完成，BizId: {}, 状态: {}", 
                    bizId, response.getStatusDescription());
            
            return response;
            
        } catch (Exception e) {
            log.error("快速转码请求处理失败，BizId: {}", bizId, e);
            return TranscodeResponseVo.failure(bizId, "unknown", e.getMessage());
        }
    }
    
    /**
     * 智能转码接口（根据文件大小选择方式）
     * 
     * @param bucket 存储桶名称
     * @param object 文件路径
     * @param bizId 业务ID
     * @param callbackUrl 回调地址（可选，如果不提供将使用配置文件中的默认回调地址）
     * @param fileSize 文件大小（字节）
     * @return 转码响应结果
     */
    @PostMapping("/smart")
    public TranscodeResponseVo smartTranscode(
            @RequestParam String bucket,
            @RequestParam String object,
            @RequestParam String bizId,
            @RequestParam(required = false) String callbackUrl,
            @RequestParam(required = false) Long fileSize) {
        try {
            log.info("收到智能转码请求，BizId: {}, 文件: {}/{}, 大小: {} bytes", 
                    bizId, bucket, object, fileSize);
            
            TranscodeResponseVo response = transcodeService.smartTranscode(bucket, object, bizId, callbackUrl, fileSize);
            
            log.info("智能转码请求处理完成，BizId: {}, 状态: {}", 
                    bizId, response.getStatusDescription());
            
            return response;
            
        } catch (Exception e) {
            log.error("智能转码请求处理失败，BizId: {}", bizId, e);
            return TranscodeResponseVo.failure(bizId, "unknown", e.getMessage());
        }
    }
    
    /**
     * 指定方式转码接口
     * 
     * @param request 转码请求参数
     * @param method 转码方式：fc/mts
     * @return 转码响应结果
     */
    @PostMapping("/method/{method}")
    public TranscodeResponseVo transcodeWithMethod(
            @RequestBody TranscodeRequestVo request,
            @PathVariable String method) {
        try {
            log.info("收到指定方式转码请求，方式: {}, BizId: {}, 文件: {}/{}", 
                    method, request.getBizId(), request.getBucket(), request.getObject());
            
            TranscodeResponseVo response = transcodeService.transcodeWithMethod(request, method);
            
            log.info("指定方式转码请求处理完成，方式: {}, BizId: {}, 状态: {}", 
                    method, request.getBizId(), response.getStatusDescription());
            
            return response;
            
        } catch (Exception e) {
            log.error("指定方式转码请求处理失败，方式: {}, BizId: {}", method, request.getBizId(), e);
            return TranscodeResponseVo.failure(request.getBizId(), method, e.getMessage())
                    .withRequest(request);
        }
    }
    
    /**
     * 批量转码接口
     * 
     * @param requests 转码请求列表
     * @return 转码响应列表
     */
    @PostMapping("/batch")
    public List<TranscodeResponseVo> batchTranscode(@RequestBody List<TranscodeRequestVo> requests) {
        try {
            log.info("收到批量转码请求，任务数量: {}", requests.size());
            
            List<TranscodeResponseVo> responses = transcodeService.batchTranscode(requests);
            
            long successCount = responses.stream().mapToLong(r -> r.getStatus() == 1L ? 1L : 0L).sum();
            log.info("批量转码请求处理完成，总数: {}, 成功: {}, 失败: {}", 
                    responses.size(), successCount, responses.size() - successCount);
            
            return responses;
            
        } catch (Exception e) {
            log.error("批量转码请求处理失败", e);
            // 返回错误响应列表
            return requests.stream()
                    .map(req -> TranscodeResponseVo.failure(req.getBizId(), "unknown", e.getMessage()))
                    .toList();
        }
    }
} 