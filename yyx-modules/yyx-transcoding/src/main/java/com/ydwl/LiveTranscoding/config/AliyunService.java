package com.ydwl.LiveTranscoding.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云服务客户端管理类
 * 提供OSS对象存储、函数计算FC和媒体处理MTS服务的客户端实例
 *
 */
@Slf4j
@Service
public class AliyunService {


    static String accessKeyId = "LTAI5tGA15ASheoXwvc7pYcN";
    static String accessKeySecret = "******************************";
    static String bucketName = "video-ydwl";
    static String endpoint = "oss-cn-beijing.aliyuncs.com"; // OSS地域节点
    
    /**
     * <b>description</b> :
     * <p>使用凭据初始化账号Client</p>
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.fc20230330.Client createFcClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
            .setAccessKeyId(accessKeyId)
            .setAccessKeySecret(accessKeySecret);
        config.endpoint = "1542751858224814.cn-beijing.fc.aliyuncs.com";
        return new com.aliyun.fc20230330.Client(config);
    }

    public static com.aliyun.mts20140618.Client createMtsClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
            .setAccessKeyId(accessKeyId)
            .setAccessKeySecret(accessKeySecret);
        config.endpoint = "mts.cn-beijing.aliyuncs.com";
        return new com.aliyun.mts20140618.Client(config);
    }

    public static OSS createOssClient() throws Exception {
        log.info("创建OSS客户端, endpoint={}, bucket={}", endpoint, bucketName);
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }
    
    /**
     * 为OSS对象生成带有过期时间的私有URL
     * 
     * @param objectName 对象名称
     * @param duration 过期时间
     * @return 带签名的URL
     * @throws Exception 如果生成URL失败
     */
    public static String getPrivateUrl(String objectName, Duration duration) throws Exception {
        OSS ossClient = createOssClient();
        try {
            // 设置过期时间
            Date expiration = new Date(System.currentTimeMillis() + duration.toMillis());
            
            // 创建请求对象
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, objectName);
            request.setExpiration(expiration);
            request.setMethod(com.aliyun.oss.HttpMethod.GET);
            
            // 如果是m3u8文件，添加HLS签名处理
            if (objectName.toLowerCase().endsWith(".m3u8")) {
                Map<String, String> queryParams = new HashMap<>();
                queryParams.put("x-oss-process", "hls/sign");
                request.setQueryParameter(queryParams);
            }
            
            // 生成URL
            URL url = ossClient.generatePresignedUrl(request);
            return url.toString();
        } finally {
            ossClient.shutdown();
        }
    }

}
