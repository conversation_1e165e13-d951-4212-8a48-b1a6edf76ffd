package com.ydwl.LiveTranscoding.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * FC转码回调数据
 * 使用MTS标准格式的转码回调数据结构
 * 
 * 回调数据结构：
 * {
 *     "Type": "Report",
 *     "EventType": "TranscodeSuccess",
 *     "JobId": "88c6ca184c0e432bbf5b2ea52ea4****",
 *     "State": "Success",
 *     "Code": "",
 *     "Message": "",
 *     "UserData": "testid-001",
 *     "Input": {
 *         "Bucket": "example-bucket",
 *         "Location": "oss-cn-beijing",
 *         "Object": "video/example.mp4"
 *     },
 *     "Outputs": [
 *         {
 *             "ObjectName": "output/example_480p.m3u8",
 *             "Bucket": "output-bucket",
 *             "Location": "oss-cn-beijing",
 *             "TemplateId": "FC-480p-template",
 *             "State": "Success",
 *             "Width": "854",
 *             "Height": "480",
 *             "Duration": "12.212000",
 *             "Filesize": "123456",
 *             "Bitrate": "800"
 *         }
 *     ]
 * }
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FcTranscodeCallbackResponseDto {
    
    /**
     * 消息类型，固定值"Report"
     */
    @JsonProperty("Type")
    private String type;
    
    /**
     * 事件类型，TranscodeSuccess/TranscodeFail/Transcoding
     */
    @JsonProperty("EventType") 
    private String eventType;
    
    /**
     * 任务ID
     */
    @JsonProperty("JobId")
    private String jobId;
    
    /**
     * 任务状态：Success/Fail/Transcoding
     */
    @JsonProperty("State")
    private String state;
    
    /**
     * 错误码（任务失败时）
     */
    @JsonProperty("Code")
    private String code;
    
    /**
     * 错误信息（任务失败时）
     */
    @JsonProperty("Message")
    private String message;
    
    /**
     * 用户自定义数据（bizId）
     */
    @JsonProperty("UserData")
    private String userData;
    
    /**
     * 输入文件信息
     */
    @JsonProperty("Input")
    private InputDTO input;
    
    /**
     * 输出文件信息列表
     */
    @JsonProperty("Outputs")
    private List<OutputDTO> outputs;

    /**
     * MTS标准输入文件信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InputDTO {
        @JsonProperty("Bucket")
        private String bucket;
        
        @JsonProperty("Location")
        private String location;
        
        @JsonProperty("Object")
        private String object;
    }
    
    /**
     * MTS标准输出文件信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OutputDTO {
        /**
         * 输出文件名
         */
        @JsonProperty("ObjectName")
        private String objectName;
        
        /**
         * 输出存储桶
         */
        @JsonProperty("Bucket")
        private String bucket;
        
        /**
         * 输出位置
         */
        @JsonProperty("Location")
        private String location;
        
        /**
         * 转码模板ID
         */
        @JsonProperty("TemplateId")
        private String templateId;
        
        /**
         * 输出状态
         */
        @JsonProperty("State")
        private String state;
        
        /**
         * 错误码
         */
        @JsonProperty("Code")
        private String code;
        
        /**
         * 错误信息
         */
        @JsonProperty("Message")
        private String message;
        
        /**
         * 视频宽度
         */
        @JsonProperty("Width")
        private String width;
        
        /**
         * 视频高度
         */
        @JsonProperty("Height")
        private String height;
        
        /**
         * 视频时长（秒）
         */
        @JsonProperty("Duration")
        private String duration;
        
        /**
         * 文件大小（字节）
         */
        @JsonProperty("Filesize")
        private String filesize;
        
        /**
         * 视频码率
         */
        @JsonProperty("Bitrate")
        private String bitrate;
        
        /**
         * 视频帧率
         */
        @JsonProperty("Fps")
        private String fps;
    }
    
    /**
     * 获取业务ID
     * 
     * @return userData
     */
    public String getBusinessId() {
        return userData;
    }
    
    /**
     * 获取任务ID
     * 
     * @return jobId
     */
    public String getTaskId() {
        return jobId;
    }
    
    /**
     * 获取转码状态
     * 
     * @return normalized status
     */
    public String getTranscodeStatus() {
        if ("Success".equalsIgnoreCase(state)) { // Use equalsIgnoreCase for robustness
            return "success";
        } else if ("Fail".equalsIgnoreCase(state)) { // Use equalsIgnoreCase for robustness
            return "error";
        } else if ("Transcoding".equalsIgnoreCase(state)) { // Explicitly handle Transcoding state
            return "processing";
        } else {
            return "unknown"; // Or a more specific unknown/other state
        }
    }
    
    /**
     * 获取错误信息
     * 
     * @return error message
     */
    public String getErrorMessage() {
        return message;
    }
    
    /**
     * 获取主播放列表URL
     * (此方法需要根据您的实际输出结构和OSS域名规则来实现)
     * @return master playlist URL or null if not applicable/found
     */
    public String getMasterPlaylistUrl() {
        // 从outputs中查找主播放列表
        // 示例逻辑：假设第一个 .m3u8 文件是主列表，或者您可以根据TemplateId或其他标识
        if (outputs != null) {
            for (OutputDTO output : outputs) {
                if (output.getObjectName() != null && output.getObjectName().endsWith(".m3u8")) {
                    // 您需要构建完整的URL，例如： "https://" + output.getBucket() + "." + ossEndpoint(output.getLocation()) + "/" + output.getObjectName();
                    // ossEndpoint(location) 是一个辅助方法，用于根据location获取OSS域名
                    // 此处返回相对路径或标记为待实现
                    return "oss://" + output.getBucket() + "/" + output.getObjectName(); // Placeholder
                }
            }
        }
        return null;
    }

    /**
     * 检查任务是否成功
     * @return true if state is "Success"
     */
    public boolean isSuccess() {
        return "Success".equalsIgnoreCase(state);
    }

    /**
     * 检查任务是否失败
     * @return true if state is "Fail"
     */
    public boolean isFailed() {
        return "Fail".equalsIgnoreCase(state);
    }

    /**
     * 检查任务是否正在进行中
     * @return true if state is "Transcoding"
     */
    public boolean isProcessing() {
        return "Transcoding".equalsIgnoreCase(state);
    }
} 