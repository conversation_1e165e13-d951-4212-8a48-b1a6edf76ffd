package com.ydwl.LiveTranscoding.controller;

import com.aliyun.fc20230330.Client;
import com.aliyun.fc20230330.models.InvokeFunctionRequest;
import com.aliyun.fc20230330.models.InvokeFunctionResponse;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.Bucket;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.ydwl.LiveTranscoding.config.AliyunService;
import com.ydwl.LiveTranscoding.service.IUniversalTranscodeService;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeRequestVo;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeResponseVo;
import com.ydwl.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 转码测试控制器
 * 展示统一转码服务的使用方法
 *
 * <AUTHOR> Yi
 * @date 2025-05-05
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/manage/")
public class TestController {

    private final IUniversalTranscodeService universalTranscodeService;

    @Value("${transcoding.callback.default-url:https://yd-live.vip.cpolar.cn/live/transcode/callback}")
    private String defaultCallbackUrl;

    @Value("${transcoding.callback.fc-url:${transcoding.callback.default-url}}")
    private String fcCallbackUrl;

    @Value("${transcoding.callback.mts-url:${transcoding.callback.default-url}}")
    private String mtsCallbackUrl;

    /**
     * 测试OSS客户端连接
     */
    @GetMapping("/test")
    public String testClient() throws Exception {
        OSS ossClient = AliyunService.createOssClient();
        List<Bucket> buckets = ossClient.listBuckets();
        System.out.println("OSS buckets: " + buckets);
        ObjectListing objectListing = ossClient.listObjects("video-ydwl");
        for (Bucket bucket : buckets) {
            System.out.println(" - " + bucket.getName());
        }
        for (OSSObjectSummary objectSummary : objectListing.getObjectSummaries()) {
            System.out.println(" - " + objectSummary.getKey() + "  " +
                "(" + objectSummary.getETag() + ")" +
                "  " + objectSummary.getSize() + "  " +
                objectSummary.getLastModified());
        }

        return "Client is ready.";
    }

    /**
     * 测试FC客户端连接
     */
    @GetMapping("/test2")
    public String testClientFc() throws Exception {
        Client fcClient = AliyunService.createFcClient();

        String functionName = "video-turncode-ydwl-prod"; // 函数名
        InputStream body = new InvokeFunctionRequest().body;
        InvokeFunctionRequest invokeFunctionRequest = new InvokeFunctionRequest();
        invokeFunctionRequest.setBody(body);
        InvokeFunctionResponse invokeFunctionResponse = fcClient.invokeFunction(functionName, invokeFunctionRequest);
        // 获取返回结果
        String result = new String(invokeFunctionResponse.getBody().readAllBytes(), StandardCharsets.UTF_8);
        System.out.println("Function response: " + result);
        return "Function invoked successfully. Response: " + result;
    }

    /**
     * 测试统一转码服务 - 快速转码
     */
    @GetMapping("/test-quick-transcode")
    public R<TranscodeResponseVo> testQuickTranscode() throws Exception {
        String bucket = "video-ydwl";
        String object = "videos/u_1865470200001516/202505/v_1865480900001478/raw/f_1865480900002627/2025-05-09-14-52-37_2025-05-09-15-52-37.mp4";
        String bizId = "test-quick-" + System.currentTimeMillis();

        TranscodeResponseVo response = universalTranscodeService.quickTranscode(
            bucket, object, bizId, null
        );

        return R.ok(response);
    }

    /**
     * 测试统一转码服务 - 智能转码
     */
    @GetMapping("/test-smart-transcode")
    public R<TranscodeResponseVo> testSmartTranscode() throws Exception {
        String bucket = "video-ydwl";
        String object = "videos/u_1865470200001516/202505/v_1865480900001478/raw/f_1865480900002627/2025-05-09-14-52-37_2025-05-09-15-52-37.mp4";
        String bizId = "test-smart-" + System.currentTimeMillis();
        Long fileSize = 500 * 1024 * 1024L; // 500MB

        TranscodeResponseVo response = universalTranscodeService.smartTranscode(
            bucket, object, bizId, null, fileSize
        );

        return R.ok(response);
    }

    /**
     * 测试统一转码服务 - 指定FC转码
     */
    @GetMapping("/test-fc-transcode")
    public R<TranscodeResponseVo> testFcTranscode() throws Exception {
        String bucket = "video-ydwl";
        String object = "videos/u_1865470200001516/202505/v_1865480900001478/raw/f_1865480900002627/2025-05-09-14-52-37_2025-05-09-15-52-37.mp4";
        String bizId = "test-fc-" + System.currentTimeMillis();

        TranscodeRequestVo request = TranscodeRequestVo.quick(bucket, object, TranscodeRequestVo.DEFAULT_INPUT_LOCATION, bizId, fcCallbackUrl);
        TranscodeResponseVo response = universalTranscodeService.transcodeWithMethod(request, "fc");

        return R.ok(response);
    }

    /**
     * 测试统一转码服务 - 指定MTS转码
     */
    @GetMapping("/test-mts-transcode")
    public R<TranscodeResponseVo> testMtsTranscode() throws Exception {
        String bucket = "video-ydwl";
        String object = "videos/u_1865470200001516/202505/v_1865480900001478/raw/f_1865480900002627/2025-05-09-14-52-37_2025-05-09-15-52-37.mp4";
        String bizId = "test-mts-" + System.currentTimeMillis();

        TranscodeRequestVo request = TranscodeRequestVo.quick(bucket, object, TranscodeRequestVo.DEFAULT_INPUT_LOCATION, bizId, mtsCallbackUrl);
        TranscodeResponseVo response = universalTranscodeService.transcodeWithMethod(request, "mts");

        return R.ok(response);
    }
}

