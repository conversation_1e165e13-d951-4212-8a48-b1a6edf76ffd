package com.ydwl.LiveTranscoding.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 发起转码后的统一响应对象
 * 替代了原来的MtsOnVideoTranscodeResponseVo和FcOnVideoTranscodeResponseVo
 * 
 * 返回格式:
 * {
 *     "status": 1,      // 1-处理中，2-成功，0-失败
 *     "bizId": "1234567890",  // 业务ID，回调时用于确定转码任务
 *     "jobId": "job-xxxxx",   // 转码任务ID
 *     "method": "fc",         // 转码方式：fc/mts
 *     "message": "转码任务已提交"
 * }
 * 
 * <AUTHOR>
 * @since 2024-06-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TranscodeInitResponseVo {
    
    /**
     * 状态码：1-处理中，2-成功，0-失败
     */
    private Long status;
    
    /**
     * 业务ID
     */
    private String bizId;
    
    /**
     * 转码任务ID
     */
    private String jobId;
    
    /**
     * 转码方式：fc/mts
     */
    private String method;
    
    /**
     * 消息描述
     */
    private String message;
    
    /**
     * 创建处理中响应
     */
    public static TranscodeInitResponseVo processing(String bizId, String jobId, String method) {
        return TranscodeInitResponseVo.builder()
                .status(1L)
                .bizId(bizId)
                .jobId(jobId)
                .method(method)
                .message("转码任务已提交")
                .build();
    }
    
    /**
     * 创建成功响应
     */
    public static TranscodeInitResponseVo success(String bizId, String jobId, String method) {
        return TranscodeInitResponseVo.builder()
                .status(2L)
                .bizId(bizId)
                .jobId(jobId)
                .method(method)
                .message("转码任务已完成")
                .build();
    }
    
    /**
     * 创建失败响应
     */
    public static TranscodeInitResponseVo failure(String bizId, String jobId, String method, String errorMessage) {
        return TranscodeInitResponseVo.builder()
                .status(0L)
                .bizId(bizId)
                .jobId(jobId)
                .method(method)
                .message(errorMessage != null ? errorMessage : "转码任务失败")
                .build();
    }
} 