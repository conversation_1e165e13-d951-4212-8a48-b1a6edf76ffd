package com.ydwl.LiveTranscoding.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 转码输出配置
 * 
 * <AUTHOR>
 * @since 2024-06-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutputConfig {
    /**
     * 输出文件路径
     */
    private String outputObject;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 水印配置列表（可选）
     */
    private String waterMarks;
} 