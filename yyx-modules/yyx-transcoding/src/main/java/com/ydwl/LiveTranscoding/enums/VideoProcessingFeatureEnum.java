package com.ydwl.LiveTranscoding.enums;

public enum VideoProcessingFeatureEnum {
    /**
     * 倍速转码
     */
    BOOST("Boost"),
    /**
     * 普通管道
     */
    STANDARD("Standard"),
    /**
     * 窄带高清
     */
    NARROW_BAND_HD_V2("NarrowBandHDV2"),
    /**
     * 窄带高清
     */
    AI_VIDEO_COVER("AIVideoCover"),
    /**
     * 视频标签
     */
    AI_VIDEO_TAG("AIVideoTag");


    // 枚举对应的字符串值
    private final String value;

    // 构造函数
    VideoProcessingFeatureEnum(String value) {
        this.value = value;
    }

    // 获取枚举对应的字符串值
    public String getValue() {
        return value;
    }

    // 根据字符串值获取对应的枚举实例
    public static VideoProcessingFeatureEnum fromValue(String value) {
        for (VideoProcessingFeatureEnum feature : VideoProcessingFeatureEnum.values()) {
            if (feature.getValue().equalsIgnoreCase(value)) {
                return feature;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + value + "]");
    }

    @Override
    public String toString() {
        return "VideoProcessingFeatureEnum{" +
            "value='" + value + '\'' +
            '}';
    }
}
