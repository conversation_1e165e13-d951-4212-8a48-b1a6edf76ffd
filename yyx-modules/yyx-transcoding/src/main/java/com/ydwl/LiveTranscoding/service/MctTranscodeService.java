package com.ydwl.LiveTranscoding.service;

import com.aliyun.mts20140618.Client;
import com.aliyun.mts20140618.models.SubmitJobsRequest;
import com.aliyun.mts20140618.models.SubmitJobsResponse;
import com.aliyun.mts20140618.models.SubmitJobsResponseBody;
import com.ydwl.LiveTranscoding.domain.dto.MtsInput;
import com.ydwl.LiveTranscoding.domain.dto.MtsTranscodeRequestDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsOutputItemDto;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeInitResponseVo;
import com.ydwl.LiveTranscoding.config.AliyunService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 阿里云媒体转码服务(MTS)处理类
 * 用于处理视频转码、水印添加等媒体处理任务
 */
@Slf4j
@Service
public class MctTranscodeService {

    @Value("${transcoding.mts.default.output-bucket:video-ydwl}")
    private String defaultOutputBucket;

    @Value("${transcoding.mts.default.location:oss-cn-beijing}")
    private String defaultLocation;

    @Value("${transcoding.mts.default.pipeline-id:YOUR_DEFAULT_MTS_PIPELINE_ID}")
    private String defaultPipelineId;

    @Value("${transcoding.mts.default.referer:*}")
    private String defaultReferer;

    // HLS转码模板ID
    private static final String HLS_1080P_TEMPLATE_ID = "752c184e452a41db86714fdf74601e2b"; // 1080p模板
    private static final String HLS_720P_TEMPLATE_ID = "78b4d8f80f5c44a5b23eb07c1f585bb3";  // 720p模板
    private static final String HLS_480P_TEMPLATE_ID = "YOUR_VALID_480P_TEMPLATE_ID_HERE";  // 480p模板

    @Value("${transcoding.fc.template.720p}")
    private String fcTemplate720p;

    @Value("${transcoding.mts.template.hls.720p}")
    private String mtsTemplateHls720p;

    /**
     * 执行MTS视频转码任务
     *
     * @param dto 转码任务参数
     * @return 转码任务响应信息
     * @throws Exception 转码过程中可能发生的异常
     */
    public TranscodeInitResponseVo MtsTranscode(MtsTranscodeRequestDto dto) throws Exception {
        validateTranscodeParams(dto);
        log.info("开始执行MTS转码任务，BizId: {}, InputObject: {}", dto.getBizId(), dto.getInput().getObject());

        Client mtsClient = AliyunService.createMtsClient();
        SubmitJobsRequest submitJobsRequest = new SubmitJobsRequest();

        submitJobsRequest.setOutputBucket(dto.getOutputBucket() != null ? dto.getOutputBucket() : defaultOutputBucket);
        submitJobsRequest.setOutputLocation(dto.getOutputLocation() != null ? dto.getOutputLocation() : defaultLocation);
        submitJobsRequest.setPipelineId(dto.getPipelineId() != null ? dto.getPipelineId() : defaultPipelineId);

        String inputJson = buildInputJson(dto.getInput());
        submitJobsRequest.setInput(inputJson);

        String outputsJson = buildOutputsJson(dto.getOutputs(), dto.getBizId());
        submitJobsRequest.setOutputs(outputsJson);

        if (StringUtils.hasText(dto.getCallbackUrl())) {
            log.warn("MtsTranscodeRequestDto包含callbackUrl，但当前MctTranscodeService未实现作业级别的HTTP回调配置。将依赖管道配置的回调。 URL: {}", dto.getCallbackUrl());
        }

        try {
            SubmitJobsResponse submitJobsResponse = mtsClient.submitJobs(submitJobsRequest);
            SubmitJobsResponseBody body = submitJobsResponse.getBody();
            String requestId = body != null ? body.getRequestId() : "N/A";
            String jobId = "N/A";
            if (body != null && body.getJobResultList() != null && !body.getJobResultList().getJobResult().isEmpty()) {
                jobId = body.getJobResultList().getJobResult().get(0).getJob().getJobId();
            }
            log.info("MTS转码任务提交成功，BizId: {}, MTS JobId: {}, RequestId: {}, 状态码：{}",
                    dto.getBizId(), jobId, requestId, submitJobsResponse.getStatusCode());

            return buildResponse(submitJobsResponse, dto.getBizId());
        } catch (Exception e) {
            log.error("MTS转码任务提交失败，BizId: {}：", dto.getBizId(), e);
            throw new RuntimeException("转码任务提交失败 (BizId: " + dto.getBizId() + "): " + e.getMessage());
        }
    }

    /**
     * 验证转码参数
     *
     * @param dto 转码任务参数
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    private void validateTranscodeParams(MtsTranscodeRequestDto dto) {
        if (dto == null) {
            throw new IllegalArgumentException("转码参数不能为空");
        }
        if (dto.getInput() == null) {
            throw new IllegalArgumentException("输入源配置不能为空");
        }
        if (!StringUtils.hasText(dto.getInput().getObject())) {
            throw new IllegalArgumentException("输入文件路径不能为空");
        }
        if (!StringUtils.hasText(dto.getInput().getBucket())) {
            throw new IllegalArgumentException("输入文件存储桶不能为空");
        }
        if (!StringUtils.hasText(dto.getInput().getLocation())) {
            throw new IllegalArgumentException("输入文件位置不能为空");
        }
        if (!StringUtils.hasText(dto.getBizId())) {
            throw new IllegalArgumentException("业务ID (bizId) 不能为空");
        }
        if (dto.getOutputs() == null || dto.getOutputs().isEmpty()) {
            throw new IllegalArgumentException("输出配置列表不能为空");
        }
        for (MtsOutputItemDto output : dto.getOutputs()) {
            if (!StringUtils.hasText(output.getOutputObject())) {
                throw new IllegalArgumentException("输出配置中的OutputObject不能为空");
            }
            if (!StringUtils.hasText(output.getTemplateId())) {
                throw new IllegalArgumentException("输出配置中的TemplateId不能为空");
            }
        }
    }

    /**
     * 构建输入源JSON配置
     *
     * @param inputDto 输入源参数
     * @return 输入源JSON字符串
     */
    private String buildInputJson(MtsInput inputDto) {
        try {
            Map<String, String> inputMap = new HashMap<>();
            inputMap.put("Bucket", inputDto.getBucket());
            inputMap.put("Location", inputDto.getLocation());
            inputMap.put("Object", inputDto.getObject());
            inputMap.put("Referer", inputDto.getReferer() != null ? inputDto.getReferer() : defaultReferer);
            return JSON.toJSONString(inputMap);
        } catch (Exception e) {
            log.error("构建输入JSON失败：", e);
            throw new RuntimeException("构建输入JSON失败：" + e.getMessage());
        }
    }

    /**
     * 构建HLS格式的输出配置JSON
     *
     * @param jobBizId 转码任务参数
     * @return 输出配置JSON字符串
     */
    private String buildOutputsJson(List<MtsOutputItemDto> outputs, String jobBizId) {
        List<Map<String, Object>> outputList = new ArrayList<>();
        for (MtsOutputItemDto item : outputs) {
            Map<String, Object> outputMap = new HashMap<>();
            outputMap.put("OutputObject", item.getOutputObject());
            outputMap.put("TemplateId", item.getTemplateId());

            outputMap.put("UserData", StringUtils.hasText(item.getUserData()) ? item.getUserData() : jobBizId);

            if (item.getOutputObject() != null && item.getOutputObject().toLowerCase().endsWith(".m3u8")) {
                Map<String, Object> segmentConfig = new HashMap<>();
                segmentConfig.put("SegmentFormat", "ts");
                segmentConfig.put("SegmentDuration", "10");
                outputMap.put("SegmentConfig", segmentConfig);
            }
            outputList.add(outputMap);
        }
        return JSON.toJSONString(outputList);
    }

    /**
     * 构建响应信息
     *
     * @param response MTS转码任务响应
     * @param bizId 业务ID
     * @return 转码任务响应信息
     */
    private TranscodeInitResponseVo buildResponse(SubmitJobsResponse response, String bizId) {
        String jobId = "N/A";
        String message = "转码任务已提交";

        // 从响应中提取jobId和状态信息
        if (response != null && response.getBody() != null) {
            try {
                if (response.getBody().getJobResultList() != null &&
                    !response.getBody().getJobResultList().getJobResult().isEmpty()) {

                    var firstJob = response.getBody().getJobResultList().getJobResult().get(0);
                    if (firstJob.getJob() != null) {
                        jobId = firstJob.getJob().getJobId();

                        if (firstJob.getSuccess()) {
                            message = "转码任务已提交，MTS JobId：" + jobId;
                        } else {
                            message = "转码任务提交可能存在问题，MTS JobId：" + jobId +
                                    ", Code: " + firstJob.getCode() +
                                    ", Message: " + firstJob.getMessage();
                        }
                    } else {
                        message = "转码任务提交响应中未包含有效的Job信息";
                        if (firstJob.getCode() != null) {
                            message += ", Code: " + firstJob.getCode();
                        }
                        if (firstJob.getMessage() != null) {
                            message += ", Message: " + firstJob.getMessage();
                        }
                    }
                } else {
                    message = "转码任务提交成功，但响应数据中JobResultList为空或不存在";
                    if (response.getBody().getRequestId() != null) {
                        message += "，RequestId: " + response.getBody().getRequestId();
                    }
                }
            } catch (Exception e) {
                log.warn("解析MTS响应时发生异常: {}", e.getMessage());
                message = "转码任务提交成功，但解析响应数据时出错: " + e.getMessage();
            }
        } else {
            message = "转码任务提交成功，但响应体为空";
        }

        // 创建响应对象
        TranscodeInitResponseVo responseVo = new TranscodeInitResponseVo();
        responseVo.setStatus(1L); // 1表示任务已提交
        responseVo.setBizId(bizId);
        responseVo.setJobId(jobId);
        responseVo.setMethod("mts");
        responseVo.setMessage(message);

        return responseVo;
    }
}
