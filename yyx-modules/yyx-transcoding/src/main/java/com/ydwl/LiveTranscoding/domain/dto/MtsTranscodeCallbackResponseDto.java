package com.ydwl.LiveTranscoding.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * MTS转码回调数据
 * 
 * MTS转码完成后的回调数据结构：
 * {
 *     "Type": "Report",
 *     "EventType": "JobCompleted", // MTS 通常是 JobCompleted, FC 是 TranscodeSuccess/Fail
 *     "JobId": "88c6ca184c0e432bbf5b2ea52ea4****",
 *     "State": "Success", // Success / Fail
 *     "Code": "",
 *     "Message": "",
 *     "UserData": "testid-001", // 这是作业级别的 UserData
 *     // MTS 的 Input 和 Outputs 结构与FC回调可能不同，或通过MNS消息体包装
 *     // 以下为通用适配，具体字段可能需要根据实际MTS回调消息体调整
 *     "Input": {
 *         "Bucket": "example-bucket",
 *         "Location": "oss-cn-beijing",
 *         "Object": "video/example.mp4"
 *     },
 *     "Outputs": [
 *         {
 *             "OutputObject": "output/example_480p.m3u8", // MTS 可能用 OutputObject
 *             "Bucket": "output-bucket",
 *             "Location": "oss-cn-beijing",
 *             "TemplateId": "S00000001-100020",
 *             // MTS的单个输出可能没有独立的State, Code, Message，这些通常是作业级别的
 *             "Width": "854",
 *             "Height": "480",
 *             "Duration": "12.212000",
 *             "FileSize": "123456", // MTS 可能用 FileSize
 *             "Video": { "Bitrate": "800" }, // MTS 可能有嵌套结构
 *             "UserData": "output-userdata-01" // MTS 输出可以有自己的 UserData
 *         }
 *     ],
 *     "RequestId": "5C89D4C4-B079-411F-A895-9D8DA193****" // MTS 回调通常包含 RequestId
 *     // MediaWorkflowExecution for 媒体工作流回调
 * }
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MtsTranscodeCallbackResponseDto {
    
    @JsonProperty("Type")
    private String type;        // "Report" for FC, can be different for MNS raw message
    
    @JsonProperty("EventType") // FC specific, MTS EventName might be in MNS header or message body
    private String eventType;   // For MTS through MNS, this might be the MNS MessageType or a field in JSON body
                                // e.g. "mts:JobFinished"

    @JsonProperty("EventName") // More common for MTS direct HTTP callback or MNS JSON body
    private String eventName;   // e.g. "TranscodeComplete", "WorkflowFinished"

    @JsonProperty("JobId")
    private String jobId;
    
    @JsonProperty("State")
    private String state;
    
    @JsonProperty("Code")
    private String code;        // Job-level error code

    @JsonProperty("ErrorCode") // MTS sometimes uses ErrorCode
    private String errorCode;
    
    @JsonProperty("Message")
    private String message;     // Job-level error message

    @JsonProperty("ErrorMessage") // MTS sometimes uses ErrorMessage
    private String errorMessage;
    
    @JsonProperty("UserData")
    private String userData;    // Job-level UserData (bizId)
    
    @JsonProperty("Input")
    private InputDTO input;     // Simplified, actual MTS callback might not have this top-level field
    
    @JsonProperty("Outputs")
    private List<OutputDTO> outputs; // Simplified, actual MTS callback might have outputs differently structured or within workflow

    @JsonProperty("RequestId")
    private String requestId;

    @JsonProperty("MediaWorkflowExecution") // For 媒体工作流
    private MediaWorkflowExecutionDTO mediaWorkflowExecution;

    /**
     * 输入文件信息 (Simplified for compatibility)
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InputDTO {
        @JsonProperty("Bucket")
        private String bucket;
        @JsonProperty("Location")
        private String location;
        @JsonProperty("Object")
        private String object;
        // MTS input details might be part of UserData or known beforehand
    }
    
    /**
     * 输出文件信息 (Structure can vary significantly for MTS)
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OutputDTO {
        @JsonProperty("OutputObject")
        private String outputObject;
        @JsonProperty("Bucket")         // May not be present if using OutputBucket from job level
        private String outputBucket;
        @JsonProperty("Location")       // May not be present if using OutputLocation from job level
        private String outputLocation;
        @JsonProperty("TemplateId")
        private String templateId;
        @JsonProperty("UserData")       // MTS output can have its own UserData
        private String userData;

        // Media Info (might be nested or flat depending on MTS/MNS message)
        @JsonProperty("Width")
        private String width;
        @JsonProperty("Height")
        private String height;
        @JsonProperty("Duration")
        private String duration;
        @JsonProperty("FileSize")
        private String fileSize;        // Note: FC uses "Filesize", MTS often "FileSize"
        
        @JsonProperty("VideoStream")
        private VideoStreamDTO videoStream;
        @JsonProperty("AudioStream")
        private AudioStreamDTO audioStream;
        
        // Individual output error (less common, usually job-level errors for MTS)
        @JsonProperty("Code")
        private String errorCode; 
        @JsonProperty("Message")
        private String errorMessage;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VideoStreamDTO {
        @JsonProperty("Bitrate")
        private String bitrate;
        @JsonProperty("Fps")
        private String fps;
        // Add other video stream properties: Codec, Profile, etc.
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AudioStreamDTO {
        @JsonProperty("Bitrate")
        private String bitrate;
        // Add other audio stream properties: Codec, Samplerate, Channels, etc.
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MediaWorkflowExecutionDTO {
        @JsonProperty("WorkflowId")
        private String workflowId;
        @JsonProperty("Name")
        private String name;
        @JsonProperty("State")
        private String state;
        // Potentially contains MediaList with actual output files
        @JsonProperty("MediaList")
        private MediaListDTO mediaList;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MediaListDTO {
        @JsonProperty("Media")
        private List<MediaDTO> media;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MediaDTO {
        @JsonProperty("File")
        private FileDTO file;
        // Other media properties like Width, Height, Duration, Size, Format etc.
        // These might be the actual output files from a workflow.
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileDTO {
        @JsonProperty("Bucket")
        private String bucket;
        @JsonProperty("Location")
        private String location;
        @JsonProperty("Object")
        private String object;
    }

    // Helper methods to normalize access, similar to FcTranscodeCallbackResponseDto
    public String getEffectiveEventName() {
        return eventName != null ? eventName : eventType;
    }

    public String getEffectiveErrorCode() {
        return errorCode != null ? errorCode : code;
    }

    public String getEffectiveErrorMessage() {
        return errorMessage != null ? errorMessage : message;
    }
} 