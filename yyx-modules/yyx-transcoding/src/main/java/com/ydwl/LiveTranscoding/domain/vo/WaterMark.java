package com.ydwl.LiveTranscoding.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 水印配置类
 * 
 * <AUTHOR>
 * @since 2024-06-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WaterMark {
    /**
     * 水印ID
     */
    private String watermarkId;
    
    /**
     * 水印位置
     */
    private String position;
    
    /**
     * 水印类型
     */
    private String type;

    /**
     * 文本水印配置
     */
    private TextWaterMark textWaterMark;

    /**
     * 内部类：文本水印配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TextWaterMark {
        /**
         * 文本内容
         */
        private String content;
        
        /**
         * 字体名称
         */
        private String fontName;
        
        /**
         * 字体大小
         */
        private String fontSize;
        
        /**
         * 上边距
         */
        private Integer top;
        
        /**
         * 左边距
         */
        private Integer left;
    }
} 