package com.ydwl.LiveTranscoding.service;

import com.ydwl.LiveTranscoding.domain.vo.TranscodeRequestVo;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeResponseVo;

import java.util.List;

/**
 * 统一转码服务接口
 * 为FC和MTS转码提供一致的API
 * 
 * 设计目标：
 * 1. 统一调用方式：无论使用FC还是MTS，调用API完全相同
 * 2. 统一参数结构：使用统一的请求/响应对象
 * 3. 智能选择：根据文件大小等因素自动选择最适合的转码方式
 * 4. 易于扩展：便于添加新的转码服务提供商
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
public interface IUniversalTranscodeService {
    
    /**
     * 标准转码接口
     * 根据请求参数智能选择使用FC或MTS进行转码
     * 
     * @param request 转码请求参数
     * @return 转码响应结果
     * @throws Exception 转码调用异常
     */
    TranscodeResponseVo transcode(TranscodeRequestVo request) throws Exception;
    
    /**
     * 快速转码接口（使用默认配置）
     * 默认启用720p和1080p分辨率，使用4秒切片
     * 
     * @param bucket 存储桶名称
     * @param object 文件路径
     * @param bizId 业务ID
     * @param callbackUrl 回调地址
     * @return 转码响应结果
     * @throws Exception 转码调用异常
     */
    TranscodeResponseVo quickTranscode(String bucket, String object, String bizId, String callbackUrl) throws Exception;
    
    /**
     * 智能转码接口（根据文件大小自动选择策略）
     * 根据文件大小智能选择分辨率配置和转码方式：
     * - 小于100MB：480p + 720p，使用FC
     * - 100MB-500MB：720p + 1080p，使用FC
     * - 500MB-2GB：720p + 1080p + 2K，使用MTS
     * - 大于2GB：全分辨率，使用MTS
     * 
     * @param bucket 存储桶名称
     * @param object 文件路径
     * @param bizId 业务ID
     * @param callbackUrl 回调地址
     * @param fileSize 文件大小（字节）
     * @return 转码响应结果
     * @throws Exception 转码调用异常
     */
    TranscodeResponseVo smartTranscode(String bucket, String object, String bizId, String callbackUrl, Long fileSize) throws Exception;
    
    /**
     * 指定转码方式进行转码
     * 强制使用指定的转码方式
     * 
     * @param request 转码请求参数
     * @param method 转码方式：fc/mts
     * @return 转码响应结果
     * @throws Exception 转码调用异常
     */
    TranscodeResponseVo transcodeWithMethod(TranscodeRequestVo request, String method) throws Exception;
    
    /**
     * 批量转码接口
     * 支持一次提交多个文件的转码任务
     * 
     * @param requests 转码请求列表
     * @return 转码响应列表
     * @throws Exception 转码调用异常
     */
    List<TranscodeResponseVo> batchTranscode(List<TranscodeRequestVo> requests) throws Exception;
} 