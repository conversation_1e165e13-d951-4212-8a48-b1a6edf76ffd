package com.ydwl.LiveTranscoding.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeRequestVo;
import com.ydwl.LiveTranscoding.domain.vo.ResolutionConfig;

import java.util.Date;

/**
 * 统一转码响应对象
 * 为FC和MTS转码提供统一的响应结构
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TranscodeResponseVo {
    
    // ========== 基础信息 ==========
    
    /**
     * 状态码
     * 1: 任务提交成功
     * 0: 任务提交中
     * -1: 任务提交失败
     */
    private Long status;
    
    /**
     * 任务ID（用于后续查询和回调识别）
     */
    private String taskId;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 转码方式：fc/mts
     */
    private String method;
    
    // ========== 请求信息 ==========
    
    /**
     * 业务ID（来自请求）
     */
    private String bizId;
    
    /**
     * 存储桶名称
     */
    private String bucket;
    
    /**
     * 文件路径
     */
    private String object;
    
    /**
     * 回调地址
     */
    private String callbackUrl;
    
    // ========== 时间信息 ==========
    
    /**
     * 请求提交时间
     */
    private Date submitTime;
    
    /**
     * 预估完成时间（可选）
     */
    private Date estimatedCompletionTime;
    
    // ========== 转码配置信息 ==========
    
    /**
     * 启用的分辨率配置
     */
    private ResolutionConfig resolutions;
    
    /**
     * 切片时间
     */
    private Integer segmentTime;
    
    // ========== 扩展信息 ==========
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     * 管道ID（MTS使用）
     */
    private String pipelineId;
    
    /**
     * 错误代码（失败时）
     */
    private String errorCode;
    
    /**
     * 详细错误信息（失败时）
     */
    private String errorDetail;
    
    // ========== 便捷构造方法 ==========
    
    /**
     * 创建成功响应
     * 
     * @param taskId 任务ID
     * @param bizId 业务ID
     * @param method 转码方式
     * @param message 响应消息
     * @return 成功响应对象
     */
    public static TranscodeResponseVo success(String taskId, String bizId, String method, String message) {
        TranscodeResponseVo response = new TranscodeResponseVo();
        response.setStatus(1L);
        response.setTaskId(taskId);
        response.setBizId(bizId);
        response.setMethod(method);
        response.setMessage(message != null ? message : "转码任务提交成功");
        response.setSubmitTime(new Date());
        return response;
    }
    
    /**
     * 创建失败响应
     * 
     * @param bizId 业务ID
     * @param method 转码方式
     * @param errorMessage 错误消息
     * @return 失败响应对象
     */
    public static TranscodeResponseVo failure(String bizId, String method, String errorMessage) {
        TranscodeResponseVo response = new TranscodeResponseVo();
        response.setStatus(-1L);
        response.setBizId(bizId);
        response.setMethod(method);
        response.setMessage("转码任务提交失败");
        response.setErrorDetail(errorMessage);
        response.setSubmitTime(new Date());
        return response;
    }
    
    /**
     * 创建失败响应（带错误代码）
     * 
     * @param bizId 业务ID
     * @param method 转码方式
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @return 失败响应对象
     */
    public static TranscodeResponseVo failure(String bizId, String method, String errorCode, String errorMessage) {
        TranscodeResponseVo response = failure(bizId, method, errorMessage);
        response.setErrorCode(errorCode);
        return response;
    }
    
    /**
     * 创建处理中响应
     * 
     * @param taskId 任务ID
     * @param bizId 业务ID
     * @param method 转码方式
     * @return 处理中响应对象
     */
    public static TranscodeResponseVo processing(String taskId, String bizId, String method) {
        TranscodeResponseVo response = new TranscodeResponseVo();
        response.setStatus(0L);
        response.setTaskId(taskId);
        response.setBizId(bizId);
        response.setMethod(method);
        response.setMessage("转码任务处理中");
        response.setSubmitTime(new Date());
        return response;
    }
    
    // ========== 便捷方法 ==========
    
    /**
     * 判断是否成功
     * 
     * @return true表示成功
     */
    public boolean isSuccess() {
        return status != null && status == 1L;
    }
    
    /**
     * 判断是否失败
     * 
     * @return true表示失败
     */
    public boolean isFailure() {
        return status != null && status == -1L;
    }
    
    /**
     * 判断是否处理中
     * 
     * @return true表示处理中
     */
    public boolean isProcessing() {
        return status != null && status == 0L;
    }
    
    /**
     * 获取状态描述
     * 
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知状态";
        }
        switch (status.intValue()) {
            case 1:
                return "提交成功";
            case 0:
                return "处理中";
            case -1:
                return "提交失败";
            default:
                return "未知状态(" + status + ")";
        }
    }
    
    /**
     * 设置请求信息
     * 
     * @param request 转码请求
     * @return 当前对象（支持链式调用）
     */
    public TranscodeResponseVo withRequest(TranscodeRequestVo request) {
        if (request != null) {
            this.setBizId(request.getBizId());
            this.setBucket(request.getBucket());
            this.setObject(request.getObject());
            this.setCallbackUrl(request.getCallbackUrl());
            this.setResolutions(request.getResolutions());
            this.setSegmentTime(request.getSegmentTime());
            this.setFileSize(request.getFileSize());
            this.setPriority(request.getPriority());
            this.setPipelineId(request.getPipelineId());
        }
        return this;
    }
    
    /**
     * 设置预估完成时间（例如，当前时间+平均处理时长）
     * 
     * @return 当前对象（支持链式调用）
     */
    public TranscodeResponseVo withEstimatedTime() {
        // 默认预估5分钟，可以根据实际情况调整
        // this.estimatedCompletionTime = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
        // 更复杂的逻辑可以基于文件大小、转码类型等进行预估
        return this;
    }
    
    @Override
    public String toString() {
        return "TranscodeResponseVo{" +
                "status=" + status +
                ", taskId='" + taskId + '\'' +
                ", message='" + message + '\'' +
                ", method='" + method + '\'' +
                ", bizId='" + bizId + '\'' +
                ", bucket='" + bucket + '\'' +
                ", object='" + object + '\'' +
                ", submitTime=" + submitTime +
                '}';
    }
} 