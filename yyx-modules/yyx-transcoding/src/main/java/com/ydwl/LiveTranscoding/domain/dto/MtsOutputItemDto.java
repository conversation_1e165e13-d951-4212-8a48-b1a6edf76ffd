package com.ydwl.LiveTranscoding.domain.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

// import java.util.List; // Future: For Watermarks

/**
 * MTS转码任务的单个输出配置项
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MtsOutputItemDto {
    /**
     * 输出文件路径。OSS Object Key。
     * 例如："output/video_hd.mp4" 或 "output/hls/video_hd/playlist.m3u8"
     */
    private String outputObject;

    /**
     * 转码模板ID。
     */
    private String templateId;

    /**
     * 用户自定义数据，将透传到该输出对应的回调消息中。
     * （注意：这不同于整个转码作业的UserData/bizId）
     */
    private String userData;

    // TODO: 根据需要添加更多MTS输出参数，例如：
    // private String videoBitrate;
    // private String audioBitrate;
    // private String resolution; // WxH format
    // private String container; // e.g., "mp4", "hls"
    // private List<WatermarkDto> waterMarks; // 水印配置
    // private String segmentConfig; // HLS分片配置JSON字符串
} 