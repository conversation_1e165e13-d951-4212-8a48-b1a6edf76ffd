package com.ydwl.LiveTranscoding.domain.dto;

import com.ydwl.LiveTranscoding.domain.dto.MtsInput;
import com.ydwl.LiveTranscoding.domain.dto.MtsOutputItemDto;
import lombok.Data;
import java.util.List;

/**
 * 调用MTS转码的请求参数DTO
 */
@Data
public class MtsTranscodeRequestDto {

    /**
     * 作业输入，JSON格式字符串。
     * 示例值: {"Bucket":"exampleBucket","Location":"oss-cn-hangzhou","Object":"example.flv","Referer":"xxx"}
     *
     * 必填字段说明:
     * - Bucket: 输入文件所在的 OSS Bucket 名称
     * - Location: OSS 地域，如 oss-cn-hangzhou
     * - Object: 输入文件路径及名称
     * - Referer: (可选) OSS 防盗链设置中的 Referer 值
     */
    private MtsInput input;

    /**
     * 业务ID，用于整个转码作业的识别和回调关联。
     * 将作为MTS作业的顶层UserData。
     */
    private String bizId;

    /**
     * 作业输出配置列表。
     * 每个元素定义一个输出流。
     */
    private List<MtsOutputItemDto> outputs;

    /**
     * 管道 ID，用于指定该任务使用的管道。
     * 示例值: dd3dae411e704030b921e52698e5****
     */
    private String pipelineId;

    /**
     * 输出文件所在的 OSS Bucket 名称。
     * 示例值: exampleBucket
     */
    private String outputBucket;

    /**
     * 输出文件所在的 OSS Bucket 的地域（OSS Region）。
     * 示例值: oss-cn-hangzhou
     */
    private String outputLocation;

    /**
     * 回调URL，转码完成后MTS将通知此地址。
     */
    private String callbackUrl;

} 