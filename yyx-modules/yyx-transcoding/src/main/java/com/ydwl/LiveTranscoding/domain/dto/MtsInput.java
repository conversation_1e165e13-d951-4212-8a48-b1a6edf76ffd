package com.ydwl.LiveTranscoding.domain.dto;

import lombok.Data;

/**
 * MTS 转码任务输入源对象
 */
@Data
public class MtsInput {
    /**
     * 输入文件所在的 OSS Bucket 名称
     */
    private String bucket;

    /**
     * OSS 地域，如 oss-cn-hangzhou
     */
    private String location;

    /**
     * 输入文件路径及名称
     */
    private String object;

    /**
     * (可选) OSS 防盗链设置中的 Referer 值
     */
    private String referer;
} 