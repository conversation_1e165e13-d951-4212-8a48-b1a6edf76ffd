package com.ydwl.LiveTranscoding.domain.vo;

import com.ydwl.LiveTranscoding.domain.dto.FcTranscodeCallbackResponseDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsTranscodeCallbackResponseDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 统一转码回调VO
 * 为FC和MTS转码提供统一的回调数据格式
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UniversalTranscodeCallbackVo {
    
    // ========== 基础信息 ==========
    
    /**
     * 回调类型，固定为"Report"
     */
    private String type = "Report";
    
    /**
     * 转码方式：fc/mts
     */
    private String transcodeMethod;
    
    /**
     * 事件类型：Transcoding/TranscodeSuccess/TranscodeFail
     */
    private String eventType;
    
    /**
     * 任务ID
     */
    private String jobId;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 业务ID（用户数据）
     */
    private String bizId;
    
    /**
     * 任务状态：Success/Fail/Transcoding
     */
    private String state;
    
    /**
     * 错误代码（失败时）
     */
    private String code;
    
    /**
     * 消息描述
     */
    private String message;
    
    // ========== 输入输出信息 ==========
    
    /**
     * 输入源信息
     */
    private InputInfo input;
    
    /**
     * 输出结果列表
     */
    private List<OutputInfo> outputs;
    
    /**
     * 输入源信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InputInfo {
        /**
         * 存储桶名称
         */
        private String bucket;
        
        /**
         * 存储区域
         */
        private String location;
        
        /**
         * 文件路径
         */
        private String object;
        
        /**
         * 引用地址
         */
        private String referer;
    }
    
    /**
     * 输出结果信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OutputInfo {
        /**
         * 输出文件名
         */
        private String objectName;
        
        /**
         * 存储桶名称
         */
        private String bucket;
        
        /**
         * 存储区域
         */
        private String location;
        
        /**
         * 模板ID
         */
        private String templateId;
        
        /**
         * 输出状态
         */
        private String state;
        
        /**
         * 错误代码
         */
        private String code;
        
        /**
         * 消息描述
         */
        private String message;
        
        /**
         * 视频宽度
         */
        private String width;
        
        /**
         * 视频高度
         */
        private String height;
        
        /**
         * 视频时长
         */
        private String duration;
        
        /**
         * 文件大小
         */
        private String filesize;
        
        /**
         * 码率
         */
        private String bitrate;
        
        /**
         * 帧率
         */
        private String fps;
        
        /**
         * 转码方式标识
         */
        private String transcodeMethod;
        
        /**
         * 用户自定义数据
         */
        private String userData;
    }
    
    // ========== 便捷方法 ==========
    
    /**
     * 创建成功回调
     */
    public static UniversalTranscodeCallbackVo success(String jobId, String bizId, 
                                                      String transcodeMethod, String message) {
        UniversalTranscodeCallbackVo callback = new UniversalTranscodeCallbackVo();
        callback.setType("Report");
        callback.setEventType("TranscodeSuccess");
        callback.setJobId(jobId);
        callback.setRequestId(jobId); // Consider if requestId should be different or always same as jobId
        callback.setBizId(bizId);
        callback.setState("Success");
        callback.setCode("");
        callback.setMessage(message != null ? message : "转码成功");
        callback.setTranscodeMethod(transcodeMethod);
        return callback;
    }
    
    /**
     * 创建失败回调
     */
    public static UniversalTranscodeCallbackVo failure(String jobId, String bizId, 
                                                      String transcodeMethod, String message) {
        UniversalTranscodeCallbackVo callback = new UniversalTranscodeCallbackVo();
        callback.setType("Report");
        callback.setEventType("TranscodeFail");
        callback.setJobId(jobId);
        callback.setRequestId(jobId);
        callback.setBizId(bizId);
        callback.setState("Fail");
        callback.setCode(transcodeMethod != null ? transcodeMethod.toUpperCase() + "_TRANSCODE_ERROR" : "TRANSCODE_ERROR");
        callback.setMessage(message != null ? message : "转码失败");
        callback.setTranscodeMethod(transcodeMethod);
        return callback;
    }
    
    /**
     * 创建进行中回调
     */
    public static UniversalTranscodeCallbackVo processing(String jobId, String bizId, 
                                                         String transcodeMethod, String message) {
        UniversalTranscodeCallbackVo callback = new UniversalTranscodeCallbackVo();
        callback.setType("Report");
        callback.setEventType("Transcoding");
        callback.setJobId(jobId);
        callback.setRequestId(jobId);
        callback.setBizId(bizId);
        callback.setState("Transcoding");
        callback.setCode("");
        callback.setMessage(message != null ? message : "转码进行中");
        callback.setTranscodeMethod(transcodeMethod);
        return callback;
    }
    
    /**
     * 获取用户自定义数据 (bizId)
     */
    public String getUserData() {
        return this.bizId;
    }

    /**
     * 获取原始状态
     */
    public String getStatus() {
        return this.state;
    }

    /**
     * 获取第一个输出文件的播放地址 (需要具体实现OSS域名拼接逻辑)
     * @return URL or null
     */
    public String getOutputUrl() { 
        if (this.outputs != null && !this.outputs.isEmpty()) {
            OutputInfo firstOutput = this.outputs.get(0);
            // TODO: Implement actual URL construction based on bucket, location, objectName and your OSS domain rules.
            // e.g., return "https://" + firstOutput.getBucket() + "." + ossEndpoint(firstOutput.getLocation()) + "/" + firstOutput.getObjectName();
            return "oss://" + firstOutput.getBucket() + "/" + firstOutput.getObjectName(); // Placeholder
        }
        return null;
    }

    public boolean isSuccess() {
        return "Success".equalsIgnoreCase(this.state);
    }

    public boolean isFailure() {
        return "Fail".equalsIgnoreCase(this.state) || "TranscodeFail".equalsIgnoreCase(this.eventType) ;
    }

    public boolean isProcessing() {
        return "Transcoding".equalsIgnoreCase(this.state);
    }

    public UniversalTranscodeCallbackVo withInput(String bucket, String location, String object) {
        InputInfo inputInfo = new InputInfo();
        inputInfo.setBucket(bucket);
        inputInfo.setLocation(location);
        inputInfo.setObject(object);
        this.setInput(inputInfo);
        return this;
    }

    public UniversalTranscodeCallbackVo withOutput(List<OutputInfo> outputs) {
        this.setOutputs(outputs);
        return this;
    }

    // Adapter methods from specific callback DTOs

    public static UniversalTranscodeCallbackVo fromFcCallback(FcTranscodeCallbackResponseDto fcDto) {
        UniversalTranscodeCallbackVo vo = new UniversalTranscodeCallbackVo();
        vo.setTranscodeMethod("fc");
        vo.setEventType(fcDto.getEventType());
        vo.setJobId(fcDto.getJobId());
        vo.setRequestId(fcDto.getJobId()); // FC JobId often serves as RequestId
        vo.setBizId(fcDto.getUserData());
        vo.setState(fcDto.getState());
        vo.setCode(fcDto.getCode());
        vo.setMessage(fcDto.getMessage());

        if (fcDto.getInput() != null) {
            FcTranscodeCallbackResponseDto.InputDTO fcInput = fcDto.getInput();
            vo.setInput(new InputInfo(fcInput.getBucket(), fcInput.getLocation(), fcInput.getObject(), null));
        }

        if (fcDto.getOutputs() != null) {
            vo.setOutputs(fcDto.getOutputs().stream().map(fcOut -> 
                new OutputInfo(
                    fcOut.getObjectName(), 
                    fcOut.getBucket(), 
                    fcOut.getLocation(), 
                    fcOut.getTemplateId(), 
                    fcOut.getState(), 
                    fcOut.getCode(), 
                    fcOut.getMessage(), 
                    fcOut.getWidth(), 
                    fcOut.getHeight(), 
                    fcOut.getDuration(), 
                    fcOut.getFilesize(), 
                    fcOut.getBitrate(), 
                    fcOut.getFps(), 
                    "fc", // Explicitly set method for output item
                    null // FC output typically doesn't have individual UserData per output, main UserData is job level
                )
            ).collect(Collectors.toList()));
        }
        return vo;
    }

    public static UniversalTranscodeCallbackVo fromMtsCallback(MtsTranscodeCallbackResponseDto mtsDto) {
        UniversalTranscodeCallbackVo vo = new UniversalTranscodeCallbackVo();
        vo.setTranscodeMethod("mts");
        vo.setEventType(mtsDto.getEventName()); // MTS uses EventName
        vo.setJobId(mtsDto.getJobId());
        vo.setRequestId(mtsDto.getRequestId());
        vo.setBizId(mtsDto.getUserData()); // MTS top-level UserData is the BizId
        vo.setState(mtsDto.getState());
        vo.setCode(mtsDto.getErrorCode());
        vo.setMessage(mtsDto.getErrorMessage());
        
        // MTS callback structure is flatter, directly map relevant fields.
        // Input details are usually part of the top-level UserData or not explicitly in callback like FC.
        // For simplicity, if input details are needed, they might need to be fetched or enriched separately.
        // Here, we can try to populate from what's available or leave it null.
        // vo.setInput(new InputInfo(mtsDto.getInputBucket(), mtsDto.getInputLocation(), mtsDto.getInputObject(), null));

        if (mtsDto.getOutputs() != null) {
            vo.setOutputs(mtsDto.getOutputs().stream().map(mtsOut -> {
                OutputInfo outInfo = new OutputInfo();
                outInfo.setObjectName(mtsOut.getOutputObject());
                outInfo.setBucket(mtsOut.getOutputBucket());
                outInfo.setLocation(mtsOut.getOutputLocation());
                outInfo.setTemplateId(mtsOut.getTemplateId());
                outInfo.setState(mtsDto.getState()); // MTS output status is job-level
                outInfo.setCode(mtsDto.getErrorCode()); // Job-level error
                outInfo.setMessage(mtsOut.getErrorMessage()); // Output specific message if any
                outInfo.setWidth(mtsOut.getWidth());
                outInfo.setHeight(mtsOut.getHeight());
                outInfo.setDuration(mtsOut.getDuration());
                outInfo.setFilesize(mtsOut.getFileSize());
                outInfo.setBitrate(mtsOut.getVideoStream() != null ? mtsOut.getVideoStream().getBitrate() : null);
                outInfo.setFps(mtsOut.getVideoStream() != null ? mtsOut.getVideoStream().getFps() : null);
                outInfo.setTranscodeMethod("mts");
                outInfo.setUserData(mtsOut.getUserData()); // MTS output can have its own UserData
                return outInfo;
            }).collect(Collectors.toList()));
        } else if ("Success".equals(mtsDto.getState()) && mtsDto.getMediaWorkflowExecution() != null) {
            // Handle Media Workflow output
            // This part needs specific logic based on how Media Workflow populates output details
            // For now, this is a placeholder
             List<OutputInfo> workflowOutputs = new ArrayList<>();
            // Example: Iterate through mtsDto.getMediaWorkflowExecution().getMediaList().getMedia()
            // and map to OutputInfo
            vo.setOutputs(workflowOutputs);
        }

        return vo;
    }
} 