package com.ydwl.LiveTranscoding.domain.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * FC函数转码参数DTO
 * 使用MTS标准格式，与阿里云媒体转码服务保持一致
 */
@Data
public class FcVideoFcTranscodeDto {
    
    /**
     * MTS标准输入源配置
     * 格式: {"Bucket":"video-bucket","Location":"oss-cn-beijing","Object":"video/test.mp4"}
     */
    @JsonProperty("Input")
    private InputDTO input;
    
    /**
     * MTS标准输出配置列表
     * 格式: [{"OutputObject":"output/test_480p.m3u8","TemplateId":"480p-template"}]
     */
    @JsonProperty("Outputs")
    private List<OutputDTO> outputs;
    
    /**
     * 用户自定义数据，通常包含bizId
     */
    @JsonProperty("UserData")
    private String userData;
    
    /**
     * 管道ID（MTS兼容字段）
     */
    @JsonProperty("PipelineId")
    private String pipelineId;
    
    /**
     * 回调URL
     */
    @JsonProperty("CallbackUrl")
    private String callbackUrl;
    
    /**
     * 转码HLS的分片时间（秒）
     */
    @JsonProperty("SegmentTime")
    private Integer segmentTime;
    
    /**
     * 是否启用应急模式
     */
    @JsonProperty("EmergencyMode")
    private Boolean emergencyMode;
    
    /**
     * 函数超时时间（秒）
     */
    @JsonProperty("Timeout")
    private Integer timeout;
    
    /**
     * 是否发送开始处理回调
     */
    @JsonProperty("SendStartCallback")
    private Boolean sendStartCallback;

    /**
     * MTS标准输入源配置
     */
    @Data
    public static class InputDTO {
        /**
         * 输入文件所在的OSS Bucket名称
         */
        @JsonProperty("Bucket")
        private String bucket;
        
        /**
         * OSS地域，如oss-cn-beijing
         */
        @JsonProperty("Location")
        private String location;
        
        /**
         * 输入文件路径及名称
         */
        @JsonProperty("Object")
        private String object;
        
        /**
         * OSS防盗链设置中的Referer值（可选）
         */
        @JsonProperty("Referer")
        private String referer;
    }
    
    /**
     * MTS标准输出配置
     */
    @Data
    public static class OutputDTO {
        /**
         * 输出文件名
         */
        @JsonProperty("OutputObject")
        private String outputObject;
        
        /**
         * 转码模板ID
         */
        @JsonProperty("TemplateId")
        private String templateId;
        
        /**
         * 用户自定义数据
         */
        @JsonProperty("UserData")
        private String userData;
        
        /**
         * 水印配置（可选）
         */
        @JsonProperty("WaterMarks")
        private List<WaterMarkDTO> waterMarks;
    }
    
    /**
     * 水印配置
     */
    @Data
    public static class WaterMarkDTO {
        /**
         * 水印模板ID
         */
        @JsonProperty("WaterMarkTemplateId")
        private String waterMarkTemplateId;
        
        /**
         * 水印输入文件
         */
        @JsonProperty("InputFile")
        private InputFileDTO inputFile;
    }
    
    /**
     * 水印输入文件
     */
    @Data
    public static class InputFileDTO {
        @JsonProperty("Bucket")
        private String bucket;
        @JsonProperty("Location")
        private String location;
        @JsonProperty("Object")
        private String object;
    }
    
    /**
     * 构建MTS标准格式的转码请求
     * 
     * @param bucket 输入存储桶
     * @param object 输入文件路径
     * @param userData 用户数据（bizId）
     * @param callbackUrl 回调地址
     * @param templateIds 转码模板ID列表
     * @param segmentTime HLS分片时间
     * @return FcVideoFcTranscodeDto
     */
    public static FcVideoFcTranscodeDto build(String bucket, String object, 
                                             String userData, String callbackUrl,
                                             List<String> templateIds, Integer segmentTime) {
        FcVideoFcTranscodeDto dto = new FcVideoFcTranscodeDto();
        
        // 设置输入源
        InputDTO input = new InputDTO();
        input.setBucket(bucket);
        input.setLocation("oss-cn-beijing"); // TODO: Consider making this configurable
        input.setObject(object);
        input.setReferer("*"); // TODO: Consider making this configurable
        dto.setInput(input);
        
        // 设置输出配置
        if (templateIds != null && !templateIds.isEmpty()) {
            List<OutputDTO> outputs = templateIds.stream().map(templateId -> {
                OutputDTO output = new OutputDTO();
                output.setTemplateId(templateId);
                output.setUserData(userData);
                
                // 根据模板ID生成输出文件名
                String resolution = extractResolutionFromTemplate(templateId);
                String outputPath = generateOutputPath(object, resolution);
                output.setOutputObject(outputPath);
                
                return output;
            }).toList();
            dto.setOutputs(outputs);
        }
        
        dto.setUserData(userData);
        dto.setCallbackUrl(callbackUrl);
        dto.setPipelineId("default-pipeline"); // TODO: Consider making this configurable or use injected default
        dto.setSegmentTime(segmentTime != null ? segmentTime : 4);
        dto.setEmergencyMode(false);
        dto.setTimeout(36000);
        dto.setSendStartCallback(true);
        
        return dto;
    }
    
    /**
     * 快速构建转码请求（使用默认模板）
     * 
     * @param bucket 输入存储桶
     * @param object 输入文件路径
     * @param userData 用户数据（bizId）
     * @param callbackUrl 回调地址
     * @param enable480p 是否启用480p
     * @param enable720p 是否启用720p
     * @param enable1080p 是否启用1080p
     * @return FcVideoFcTranscodeDto
     */
    public static FcVideoFcTranscodeDto buildWithResolutions(String bucket, String object,
                                                            String userData, String callbackUrl,
                                                            boolean enable480p, boolean enable720p, 
                                                            boolean enable1080p) {
        List<String> templateIds = new java.util.ArrayList<>();
        // TODO: These template IDs should be injected from config instead of being hardcoded here
        if (enable480p) templateIds.add("FC-480p-template"); 
        if (enable720p) templateIds.add("FC-720p-template");
        if (enable1080p) templateIds.add("FC-1080p-template");
        
        // 如果没有选择任何分辨率，默认使用720p
        if (templateIds.isEmpty()) {
            templateIds.add("FC-720p-template");
        }
        
        return build(bucket, object, userData, callbackUrl, templateIds, 4);
    }
    
    /**
     * 从模板ID中提取分辨率信息
     */
    private static String extractResolutionFromTemplate(String templateId) {
        if (templateId.contains("480")) return "480p";
        if (templateId.contains("720")) return "720p";
        if (templateId.contains("1080")) return "1080p";
        if (templateId.contains("2k")) return "2k";
        if (templateId.contains("4k")) return "4k";
        return "default"; // Fallback
    }
    
    /**
     * 生成输出路径
     */
    private static String generateOutputPath(String inputPath, String resolution) {
        int lastSlash = inputPath.lastIndexOf('/');
        String basePath = (lastSlash == -1) ? "" : inputPath.substring(0, lastSlash + 1);
        String fileName = (lastSlash == -1) ? inputPath : inputPath.substring(lastSlash + 1);
        int dotIndex = fileName.lastIndexOf('.');
        String baseName = (dotIndex == -1) ? fileName : fileName.substring(0, dotIndex);
        return basePath + resolution + "/" + baseName + "_" + resolution + ".m3u8";
    }
    
    public static ByteArrayInputStream convert(FcVideoFcTranscodeDto vo) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(vo);
        return new ByteArrayInputStream(jsonString.getBytes(StandardCharsets.UTF_8));
    }
} 