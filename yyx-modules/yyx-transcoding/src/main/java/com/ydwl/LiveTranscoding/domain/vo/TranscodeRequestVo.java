package com.ydwl.LiveTranscoding.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;
import com.ydwl.LiveTranscoding.domain.vo.ResolutionConfig;

import java.io.Serializable;

/**
 * 统一转码请求对象 (用于FC和MTS转码) 这里写的很牛逼 我弄了好几天
 * 为FC和MTS转码提供统一的参数结构
 * 
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true) // Enables chained setters
public class TranscodeRequestVo implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String DEFAULT_OUTPUT_LOCATION = "oss-cn-beijing";
    public static final String DEFAULT_INPUT_LOCATION = "oss-cn-beijing"; // Added default input location
    public static final String DEFAULT_OUTPUT_BUCKET = "video-ydwl-output"; // Self-contained default

    // ========== 基础参数 ==========
    
    /**
     * 存储桶名称
     */
    private String bucket;
    
    /**
     * 文件路径
     */
    private String object;
    
    /**
     * 输入文件所在OSS区域
     */
    private String inputLocation;
    
    /**
     * 业务ID（用于回调识别）
     */
    private String bizId;
    
    /**
     * 回调地址
     */
    private String callbackUrl;
    
    // ========== 转码配置 ==========
    
    /**
     * 分辨率配置
     */
    @Builder.Default
    private ResolutionConfig resolutions = ResolutionConfig.standard();
    
    /**
     * 切片时间（秒），默认4秒
     */
    @Builder.Default
    private Integer segmentTime = 4;
    
    /**
     * 管道ID（可选，用于MTS）
     */
    private String pipelineId;
    
    /**
     * 强制指定转码方式：fc/mts/auto
     * auto表示自动选择
     */
    @Builder.Default
    private String forceMethod = "auto";
    
    // ========== 输出配置 ==========
    
    /**
     * 输出存储桶（可选，默认使用输入存储桶）
     */
    private String outputBucket;
    
    /**
     * 输出位置（可选，默认oss-cn-beijing）
     */
    @Builder.Default
    private String outputLocation = DEFAULT_OUTPUT_LOCATION;
    
    // ========== 高级配置 ==========
    
    /**
     * 是否启用紧急模式（仅FC支持）
     */
    @Builder.Default
    private Boolean emergencyMode = false;
    
    /**
     * 超时时间（秒），默认36000秒（10小时）
     */
    @Builder.Default
    private Integer timeout = 36000;
    
    /**
     * 是否发送开始处理回调
     */
    @Builder.Default
    private Boolean sendStartCallback = true;
    
    /**
     * 文件大小（字节），用于智能选择转码策略
     */
    private Long fileSize;
    
    /**
     * 优先级（1-10，数字越大优先级越高）
     */
    @Builder.Default
    private Integer priority = 5;
    
    // ========== 便捷构造方法 ==========
    
    /**
     * 快速创建转码请求
     * 
     * @param bucket 存储桶
     * @param object 文件路径
     * @param inputLocation 输入文件所在OSS区域
     * @param bizId 业务ID
     * @param callbackUrl 回调地址
     * @return 转码请求对象
     */
    public static TranscodeRequestVo quick(String bucket, String object, String inputLocation, String bizId, String callbackUrl) {
        return TranscodeRequestVo.builder()
            .bucket(bucket)
            .object(object)
            .inputLocation(StringUtils.hasText(inputLocation) ? inputLocation : DEFAULT_INPUT_LOCATION)
            .bizId(bizId)
            .callbackUrl(callbackUrl)
            .resolutions(ResolutionConfig.standard())
            .outputLocation(DEFAULT_OUTPUT_LOCATION)
            .forceMethod("auto")
            .build();
    }
    
    /**
     * 创建智能转码请求
     * 
     * @param bucket 存储桶
     * @param object 文件路径
     * @param inputLocation 输入文件所在OSS区域
     * @param bizId 业务ID
     * @param callbackUrl 回调地址
     * @param fileSize 文件大小
     * @return 转码请求对象
     */
    public static TranscodeRequestVo smart(String bucket, String object, String inputLocation, String bizId, String callbackUrl, Long fileSize) {
        TranscodeRequestVo request = quick(bucket, object, inputLocation, bizId, callbackUrl);
        request.setFileSize(fileSize);
        if (fileSize != null) {
            request.setResolutions(ResolutionConfig.smart(fileSize));
        }
        return request;
    }
    
    // ========== 校验方法 ==========
    
    /**
     * 校验请求参数
     * 
     * @throws IllegalArgumentException 参数校验失败
     */
    public void validate() {
        if (!StringUtils.hasText(bucket)) throw new IllegalArgumentException("输入Bucket不能为空");
        if (!StringUtils.hasText(object)) throw new IllegalArgumentException("输入Object Key不能为空");
        if (!StringUtils.hasText(inputLocation)) throw new IllegalArgumentException("输入InputLocation不能为空");
        if (!StringUtils.hasText(bizId)) throw new IllegalArgumentException("业务ID (bizId) 不能为空");
        if (resolutions == null || !resolutions.hasEnabledResolution()) {
            throw new IllegalArgumentException("必须至少启用一种转码分辨率");
        }
        if (isForceMethod() && !("fc".equalsIgnoreCase(forceMethod) || "mts".equalsIgnoreCase(forceMethod) || "auto".equalsIgnoreCase(forceMethod))) { // 修正：auto也应是有效值
            throw new IllegalArgumentException("强制转码方式forceMethod必须是 'fc', 'mts' 或 'auto'");
        }
    }
    
    /**
     * 获取有效的输出存储桶
     * 
     * @return 输出存储桶名称
     */
    public String getEffectiveOutputBucket() {
        return StringUtils.hasText(this.outputBucket) ? this.outputBucket : DEFAULT_OUTPUT_BUCKET;
    }
    
    /**
     * 判断是否为大文件（需要使用MTS）
     * 
     * @return true表示大文件
     */
    public boolean isLargeFile() {
        return fileSize != null && fileSize > 500 * 1024 * 1024L; // 大于500MB
    }
    
    /**
     * 判断是否强制使用指定方式
     * 
     * @return true表示强制指定
     */
    public boolean isForceMethod() {
        return StringUtils.hasText(forceMethod) && !"auto".equalsIgnoreCase(forceMethod);
    }
} 