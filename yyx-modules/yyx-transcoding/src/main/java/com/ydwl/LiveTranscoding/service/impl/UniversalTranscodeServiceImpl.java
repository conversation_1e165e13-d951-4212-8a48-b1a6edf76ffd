package com.ydwl.LiveTranscoding.service.impl;

import com.ydwl.LiveTranscoding.service.IUniversalTranscodeService;
import com.ydwl.LiveTranscoding.service.FcTranscodeService;
import com.ydwl.LiveTranscoding.service.MctTranscodeService;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeRequestVo;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeResponseVo;
import com.ydwl.LiveTranscoding.domain.vo.ResolutionConfig;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeInitResponseVo;
import com.ydwl.LiveTranscoding.domain.dto.FcVideoFcTranscodeDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsTranscodeRequestDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsOutputItemDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsInput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 统一转码服务实现类
 * 整合FC和MTS转码，提供一致的API接口 这里写的很牛逼 我弄了好几天
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UniversalTranscodeServiceImpl implements IUniversalTranscodeService {

    private final FcTranscodeService fcTranscodeService;
    private final MctTranscodeService mctTranscodeService;

    @Value("${transcoding.file.threshold.large:524288000}") // 500MB in bytes
    private long largeFileThreshold;

    @Value("${transcoding.file.threshold.medium:104857600}") // 100MB in bytes
    private long mediumFileThreshold;

    // 回调地址配置
    @Value("${transcoding.callback.default-url:https://yd-live.vip.cpolar.cn/live/transcode/callback}")
    private String defaultCallbackUrl;

    @Value("${transcoding.callback.fc-url:${transcoding.callback.default-url}}")
    private String fcCallbackUrl;

    @Value("${transcoding.callback.mts-url:${transcoding.callback.default-url}}")
    private String mtsCallbackUrl;

    // FC Template IDs
    @Value("${transcoding.fc.template.480p:FC-480p-template}")
    private String fcTemplate480p;
    @Value("${transcoding.fc.template.720p:FC-720p-template}")
    private String fcTemplate720p;
    @Value("${transcoding.fc.template.1080p:FC-1080p-template}")
    private String fcTemplate1080p;
    @Value("${transcoding.fc.template.2k:FC-2k-template}")
    private String fcTemplate2k;
    @Value("${transcoding.fc.template.4k:FC-4k-template}")
    private String fcTemplate4k;

    // MTS Template IDs
    @Value("${transcoding.mts.template.hls.480p:YOUR_MTS_HLS_480P_TEMPLATE_ID}")
    private String mtsTemplateHls480p;
    @Value("${transcoding.mts.template.hls.720p:YOUR_MTS_HLS_720P_TEMPLATE_ID}")
    private String mtsTemplateHls720p;
    @Value("${transcoding.mts.template.hls.1080p:YOUR_MTS_HLS_1080P_TEMPLATE_ID}")
    private String mtsTemplateHls1080p;
    @Value("${transcoding.mts.template.hls.2k:YOUR_MTS_HLS_2K_TEMPLATE_ID}")
    private String mtsTemplateHls2k;
    @Value("${transcoding.mts.template.hls.4k:YOUR_MTS_HLS_4K_TEMPLATE_ID}")
    private String mtsTemplateHls4k;

    @Override
    public TranscodeResponseVo transcode(TranscodeRequestVo request) throws Exception {
        log.info("开始统一转码处理，BizId: {}, 文件: {}/{}",
                request.getBizId(), request.getBucket(), request.getObject());

        // 参数校验
        request.validate();

        // 选择转码方式
        String method = selectTranscodeMethod(request);
        log.info("选择转码方式: {}, BizId: {}", method, request.getBizId());

        // 执行转码
        return transcodeWithMethod(request, method);
    }

    @Override
    public TranscodeResponseVo quickTranscode(String bucket, String object, String bizId, String callbackUrl) throws Exception {
        log.info("快速转码调用，BizId: {}, 文件: {}/{}", bizId, bucket, object);
        // 使用配置的回调地址，如果传入的回调地址为空
        String effectiveCallbackUrl = StringUtils.hasText(callbackUrl) ? callbackUrl : defaultCallbackUrl;
        // Pass null for inputLocation, TranscodeRequestVo.quick will use its default
        TranscodeRequestVo request = TranscodeRequestVo.quick(bucket, object, null, bizId, effectiveCallbackUrl);
        return transcode(request);
    }

    @Override
    public TranscodeResponseVo smartTranscode(String bucket, String object, String bizId, String callbackUrl, Long fileSize) throws Exception {
        log.info("智能选择转码服务调用，BizId: {}, 文件: {}/{}, 大小: {} bytes",
                bizId, bucket, object, fileSize);
        // 使用配置的回调地址，如果传入的回调地址为空就使用默认的
        String effectiveCallbackUrl = StringUtils.hasText(callbackUrl) ? callbackUrl : defaultCallbackUrl;
        // Pass null for inputLocation, TranscodeRequestVo.smart (via quick) will use its default
        TranscodeRequestVo request = TranscodeRequestVo.smart(bucket, object, null, bizId, effectiveCallbackUrl, fileSize);
        return transcode(request);
    }

    @Override
    public TranscodeResponseVo transcodeWithMethod(TranscodeRequestVo request, String method) throws Exception {
        log.info("使用指定方式转码，方式: {}, BizId: {}", method, request.getBizId());

        request.validate();

        return switch (method.toLowerCase()) {
            case "fc" -> transcodeWithFc(request);
            case "mts" -> transcodeWithMts(request);
            default -> throw new IllegalArgumentException("不支持的转码方式: " + method);
        };
    }

    @Override
    public List<TranscodeResponseVo> batchTranscode(List<TranscodeRequestVo> requests) throws Exception {
        log.info("批量转码调用，任务数量: {}", requests.size());

        List<TranscodeResponseVo> responses = new ArrayList<>();

        for (TranscodeRequestVo request : requests) {
            try {
                TranscodeResponseVo response = transcode(request);
                responses.add(response);
                log.info("批量转码单个任务完成，BizId: {}, 状态: {}",
                        request.getBizId(), response.getStatusDescription());
            } catch (Exception e) {
                log.error("批量转码单个任务失败，BizId: {}", request.getBizId(), e);
                TranscodeResponseVo errorResponse = TranscodeResponseVo.failure(
                    request.getBizId(), "auto", e.getMessage()
                ).withRequest(request);
                responses.add(errorResponse);
            }
        }

        return responses;
    }

    /**
     * 选择转码方式
     * 根据文件大小、强制指定等因素智能选择
     */
    private String selectTranscodeMethod(TranscodeRequestVo request) {
        // 如果强制指定了转码方式
        if (request.isForceMethod()) {
            String method = request.getForceMethod();
            log.info("强制使用转码方式: {}, BizId: {}", method, request.getBizId());
            return method;
        }

        // 智能选择逻辑
        Long fileSize = request.getFileSize();
        if (fileSize != null) {
            if (fileSize > largeFileThreshold) {
                log.info("大文件({} MB)使用MTS转码, BizId: {}",
                        fileSize / 1024 / 1024, request.getBizId());
                return "mts";
            } else {
                log.info("中小文件({} MB)使用FC转码, BizId: {}",
                        fileSize / 1024 / 1024, request.getBizId());
                return "fc";
            }
        }

        // 根据分辨率配置选择
        ResolutionConfig resolutions = request.getResolutions();
        if (resolutions != null) {
            // 如果启用了高分辨率(2K/4K)，优先使用MTS
            if (resolutions.isEnable2k() || resolutions.isEnable4k()) {
                log.info("高分辨率转码使用MTS, BizId: {}", request.getBizId());
                return "mts";
            }

            // 如果启用的分辨率数量较多，使用MTS
            if (resolutions.getEnabledCount() >= 4) {
                log.info("多分辨率转码使用MTS, BizId: {}", request.getBizId());
                return "mts";
            }
        }

        // 默认使用FC转码（更快，成本更低）
        log.info("默认使用FC转码, BizId: {}", request.getBizId());
        return "fc";
    }

    /**
     * 使用FC转码
     */
    private TranscodeResponseVo transcodeWithFc(TranscodeRequestVo request) throws Exception {
        log.info("开始FC转码，BizId: {}", request.getBizId());

        try {
            // 构建FC转码请求
            FcVideoFcTranscodeDto fcRequest = buildFcRequest(request);

            // 调用FC转码服务
            TranscodeInitResponseVo fcResponse = fcTranscodeService.transcode(
                fcRequest.getInput(),
                fcRequest.getOutputs(),
                fcRequest.getUserData(),
                fcRequest.getCallbackUrl(),
                fcRequest.getPipelineId()
            );

            // 转换响应格式
            TranscodeResponseVo response = convertFcResponse(fcResponse, request);

            log.info("FC转码调用完成，BizId: {}, 状态: {}",
                    request.getBizId(), response.getStatusDescription());

            return response;

        } catch (Exception e) {
            log.error("FC转码调用失败，BizId: {}", request.getBizId(), e);
            return TranscodeResponseVo.failure(request.getBizId(), "fc", e.getMessage())
                    .withRequest(request);
        }
    }

    /**
     * 使用MTS转码
     */
    private TranscodeResponseVo transcodeWithMts(TranscodeRequestVo request) throws Exception {
        log.info("开始MTS转码，BizId: {}", request.getBizId());

        try {
            // 构建MTS转码请求
            MtsTranscodeRequestDto mtsRequest = buildMtsRequest(request);

            // 调用MTS转码服务
            TranscodeInitResponseVo mtsResponse = mctTranscodeService.MtsTranscode(mtsRequest);

            // 转换响应格式
            TranscodeResponseVo response = convertMtsResponse(mtsResponse, request);

            log.info("MTS转码调用完成，BizId: {}, 状态: {}",
                    request.getBizId(), response.getStatusDescription());

            return response;

        } catch (Exception e) {
            log.error("MTS转码调用失败，BizId: {}", request.getBizId(), e);
            return TranscodeResponseVo.failure(request.getBizId(), "mts", e.getMessage())
                    .withRequest(request);
        }
    }

    /**
     * 构建FC转码请求
     */
    private FcVideoFcTranscodeDto buildFcRequest(TranscodeRequestVo request) {
        FcVideoFcTranscodeDto fcRequest = new FcVideoFcTranscodeDto();
        FcVideoFcTranscodeDto.InputDTO input = new FcVideoFcTranscodeDto.InputDTO();
        input.setBucket(request.getBucket());
        input.setLocation(StringUtils.hasText(request.getInputLocation()) ?
                            request.getInputLocation() :
                            TranscodeRequestVo.DEFAULT_INPUT_LOCATION);
        input.setObject(request.getObject());
        input.setReferer("*");
        fcRequest.setInput(input);

        fcRequest.setOutputs(buildFcOutputs(request));
        fcRequest.setUserData(request.getBizId());

        // 使用配置的FC回调地址，如果请求中的回调地址为空
        String effectiveCallbackUrl = StringUtils.hasText(request.getCallbackUrl()) ?
                                       request.getCallbackUrl() : fcCallbackUrl;
        fcRequest.setCallbackUrl(effectiveCallbackUrl);

        fcRequest.setPipelineId(StringUtils.hasText(request.getPipelineId()) ? request.getPipelineId() : "default-pipeline");
        fcRequest.setSegmentTime(request.getSegmentTime());
        fcRequest.setEmergencyMode(request.getEmergencyMode());
        fcRequest.setTimeout(request.getTimeout());
        fcRequest.setSendStartCallback(request.getSendStartCallback());
        return fcRequest;
    }

    /**
     * 构建FC输出配置
     */
    private List<FcVideoFcTranscodeDto.OutputDTO> buildFcOutputs(TranscodeRequestVo request) {
        List<FcVideoFcTranscodeDto.OutputDTO> outputs = new ArrayList<>();
        ResolutionConfig resolutions = request.getResolutions();

        String basePath = extractBasePath(request.getObject());
        String fileName = extractFileName(request.getObject());

        // 根据分辨率配置生成输出
        if (resolutions.isEnable480p()) {
            outputs.add(createFcOutput(fcTemplate480p, basePath, fileName, "480p"));
        }
        if (resolutions.isEnable720p()) {
            outputs.add(createFcOutput(fcTemplate720p, basePath, fileName, "720p"));
        }
        if (resolutions.isEnable1080p()) {
            outputs.add(createFcOutput(fcTemplate1080p, basePath, fileName, "1080p"));
        }
        if (resolutions.isEnable2k()) {
            outputs.add(createFcOutput(fcTemplate2k, basePath, fileName, "2k"));
        }
        if (resolutions.isEnable4k()) {
            outputs.add(createFcOutput(fcTemplate4k, basePath, fileName, "4k"));
        }

        return outputs;
    }

    /**
     * 创建FC输出配置
     */
    private FcVideoFcTranscodeDto.OutputDTO createFcOutput(String templateId, String basePath,
                                                          String fileName, String resolution) {
        FcVideoFcTranscodeDto.OutputDTO output = new FcVideoFcTranscodeDto.OutputDTO();
        output.setTemplateId(templateId);
        output.setOutputObject(basePath + resolution + "/index.m3u8");
        output.setUserData(fileName + "_" + resolution);
        return output;
    }

    /**
     * 构建MTS转码请求
     */
    private MtsTranscodeRequestDto buildMtsRequest(TranscodeRequestVo request) {
        MtsTranscodeRequestDto mtsRequest = new MtsTranscodeRequestDto();
        MtsInput input = new MtsInput();
        input.setBucket(request.getBucket());
        input.setLocation(StringUtils.hasText(request.getInputLocation()) ?
                            request.getInputLocation() :
                            TranscodeRequestVo.DEFAULT_INPUT_LOCATION);
        input.setObject(request.getObject());
        input.setReferer("*");
        mtsRequest.setInput(input);

        mtsRequest.setBizId(request.getBizId());

        // 使用配置的MTS回调地址，如果请求中的回调地址为空
        String effectiveCallbackUrl = StringUtils.hasText(request.getCallbackUrl()) ?
                                      request.getCallbackUrl() : mtsCallbackUrl;
        mtsRequest.setCallbackUrl(effectiveCallbackUrl);

        mtsRequest.setOutputBucket(request.getEffectiveOutputBucket());
        mtsRequest.setOutputLocation(StringUtils.hasText(request.getOutputLocation()) ?
                                     request.getOutputLocation() :
                                     TranscodeRequestVo.DEFAULT_OUTPUT_LOCATION);
        if (StringUtils.hasText(request.getPipelineId())) {
            mtsRequest.setPipelineId(request.getPipelineId());
        }

        mtsRequest.setOutputs(buildMtsOutputs(request));
        return mtsRequest;
    }

    private List<MtsOutputItemDto> buildMtsOutputs(TranscodeRequestVo request) {
        List<MtsOutputItemDto> mtsOutputs = new ArrayList<>();
        ResolutionConfig resolutions = request.getResolutions();
        String basePath = extractBasePath(request.getObject());
        String fileNameWithoutExt = extractFileNameWithoutExtension(request.getObject());

        if (resolutions.isEnable480p()) {
            mtsOutputs.add(MtsOutputItemDto.builder()
                .outputObject(basePath + "mts_480p/" + fileNameWithoutExt + "/" + fileNameWithoutExt + "_480p.m3u8")
                .templateId(mtsTemplateHls480p)
                .userData(fileNameWithoutExt + "_mts_480p")
                .build());
        }
        if (resolutions.isEnable720p()) {
             mtsOutputs.add(MtsOutputItemDto.builder()
                .outputObject(basePath + "mts_720p/" + fileNameWithoutExt + "/" + fileNameWithoutExt + "_720p.m3u8")
                .templateId(mtsTemplateHls720p)
                .userData(fileNameWithoutExt + "_mts_720p")
                .build());
        }
        if (resolutions.isEnable1080p()) {
             mtsOutputs.add(MtsOutputItemDto.builder()
                .outputObject(basePath + "mts_1080p/" + fileNameWithoutExt + "/" + fileNameWithoutExt + "_1080p.m3u8")
                .templateId(mtsTemplateHls1080p)
                .userData(fileNameWithoutExt + "_mts_1080p")
                .build());
        }
        if (resolutions.isEnable2k()) {
            mtsOutputs.add(MtsOutputItemDto.builder()
                .outputObject(basePath + "mts_2k/" + fileNameWithoutExt + "/" + fileNameWithoutExt + "_2k.m3u8")
                .templateId(mtsTemplateHls2k)
                .userData(fileNameWithoutExt + "_mts_2k")
                .build());
        }
        if (resolutions.isEnable4k()) {
            mtsOutputs.add(MtsOutputItemDto.builder()
                .outputObject(basePath + "mts_4k/" + fileNameWithoutExt + "/" + fileNameWithoutExt + "_4k.m3u8")
                .templateId(mtsTemplateHls4k)
                .userData(fileNameWithoutExt + "_mts_4k")
                .build());
        }
        return mtsOutputs;
    }

    /**
     * 转换FC响应
     */
    private TranscodeResponseVo convertFcResponse(TranscodeInitResponseVo fcResponse,
                                                 TranscodeRequestVo request) {
        TranscodeResponseVo response;

        if (fcResponse.getStatus() == 1L) {
            response = TranscodeResponseVo.success(
                fcResponse.getBizId(),
                request.getBizId(),
                "fc",
                fcResponse.getMessage()
            );
        } else {
            response = TranscodeResponseVo.failure(
                request.getBizId(),
                "fc",
                fcResponse.getMessage()
            );
        }

        return response.withRequest(request).withEstimatedTime();
    }

    /**
     * 转换MTS响应
     */
    private TranscodeResponseVo convertMtsResponse(TranscodeInitResponseVo mtsResponse,
                                                  TranscodeRequestVo request) {
        TranscodeResponseVo response;

        if (mtsResponse.getStatus() >= 200 && mtsResponse.getStatus() < 300) {
            response = TranscodeResponseVo.success(
                mtsResponse.getBizId(),
                request.getBizId(),
                "mts",
                mtsResponse.getMessage()
            );
        } else {
            response = TranscodeResponseVo.failure(
                request.getBizId(),
                "mts",
                String.valueOf(mtsResponse.getStatus()),
                mtsResponse.getMessage()
            );
        }

        return response.withRequest(request).withEstimatedTime();
    }

    /**
     * 提取文件基础路径
     */
    private String extractBasePath(String objectPath) {
        int lastSlashIndex = objectPath.lastIndexOf("/");
        if (lastSlashIndex > 0) {
            return objectPath.substring(0, lastSlashIndex + 1);
        }
        return "";
    }

    /**
     * 提取文件名（不含扩展名）
     */
    private String extractFileName(String objectPath) {
        String fileName = objectPath;
        int lastSlashIndex = objectPath.lastIndexOf("/");
        if (lastSlashIndex >= 0) {
            fileName = objectPath.substring(lastSlashIndex + 1);
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0) {
            fileName = fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }

    private String extractFileNameWithoutExtension(String objectPath) {
        if (objectPath == null) return "default_filename";
        String fileName = objectPath;
        int lastSlashIndex = objectPath.lastIndexOf('/');
        if (lastSlashIndex >= 0) {
            fileName = objectPath.substring(lastSlashIndex + 1);
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            fileName = fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }
}
