package com.ydwl.LiveTranscoding.callback;

import com.ydwl.LiveTranscoding.domain.dto.FcTranscodeCallbackResponseDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsTranscodeCallbackResponseDto;
import com.ydwl.LiveTranscoding.domain.vo.UniversalTranscodeCallbackVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 统一转码回调处理器
 * 将FC和MTS的回调数据转换为统一格式
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class UniversalTranscodeCallbackHandler {
    
    /**
     * 处理FC转码回调
     * 
     * @param fcCallback FC转码回调数据
     * @return 统一回调格式
     */
    public UniversalTranscodeCallbackVo handleFcCallback(FcTranscodeCallbackResponseDto fcCallback) {
        log.info("处理FC转码回调，JobId: {}, UserData: {}", 
                fcCallback.getJobId(), fcCallback.getUserData());
        
        UniversalTranscodeCallbackVo callback = new UniversalTranscodeCallbackVo();
        
        // 基础信息
        callback.setType("Report");
        callback.setTranscodeMethod("fc");
        callback.setJobId(fcCallback.getJobId());
        callback.setRequestId(fcCallback.getJobId());
        callback.setBizId(fcCallback.getUserData());
        
        // 状态转换
        if ("TranscodeSuccess".equals(fcCallback.getEventType())) {
            callback.setEventType("TranscodeSuccess");
            callback.setState("Success");
            callback.setCode("");
            callback.setMessage(fcCallback.getMessage() != null ? fcCallback.getMessage() : "FC转码成功");
        } else if ("TranscodeFail".equals(fcCallback.getEventType())) {
            callback.setEventType("TranscodeFail");
            callback.setState("Fail");
            callback.setCode("FCTranscodeError");
            callback.setMessage(fcCallback.getMessage() != null ? fcCallback.getMessage() : "FC转码失败");
        } else {
            callback.setEventType("Transcoding");
            callback.setState("Transcoding");
            callback.setCode("");
            callback.setMessage(fcCallback.getMessage() != null ? fcCallback.getMessage() : "FC转码进行中");
        }
        
        // 输入信息
        if (fcCallback.getInput() != null) {
            UniversalTranscodeCallbackVo.InputInfo inputInfo = new UniversalTranscodeCallbackVo.InputInfo();
            inputInfo.setBucket(fcCallback.getInput().getBucket());
            inputInfo.setLocation(fcCallback.getInput().getLocation());
            inputInfo.setObject(fcCallback.getInput().getObject());
            callback.setInput(inputInfo);
        }
        
        // 输出信息
        if (fcCallback.getOutputs() != null && !fcCallback.getOutputs().isEmpty()) {
            callback.setOutputs(fcCallback.getOutputs().stream()
                    .map(this::convertFcOutput)
                    .toList());
        }
        
        log.info("FC转码回调处理完成，状态: {}, BizId: {}", 
                callback.getState(), callback.getBizId());
        
        return callback;
    }
    
    /**
     * 处理MTS转码回调
     * 
     * @param mtsCallback MTS转码回调数据
     * @return 统一回调格式
     */
    public UniversalTranscodeCallbackVo handleMtsCallback(MtsTranscodeCallbackResponseDto mtsCallback) {
        log.info("处理MTS转码回调，JobId: {}, UserData: {}", 
                mtsCallback.getJobId(), mtsCallback.getUserData());
        
        UniversalTranscodeCallbackVo callback = new UniversalTranscodeCallbackVo();
        
        // 基础信息
        callback.setType("Report");
        callback.setTranscodeMethod("mts");
        callback.setJobId(mtsCallback.getJobId());
        callback.setRequestId(mtsCallback.getJobId());
        callback.setBizId(mtsCallback.getUserData());
        
        // 状态转换
        callback.setEventType(mtsCallback.getEventType());
        callback.setState(mtsCallback.getState());
        callback.setCode(mtsCallback.getCode());
        callback.setMessage(mtsCallback.getMessage());
        
        // 输入信息
        if (mtsCallback.getInput() != null) {
            UniversalTranscodeCallbackVo.InputInfo inputInfo = new UniversalTranscodeCallbackVo.InputInfo();
            inputInfo.setBucket(mtsCallback.getInput().getBucket());
            inputInfo.setLocation(mtsCallback.getInput().getLocation());
            inputInfo.setObject(mtsCallback.getInput().getObject());
            callback.setInput(inputInfo);
        }
        
        // 输出信息
        if (mtsCallback.getOutputs() != null && !mtsCallback.getOutputs().isEmpty()) {
            callback.setOutputs(mtsCallback.getOutputs().stream()
                    .map(this::convertMtsOutput)
                    .toList());
        }
        
        log.info("MTS转码回调处理完成，状态: {}, BizId: {}", 
                callback.getState(), callback.getBizId());
        
        return callback;
    }
    
    /**
     * 转换FC输出信息
     */
    private UniversalTranscodeCallbackVo.OutputInfo convertFcOutput(
            FcTranscodeCallbackResponseDto.OutputDTO fcOutput) {
        
        UniversalTranscodeCallbackVo.OutputInfo output = new UniversalTranscodeCallbackVo.OutputInfo();
        
        output.setObjectName(fcOutput.getObjectName());
        output.setBucket(fcOutput.getBucket());
        output.setLocation(fcOutput.getLocation());
        output.setTemplateId(fcOutput.getTemplateId());
        output.setState(fcOutput.getState());
        output.setCode(fcOutput.getCode());
        output.setMessage(fcOutput.getMessage());
        output.setWidth(fcOutput.getWidth());
        output.setHeight(fcOutput.getHeight());
        output.setDuration(fcOutput.getDuration());
        output.setFilesize(fcOutput.getFilesize());
        output.setBitrate(fcOutput.getBitrate());
        output.setFps(fcOutput.getFps());
        
        // FC特有字段
        output.setTranscodeMethod("fc");
        
        return output;
    }
    
    /**
     * 转换MTS输出信息
     */
    private UniversalTranscodeCallbackVo.OutputInfo convertMtsOutput(
            MtsTranscodeCallbackResponseDto.OutputDTO mtsOutput) {
        
        UniversalTranscodeCallbackVo.OutputInfo output = new UniversalTranscodeCallbackVo.OutputInfo();
        
        output.setObjectName(mtsOutput.getOutputObject());
        output.setBucket(mtsOutput.getOutputBucket());
        output.setLocation(mtsOutput.getOutputLocation());
        output.setTemplateId(mtsOutput.getTemplateId());
        // MTS可能没有单独输出状态，使用作业级别状态
        output.setState("Success"); // 默认成功，若有错误码则表示失败
        output.setCode(mtsOutput.getErrorCode());
        output.setMessage(mtsOutput.getErrorMessage());
        output.setWidth(mtsOutput.getWidth());
        output.setHeight(mtsOutput.getHeight());
        output.setDuration(mtsOutput.getDuration());
        output.setFilesize(mtsOutput.getFileSize());
        output.setBitrate(mtsOutput.getVideoStream() != null ? mtsOutput.getVideoStream().getBitrate() : null);
        output.setFps(mtsOutput.getVideoStream() != null ? mtsOutput.getVideoStream().getFps() : null);
        
        // MTS特有字段
        output.setTranscodeMethod("mts");
        
        return output;
    }
    
    /**
     * 验证回调数据完整性
     * 
     * @param callback 统一回调数据
     * @return 是否有效
     */
    public boolean validateCallback(UniversalTranscodeCallbackVo callback) {
        if (callback == null) {
            log.warn("回调数据为空");
            return false;
        }
        
        if (callback.getJobId() == null || callback.getJobId().trim().isEmpty()) {
            log.warn("回调数据缺少JobId");
            return false;
        }
        
        if (callback.getBizId() == null || callback.getBizId().trim().isEmpty()) {
            log.warn("回调数据缺少BizId");
            return false;
        }
        
        if (callback.getEventType() == null || callback.getEventType().trim().isEmpty()) {
            log.warn("回调数据缺少EventType");
            return false;
        }
        
        return true;
    }
    
    /**
     * 判断是否为成功回调
     * 
     * @param callback 统一回调数据
     * @return 是否成功
     */
    public boolean isSuccessCallback(UniversalTranscodeCallbackVo callback) {
        return "TranscodeSuccess".equals(callback.getEventType()) && 
               "Success".equals(callback.getState());
    }
    
    /**
     * 判断是否为失败回调
     * 
     * @param callback 统一回调数据
     * @return 是否失败
     */
    public boolean isFailureCallback(UniversalTranscodeCallbackVo callback) {
        return "TranscodeFail".equals(callback.getEventType()) && 
               "Fail".equals(callback.getState());
    }
    
    /**
     * 判断是否为进行中回调
     * 
     * @param callback 统一回调数据
     * @return 是否进行中
     */
    public boolean isProcessingCallback(UniversalTranscodeCallbackVo callback) {
        return "Transcoding".equals(callback.getEventType()) && 
               "Transcoding".equals(callback.getState());
    }
} 