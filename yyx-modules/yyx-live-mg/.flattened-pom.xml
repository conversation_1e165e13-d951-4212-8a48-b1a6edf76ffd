<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ydwl</groupId>
    <artifactId>yyx-modules</artifactId>
    <version>5.4.0</version>
  </parent>
  <groupId>com.ydwl</groupId>
  <artifactId>yyx-live</artifactId>
  <version>5.4.0</version>
  <description>直播模块</description>
  <dependencies>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-doc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-sms</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-mail</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-idempotent</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-log</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-security</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-ratelimiter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-translation</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-sensitive</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-encrypt</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-tenant</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-websocket</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>weixin-java-miniapp</artifactId>
      <version>4.7.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>weixin-java-pay</artifactId>
      <version>4.7.0</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>3.17.4</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>live20161101</artifactId>
      <version>1.1.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>${fastjson.version}</version>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-transcoding</artifactId>
      <version>5.4.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-oss</artifactId>
    </dependency>
  </dependencies>
</project>
