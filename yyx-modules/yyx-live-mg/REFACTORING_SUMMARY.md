# 直播模块分层优化和领域模型重构总结

## 🎯 优化目标

1. **分层优化**：明确各层职责，提高代码可维护性
2. **领域模型优化**：引入DDD设计，提升业务表达力
3. **代码重复消除**：统一API设计，减少重复代码
4. **异常处理统一**：建立一致的异常处理策略

## 🏗️ 新的架构分层

### 1. 表现层 (Presentation Layer)
- **位置**: `controller/refactored/`
- **职责**: 
  - 接收HTTP请求
  - 参数验证
  - 调用Manager层
  - 返回响应

**示例**:
```java
@RestController
@RequestMapping("/api/v2/live")
public class LiveManagementController {
    // 统一的RESTful API设计
}
```

### 2. 管理层 (Manager Layer) - 新增
- **位置**: `manager/`
- **职责**:
  - 协调多个领域服务
  - 处理复杂业务流程
  - 事务管理
  - 领域事件发布

**示例**:
```java
@Component
public class LiveManager {
    // 协调领域服务，处理复杂业务流程
}
```

### 3. 领域层 (Domain Layer) - 重构
- **位置**: `domain/model/`, `domain/service/`, `domain/repository/`
- **职责**:
  - 核心业务逻辑
  - 业务规则验证
  - 领域事件
  - 聚合根管理

**核心组件**:
- `LiveAggregate`: 直播聚合根
- `LiveDomainService`: 领域服务
- `LiveRepository`: 仓储接口

### 4. 基础设施层 (Infrastructure Layer) - 新增
- **位置**: `infrastructure/`
- **职责**:
  - 仓储实现
  - 外部服务集成
  - 技术细节实现

## 🎨 领域模型设计

### 聚合根 (Aggregate Root)
```java
public class LiveAggregate {
    private final LiveId liveId;
    private LiveInfo liveInfo;
    private LiveStatus status;
    private LiveSettings settings;
    private StreamInfo streamInfo;
    private LiveStatistics statistics;
    
    // 业务方法
    public void startLive() { ... }
    public void endLive() { ... }
}
```

### 值对象 (Value Objects)
- `LiveId`: 直播ID
- `LiveInfo`: 直播基本信息
- `LiveStatus`: 直播状态（包含状态转换规则）
- `LiveSettings`: 直播设置
- `StreamInfo`: 推流信息
- `LiveStatistics`: 统计信息

### 领域事件 (Domain Events)
- `LiveCreatedDomainEvent`: 直播创建事件
- `LiveStartedDomainEvent`: 直播开始事件
- `LiveEndedDomainEvent`: 直播结束事件
- `StreamInfoUpdatedDomainEvent`: 推流信息更新事件

## 🔄 主要改进

### 1. 消除代码重复
**之前**: 
- `LiveController` 和 `LiveDataController` 都有创建直播的接口
- 功能重复，容易造成混淆

**现在**:
- 统一的 `LiveManagementController`
- 清晰的RESTful API设计

### 2. 统一异常处理
**之前**:
```java
// 有些返回 R.fail()
return R.fail("创建直播失败");

// 有些抛出异常
throw new RuntimeException("创建直播失败");
```

**现在**:
```java
// 统一的异常处理策略
try {
    // 业务逻辑
    return R.ok(result);
} catch (Exception e) {
    log.error("操作失败", e);
    return R.fail("操作失败: " + e.getMessage());
}
```

### 3. 状态管理优化
**之前**:
```java
// 状态转换逻辑分散在Controller中
switch (status) {
    case 0: targetStatus = LiveStatus.NOT_STARTED; break;
    // ...
}
```

**现在**:
```java
// 状态转换规则封装在值对象中
public boolean canTransitionTo(LiveStatusEnum targetStatus) {
    EnumSet<LiveStatusEnum> allowedTransitions = TRANSITION_RULES.get(this.value);
    return allowedTransitions != null && allowedTransitions.contains(targetStatus);
}
```

### 4. 业务规则集中化
**之前**: 业务规则分散在各个Service中

**现在**: 业务规则集中在聚合根和领域服务中
```java
public class LiveAggregate {
    private void validateBusinessRules() {
        if (liveInfo.getPlanStartTime().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("计划开始时间不能早于当前时间");
        }
    }
}
```

## 📁 新的目录结构

```
src/main/java/com/ydwl/live/
├── controller/
│   └── refactored/              # 重构后的控制器
│       ├── LiveManagementController.java
│       ├── request/             # 请求对象
│       └── response/            # 响应对象
├── manager/                     # 管理层（新增）
│   ├── LiveManager.java
│   ├── DomainEventPublisher.java
│   └── dto/                     # 管理层DTO
├── domain/                      # 领域层（重构）
│   ├── model/
│   │   ├── LiveAggregate.java   # 聚合根
│   │   └── valueobject/         # 值对象
│   ├── service/                 # 领域服务
│   └── repository/              # 仓储接口
├── infrastructure/              # 基础设施层（新增）
│   ├── repository/              # 仓储实现
│   └── service/                 # 外部服务实现
└── [原有目录保持不变]
```

## 🚀 使用方式

### 1. 创建直播
```java
// 新的API
POST /api/v2/live
{
    "categoryId": 1,
    "title": "直播标题",
    "planStartTime": "2025-01-15T20:00:00"
}
```

### 2. 开始直播
```java
PUT /api/v2/live/{id}/start
```

### 3. 结束直播
```java
PUT /api/v2/live/{id}/end
```

## 🔧 迁移建议

1. **渐进式迁移**: 新功能使用新架构，旧功能逐步迁移
2. **API版本控制**: 使用 `/api/v2/` 前缀区分新旧API
3. **向后兼容**: 保留原有API，逐步废弃
4. **测试覆盖**: 为新架构编写完整的单元测试和集成测试

## 📈 预期收益

1. **可维护性提升**: 清晰的分层和职责划分
2. **业务表达力增强**: 领域模型更好地表达业务概念
3. **代码重复减少**: 统一的API设计
4. **扩展性提升**: 更容易添加新功能
5. **测试性提升**: 更容易进行单元测试

## 🎯 下一步计划

1. **完善单元测试**: 为新架构编写测试用例
2. **性能优化**: 添加缓存机制
3. **监控指标**: 添加业务监控
4. **文档完善**: 编写API文档和开发指南
5. **团队培训**: 对团队进行DDD和新架构的培训
