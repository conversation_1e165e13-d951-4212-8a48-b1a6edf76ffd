# 直播模块问题分析与最佳实践

## 🚨 发现的主要问题

### 1. **并发安全问题**

#### 问题1：状态更新竞态条件
**现状**：
```java
// LiveStateManager.java - 存在竞态条件
public boolean updateLiveStatus(Long liveId, LiveStatus status) {
    LambdaUpdateWrapper<Live> updateWrapper = new LambdaUpdateWrapper<>();
    updateWrapper.eq(Live::getId, liveId).set(Live::getStatus, status.getValue());
    int result = liveMapper.update(null, updateWrapper);
    return result > 0;
}
```

**问题**：
- 没有并发控制，多个请求同时更新可能导致状态不一致
- 缺少乐观锁，可能出现数据覆盖
- 没有状态转换验证

**解决方案**：
- ✅ 使用分布式锁保证并发安全
- ✅ 添加乐观锁防止数据覆盖
- ✅ 实现状态转换规则验证

#### 问题2：定时任务重复执行
**现状**：
```java
@Scheduled(fixedRateString = "${ydwl.live.task.status-check-interval:60}")
public void checkLiveStatus() {
    // 多实例部署时会重复执行
    for (Live live : livesInProgress) {
        checkSingleLive(live);
    }
}
```

**问题**：
- 多实例部署时定时任务重复执行
- 可能导致资源浪费和数据不一致

**解决方案**：
- ✅ 使用分布式锁确保只有一个实例执行
- ✅ 添加任务执行日志和监控

### 2. **事务边界问题**

#### 问题3：事务范围过大
**现状**：
```java
@Transactional(rollbackFor = Exception.class)
public R<Map<String, Object>> createLiveWithStream(LiveBo bo) {
    // 事务包含了事件发布，可能导致长事务
    eventPublisher.publish(new LiveCreatedEvent(liveId, bo));
    LiveStreamInfo streamInfo = liveStreamService.createStreamForLive(liveId, bo.getTitle());
}
```

**问题**：
- 事务包含外部服务调用，可能导致长事务
- 事件发布在事务内，如果事件处理失败会影响主业务
- 外部服务调用失败会导致整个事务回滚

**解决方案**：
- ✅ 缩小事务边界，只包含核心数据库操作
- ✅ 事件发布移到事务提交后执行
- ✅ 外部服务调用异步化处理

### 3. **性能问题**

#### 问题4：N+1查询问题
**现状**：
```java
for (Live live : livesInProgress) {
    // 每个直播都单独查询推流信息
    LiveStream stream = liveStreamMapper.selectOne(
        new LambdaQueryWrapper<LiveStream>().eq(LiveStream::getLiveId, liveId));
}
```

**问题**：
- 循环中执行数据库查询，造成N+1问题
- 性能随数据量线性下降

**解决方案**：
- ✅ 使用批量查询，一次性获取所有推流信息
- ✅ 使用JOIN查询减少数据库交互
- ✅ 添加适当的缓存机制

#### 问题5：缺少缓存策略
**现状**：
- 直播状态、推流信息等频繁查询的数据没有缓存
- 每次查询都访问数据库

**解决方案**：
- ✅ 为频繁查询的数据添加Redis缓存
- ✅ 实现缓存更新和失效策略
- ✅ 添加缓存预热机制

### 4. **数据一致性问题**

#### 问题6：回调幂等性不完整
**现状**：
```java
// 只检查了任务状态，但没有完整的幂等性保证
if (taskVo != null && taskVo.getStatus() != null && taskVo.getStatus() == 2L) {
    return R.ok("任务已处理，不重复处理");
}
```

**问题**：
- 回调处理缺少完整的幂等性保证
- 可能导致重复处理或数据不一致

**解决方案**：
- ✅ 使用分布式锁保证回调处理的原子性
- ✅ 添加回调状态字段，完善幂等性检查
- ✅ 记录详细的处理日志

### 5. **错误处理问题**

#### 问题7：异常处理不一致
**现状**：
- 有些方法返回`R.fail()`
- 有些方法抛出`RuntimeException`
- 异常处理策略不统一

**解决方案**：
- ✅ 统一异常处理策略
- ✅ 定义明确的异常分类
- ✅ 实现全局异常处理器

## 🛠️ 最佳实践实现

### 1. **并发安全最佳实践**

#### 优化后的状态管理
```java
@Service
public class OptimizedLiveStateManager {
    
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "live:status", key = "#liveId")
    public boolean updateLiveStatusSafely(Long liveId, LiveStatus targetStatus) {
        String lockKey = LOCK_PREFIX + liveId;
        String lockValue = UUID.randomUUID().toString();
        
        // 获取分布式锁
        if (!distributedLockUtil.lock(lockKey, lockValue, LOCK_TIMEOUT, TimeUnit.SECONDS)) {
            throw new RuntimeException("系统繁忙，请稍后重试");
        }
        
        try {
            // 1. 查询当前状态（乐观锁）
            Live currentLive = liveMapper.selectById(liveId);
            
            // 2. 验证状态转换
            if (!isValidTransition(currentStatus, targetStatus)) {
                throw new IllegalStateException("状态转换不合法");
            }
            
            // 3. 使用乐观锁更新
            LambdaUpdateWrapper<Live> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Live::getId, liveId)
                .eq(Live::getUpdateTime, currentLive.getUpdateTime()) // 乐观锁
                .set(Live::getStatus, targetStatus.getValue())
                .set(Live::getUpdateTime, new Date());
            
            int result = liveMapper.update(null, updateWrapper);
            if (result == 0) {
                throw new RuntimeException("状态更新冲突，请重试");
            }
            
            return true;
        } finally {
            distributedLockUtil.unlock(lockKey, lockValue);
        }
    }
}
```

### 2. **定时任务最佳实践**

#### 优化后的定时任务
```java
@Component
public class OptimizedLiveStatusCheckTask {
    
    @Scheduled(fixedRateString = "${ydwl.live.task.status-check-interval:60}")
    public void checkLiveStatus() {
        String lockValue = UUID.randomUUID().toString();
        
        // 分布式锁防止重复执行
        if (!distributedLockUtil.lock(TASK_LOCK_KEY, lockValue, TASK_LOCK_TIMEOUT, TimeUnit.SECONDS)) {
            log.debug("任务已在其他实例执行，跳过");
            return;
        }
        
        try {
            executeStatusCheck();
        } finally {
            distributedLockUtil.unlock(TASK_LOCK_KEY, lockValue);
        }
    }
    
    private void executeStatusCheck() {
        // 1. 批量查询避免N+1问题
        List<Live> livesInProgress = liveMapper.selectList(/* 批量查询 */);
        
        // 2. 批量查询推流信息
        Map<Long, LiveStream> streamMap = getStreamMapByLiveIds(liveIds);
        
        // 3. 批量更新状态
        stateManager.batchUpdateLiveStatus(timeoutLiveIds, LiveStatus.ENDED);
    }
}
```

### 3. **事务边界最佳实践**

#### 优化后的事务处理
```java
@Service
public class OptimizedLiveDataService {
    
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> createLiveWithStream(LiveBo bo) {
        // 1. 核心业务逻辑在事务内
        boolean success = liveService.insertByBo(bo);
        LiveStreamInfo streamInfo = liveStreamService.createStreamForLive(liveId, bo.getTitle());
        
        // 2. 事务提交后发布事件
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                eventPublisher.publish(new LiveCreatedEvent(liveId, bo));
            }
        });
        
        return R.ok(result);
    }
}
```

### 4. **缓存策略最佳实践**

#### 多层缓存架构
```java
@Service
public class OptimizedLiveDataService {
    
    // L1缓存：直播详情
    @Cacheable(value = "live:detail", key = "#liveId", unless = "#result == null")
    public Map<String, Object> getLiveDetail(Long liveId) {
        // 实现逻辑
    }
    
    // L2缓存：直播状态
    @Cacheable(value = "live:status", key = "#liveId")
    public LiveStatus getLiveStatus(Long liveId) {
        // 实现逻辑
    }
    
    // 缓存失效
    @CacheEvict(value = {"live:detail", "live:status"}, key = "#liveId")
    public void evictLiveCache(Long liveId) {
        // 缓存失效逻辑
    }
}
```

### 5. **回调幂等性最佳实践**

#### 优化后的回调处理
```java
@Service
public class OptimizedTranscodeCallbackService {
    
    @Transactional(rollbackFor = Exception.class)
    public void handleFcTranscodeSuccess(FcTranscodeCallbackResponseDto callbackData) {
        String bizId = callbackData.getUserData();
        String lockKey = CALLBACK_LOCK_PREFIX + bizId;
        String lockValue = UUID.randomUUID().toString();
        
        // 分布式锁保证幂等性
        if (!distributedLockUtil.lock(lockKey, lockValue, CALLBACK_LOCK_TIMEOUT, TimeUnit.SECONDS)) {
            log.warn("回调正在处理中，跳过重复处理: BizId={}", bizId);
            return;
        }
        
        try {
            // 1. 检查任务状态
            LiveTranscodeTaskVo taskVo = liveTranscodeTaskService.queryByBizId(bizId);
            if (isTaskCompleted(taskVo) || isCallbackProcessed(taskVo)) {
                log.info("任务已完成或回调已处理，避免重复处理");
                return;
            }
            
            // 2. 更新任务状态
            updateTaskStatus(taskVo, callbackData);
            
            // 3. 后续处理
            handleTranscodeSuccess(taskVo, callbackData);
            
        } finally {
            distributedLockUtil.unlock(lockKey, lockValue);
        }
    }
}
```

## 📊 性能优化建议

### 1. **数据库优化**
- ✅ 添加合适的索引
- ✅ 使用批量操作减少数据库交互
- ✅ 实现读写分离

### 2. **缓存优化**
- ✅ 多层缓存架构
- ✅ 缓存预热和更新策略
- ✅ 缓存穿透和雪崩防护

### 3. **异步处理**
- ✅ 事件驱动架构
- ✅ 消息队列异步处理
- ✅ 线程池优化

## 🔍 监控和告警

### 1. **业务监控**
- 直播创建成功率
- 状态转换成功率
- 回调处理成功率

### 2. **性能监控**
- 接口响应时间
- 数据库查询性能
- 缓存命中率

### 3. **异常告警**
- 并发冲突告警
- 事务回滚告警
- 外部服务调用失败告警

## 🎯 实施建议

1. **优先级排序**：
   - P0：并发安全问题
   - P1：事务边界问题
   - P2：性能优化
   - P3：监控完善

2. **渐进式改进**：
   - 先修复关键问题
   - 逐步优化性能
   - 完善监控体系

3. **测试策略**：
   - 单元测试覆盖
   - 并发测试验证
   - 性能测试评估
