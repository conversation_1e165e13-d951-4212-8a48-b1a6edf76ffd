package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveShareBo;
import com.ydwl.live.domain.vo.LiveShareVo;
import com.ydwl.live.domain.LiveShare;
import com.ydwl.live.mapper.LiveShareMapper;
import com.ydwl.live.service.ILiveShareService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 直播分享记录Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveShareServiceImpl implements ILiveShareService {

    private final LiveShareMapper baseMapper;

    /**
     * 查询直播分享记录
     *
     * @param id 主键
     * @return 直播分享记录
     */
    @Override
    public LiveShareVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询直播分享记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播分享记录分页列表
     */
    @Override
    public TableDataInfo<LiveShareVo> queryPageList(LiveShareBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveShare> lqw = buildQueryWrapper(bo);
        Page<LiveShareVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的直播分享记录列表
     *
     * @param bo 查询条件
     * @return 直播分享记录列表
     */
    @Override
    public List<LiveShareVo> queryList(LiveShareBo bo) {
        LambdaQueryWrapper<LiveShare> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveShare> buildQueryWrapper(LiveShareBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveShare> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveShare::getId);
        lqw.eq(bo.getLiveId() != null, LiveShare::getLiveId, bo.getLiveId());
        lqw.eq(bo.getUserId() != null, LiveShare::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getSharePlatform()), LiveShare::getSharePlatform, bo.getSharePlatform());
        return lqw;
    }

    /**
     * 新增直播分享记录
     *
     * @param bo 直播分享记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveShareBo bo) {
        LiveShare add = MapstructUtils.convert(bo, LiveShare.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改直播分享记录
     *
     * @param bo 直播分享记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveShareBo bo) {
        LiveShare update = MapstructUtils.convert(bo, LiveShare.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveShare entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除直播分享记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
