package com.ydwl.live.service;

import com.ydwl.live.domain.bo.ConfirmUploadBo;
import com.ydwl.live.domain.bo.PreSignedUrlRequestBo;
import com.ydwl.live.domain.vo.LiveVideoUploadVo;
import com.ydwl.live.domain.vo.OssPreSignedUrlVo;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;

/**
 * 文件上传 服务层
 *
 * <AUTHOR> <PERSON>
 */
public interface ILiveOssService {


    /**
     * 方案一：上传 MultipartFile 到对象存储服务，并保存文件信息到数据库
     *
     * @param file 要上传的 MultipartFile 对象
     * @param liveid 直播ID
     * @return 上传成功后的 LiveVideoUploadVo 对象，包含文件信息
     */
    LiveVideoUploadVo upload(MultipartFile file, long liveid);

    /**
     * 方案二：获取OSS预签名上传URL
     *
     * @return 预签名上传信息
     */
    OssPreSignedUrlVo getPreSignedUploadUrl(PreSignedUrlRequestBo request);

    /**
     * 方案二：确认文件上传完成，保存文件信息到数据库
     *
     * @param confirmUploadBo 确认上传请求对象
     * @return 上传记录
     */
    LiveVideoUploadVo confirmUpload(ConfirmUploadBo confirmUploadBo);

//    /**
//     * 上传文件到对象存储服务，并保存文件信息到数据库
//     *
//     * @param file 要上传的文件对象
//     * @return 上传成功后的 SysOssVo 对象，包含文件信息
//     */
//    LiveVideoUploadVo upload(File file);


}
