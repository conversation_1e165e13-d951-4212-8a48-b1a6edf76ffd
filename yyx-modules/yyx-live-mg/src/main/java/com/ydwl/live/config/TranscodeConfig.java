package com.ydwl.live.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * 转码配置类
 * 提供转码服务相关的配置和Bean
 */
@Configuration
@ConfigurationProperties(prefix = "transcode")
public class TranscodeConfig {

    /**
     * 转码服务URL
     */
    private String serviceUrl = "http://localhost:8080/api/transcode";

    /**
     * 默认转码方式：fc(函数计算)/mts(媒体转码)/http(HTTP调用)
     */
    private String defaultMethod = "fc";

    /**
     * HTTP连接超时时间（毫秒）
     */
    private int connectTimeout = 5000;

    /**
     * HTTP读取超时时间（毫秒）
     */
    private int readTimeout = 30000;

    /**
     * 创建RestTemplate Bean
     * 用于HTTP方式调用转码服务
     *
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory());
        return restTemplate;
    }

    /**
     * 配置HTTP客户端工厂
     * 设置连接和读取超时时间
     *
     * @return ClientHttpRequestFactory实例
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        return factory;
    }

    // Getter和Setter方法
    public String getServiceUrl() {
        return serviceUrl;
    }

    public void setServiceUrl(String serviceUrl) {
        this.serviceUrl = serviceUrl;
    }

    public String getDefaultMethod() {
        return defaultMethod;
    }

    public void setDefaultMethod(String defaultMethod) {
        this.defaultMethod = defaultMethod;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
} 