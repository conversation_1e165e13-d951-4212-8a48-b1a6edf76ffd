package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveOrderVo;
import com.ydwl.live.domain.bo.LiveOrderBo;
import com.ydwl.live.service.ILiveOrderService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 订单
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/order")
public class LiveOrderController extends BaseController {

    private final ILiveOrderService liveOrderService;

    /**
     * 查询订单列表
     */
    @SaCheckPermission("live:order:list")
    @GetMapping("/list")
    public TableDataInfo<LiveOrderVo> list(LiveOrderBo bo, PageQuery pageQuery) {
        return liveOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单列表
     */
    @SaCheckPermission("live:order:export")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveOrderBo bo, HttpServletResponse response) {
        List<LiveOrderVo> list = liveOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单", LiveOrderVo.class, response);
    }

    /**
     * 获取订单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:order:query")
    @GetMapping("/{id}")
    public R<LiveOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveOrderService.queryById(id));
    }

    /**
     * 新增订单
     */
    @SaCheckPermission("live:order:add")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveOrderBo bo) {
        return toAjax(liveOrderService.insertByBo(bo));
    }

    /**
     * 修改订单
     */
    @SaCheckPermission("live:order:edit")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveOrderBo bo) {
        return toAjax(liveOrderService.updateByBo(bo));
    }

    /**
     * 删除订单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:order:remove")
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveOrderService.deleteWithValidByIds(List.of(ids), true));
    }
}
