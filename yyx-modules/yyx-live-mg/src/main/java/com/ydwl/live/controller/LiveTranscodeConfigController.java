package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.live.domain.vo.LiveTranscodeConfigVo;
import com.ydwl.live.domain.bo.LiveTranscodeConfigBo;
import com.ydwl.live.service.ILiveTranscodeConfigService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 转码配置
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/transcodeConfig")
public class LiveTranscodeConfigController extends BaseController {

    private final ILiveTranscodeConfigService liveTranscodeConfigService;

    /**
     * 查询转码配置列表
     */
    @SaCheckPermission("live:transcodeConfig:list")
    @GetMapping("/list")
    public TableDataInfo<LiveTranscodeConfigVo> list(LiveTranscodeConfigBo bo, PageQuery pageQuery) {
        return liveTranscodeConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取转码配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:transcodeConfig:query")
    @GetMapping("/{id}")
    public R<LiveTranscodeConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveTranscodeConfigService.queryById(id));
    }

    /**
     * 新增转码配置
     */
    @SaCheckPermission("live:transcodeConfig:add")
    @Log(title = "转码配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveTranscodeConfigBo bo) {
        return toAjax(liveTranscodeConfigService.insertByBo(bo));
    }

    /**
     * 修改转码配置
     */
    @SaCheckPermission("live:transcodeConfig:edit")
    @Log(title = "转码配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveTranscodeConfigBo bo) {
        return toAjax(liveTranscodeConfigService.updateByBo(bo));
    }

    /**
     * 删除转码配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:transcodeConfig:remove")
    @Log(title = "转码配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveTranscodeConfigService.deleteWithValidByIds(List.of(ids), true));
    }
}
