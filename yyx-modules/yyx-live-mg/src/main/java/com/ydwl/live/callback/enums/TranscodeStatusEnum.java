package com.ydwl.live.callback.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 转码任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum TranscodeStatusEnum {

    WAITING(0L, "等待中"),
    PROCESSING(1L, "处理中"),
    COMPLETED(2L, "已完成"),
    FAILED(3L, "失败");

    private final Long code;
    private final String desc;

    public static TranscodeStatusEnum getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (TranscodeStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
