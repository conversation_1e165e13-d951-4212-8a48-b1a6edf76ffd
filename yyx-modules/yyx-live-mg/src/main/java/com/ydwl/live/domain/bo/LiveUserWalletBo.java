package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveUserWallet;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 用户钱包业务对象 live_user_wallet
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveUserWallet.class, reverseConvertGenerate = false)
public class LiveUserWalletBo extends BaseEntity {

    /**
     * 钱包ID
     */
    @NotNull(message = "钱包ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 余额(单位:元)
     */
    @NotNull(message = "余额(单位:元)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long balance;

    /**
     * 累计充值(单位:元)
     */
    @NotNull(message = "累计充值(单位:元)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalRecharge;

    /**
     * 累计消费(单位:元)
     */
    @NotNull(message = "累计消费(单位:元)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalConsume;


}
