package com.ydwl.live.service;

import com.ydwl.common.core.domain.R;
import com.ydwl.live.domain.bo.LiveBo;
import com.ydwl.live.service.impl.LiveStateManager;

import java.util.Map;

/**
 * 直播数据服务接口
 * 整合直播信息、状态和推流信息的服务接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface ILiveDataService {

    /**
     * 获取直播详情，包含推流和播放信息
     *
     * @param liveId 直播ID
     * @return 直播详情数据
     */
    Map<String, Object> getLiveDetail(Long liveId);

    /**
     * 创建直播并自动生成推流信息
     *
     * @param bo 直播信息
     * @return 创建结果
     */
    R<Map<String, Object>> createLiveWithStream(LiveBo bo);

    /**
     * 刷新直播推流地址
     *
     * @param liveId 直播ID
     * @return 更新后的推流信息
     */
    R<Map<String, Object>> refreshLiveStream(Long liveId);

    /**
     * 更新直播状态
     *
     * @param liveId 直播ID
     * @param status 目标状态
     * @return 更新结果
     */
    R<Void> updateLiveStatus(Long liveId, LiveStateManager.LiveStatus status);
} 