package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveTranscodeTask;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 转码任务业务对象 live_transcode_task
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveTranscodeTask.class, reverseConvertGenerate = false)
public class LiveTranscodeTaskBo extends BaseEntity {

    /**
     * 转码任务ID
     */
    @NotNull(message = "转码任务ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskNo;

    /**
     * 任务类型(fc-函数计算,mts-媒体转码服务)
     */
    @NotBlank(message = "任务类型(fc-函数计算,mts-媒体转码服务)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskType;

    /**
     * 关联直播ID
     */
    @NotNull(message = "关联直播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long liveId;

    /**
     * 源文件地址
     */
    @NotBlank(message = "源文件地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sourceUrl;

    /**
     * 状态(0-待处理,1-处理中,2-已完成,3-已失败,4-已取消)
     */
    @NotNull(message = "状态(0-待处理,1-处理中,2-已完成,3-已失败,4-已取消)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * 转码模板ID
     */
    private Long templateId;

    /**
     * OSS存储桶名称
     */
    private String bucket;

    /**
     * OSS对象键名
     */
    private String objectKey;

    /**
     * 输出文件地址
     */
    private String outputUrl;

    /**
     * 视频时长(秒)
     */
    private Long duration;

    /**
     * 文件大小(byte)
     */
    private Long fileSize;

    /**
     * 转码进度(%)
     */
    private Long progress;

    /**
     * 业务ID(回调标识)
     */
    private String bizId;

    /**
     * 回调通知URL
     */
    private String callbackUrl;

    /**
     * 服务商任务ID (例如MTS/FC的JobID)
     */
    private String providerJobId;

    /**
     * 回调状态(0-未回调,1-已回调)
     */
    private Long callbackStatus;

    /**
     * 回调接收时间
     */
    private Date callbackTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 重试次数
     */
    private Long retryCount;

    /**
     * 优先级(1-10)
     */
    private Long priority;


}
    