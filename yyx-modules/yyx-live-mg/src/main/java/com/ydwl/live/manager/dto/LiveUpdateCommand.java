package com.ydwl.live.manager.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 更新直播命令
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Data
@Builder
public class LiveUpdateCommand {
    
    /**
     * 直播ID
     */
    private Long liveId;
    
    /**
     * 直播标题
     */
    private String title;
    
    /**
     * 直播封面图片URL
     */
    private String coverImgUrl;
    
    /**
     * 计划开始时间
     */
    private LocalDateTime planStartTime;
    
    /**
     * 直播描述
     */
    private String description;
    
    /**
     * 标签列表
     */
    private String tagList;
}
