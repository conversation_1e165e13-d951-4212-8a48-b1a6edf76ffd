package com.ydwl.live.controller;

import java.util.List;

import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveReplayVo;
import com.ydwl.live.domain.bo.LiveReplayBo;
import com.ydwl.live.service.ILiveReplayService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.live.service.IVideoProcessService;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 直播回放
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/replay")
public class LiveReplayController extends BaseController {

    private final ILiveReplayService liveReplayService;
    private final IVideoProcessService videoProcessService;


    /**
     * 测试字段翻译
     */
    @SaCheckPermission("live:replay:list")
    @GetMapping("/test")
    public LiveReplayVo list() {
        return liveReplayService.queryById(30L);
    }



    /**
     * 查询直播回放列表
     */
    @SaCheckPermission("live:replay:list")
    @GetMapping("/list")
    public TableDataInfo<LiveReplayVo> list(LiveReplayBo bo, PageQuery pageQuery) {
        return liveReplayService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出直播回放列表
     */
    @SaCheckPermission("live:replay:export")
    @Log(title = "直播回放", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveReplayBo bo, HttpServletResponse response) {
        List<LiveReplayVo> list = liveReplayService.queryList(bo);
        ExcelUtil.exportExcel(list, "直播回放", LiveReplayVo.class, response);
    }

    /**
     * 获取直播回放详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:replay:query")
    @GetMapping("/{id}")
    public R<LiveReplayVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveReplayService.queryById(id));
    }

    /**
     * 新增直播回放
     */
    @SaCheckPermission("live:replay:add")
    @Log(title = "直播回放", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveReplayBo bo) {
        return toAjax(liveReplayService.insertByBo(bo));
    }

    /**
     * 修改直播回放
     */
    @SaCheckPermission("live:replay:edit")
    @Log(title = "直播回放", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveReplayBo bo) {
        return toAjax(liveReplayService.updateByBo(bo));
    }

    /**
     * 删除直播回放
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:replay:remove")
    @Log(title = "直播回放", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveReplayService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 手动更新回放封面
     */
    @PostMapping("/update-cover/{id}")
    public R<Boolean> updateCover(@PathVariable("id") Long replayId, @RequestParam("coverUrl") String coverUrl) {
        if (StringUtils.isEmpty(coverUrl)) {
            return R.fail("封面URL不能为空");
        }

        try {
            // 获取回放记录
            LiveReplayVo replay = liveReplayService.queryById(replayId);
            if (replay == null) {
                return R.fail("未找到回放记录");
            }

            // 更新回放记录
            LiveReplayBo updateBo = new LiveReplayBo();
            updateBo.setId(replayId);
            updateBo.setCoverImgUrl(coverUrl);

            boolean updated = liveReplayService.updateByBo(updateBo);
            if (updated) {
                return R.ok(true);
            } else {
                return R.fail("更新封面失败");
            }

        } catch (Exception e) {
            log.error("更新封面异常", e);
            return R.fail("更新封面异常：" + e.getMessage());
        }
    }

    /**
     * 测试视频多帧截取功能
     */
    @GetMapping("/test-snapshots")
    public R<String> testVideoSnapshots(@RequestParam("objectKey") String objectKey,
                                       @RequestParam(value = "bucket", required = false) String bucket,
                                       @RequestParam(value = "intervalSeconds", defaultValue = "10") int intervalSeconds,
                                       @RequestParam(value = "maxFrames", required = false) Integer maxFrames) {
        if (StringUtils.isEmpty(objectKey)) {
            return R.fail("视频对象键不能为空");
        }

        if (StringUtils.isEmpty(bucket)) {
            bucket = "ydwl-live-recording";
        }

        try {
            // 生成输出路径前缀
            String outputPrefix = "snapshots/" + System.currentTimeMillis();

            // 提交多帧截图任务
            String taskId = videoProcessService.generateMultiFrameSnapshots(
                objectKey, bucket, outputPrefix, intervalSeconds, maxFrames);

            if (taskId == null) {
                return R.fail("提交截图任务失败");
            }

            return R.ok("提交截图任务成功，任务ID：" + taskId);
        } catch (Exception e) {
            log.error("视频多帧截取异常", e);
            return R.fail("视频多帧截取异常：" + e.getMessage());
        }
    }

    /**
     * 测试视频第一帧截图功能
     */
    @GetMapping("/test-first-frame")
    public R<String> testFirstFrameSnapshot(@RequestParam("objectKey") String objectKey,
                                          @RequestParam(value = "bucket", required = false) String bucket) {
        if (StringUtils.isEmpty(objectKey)) {
            return R.fail("视频对象键不能为空");
        }

        if (StringUtils.isEmpty(bucket)) {
            bucket = "video-ydwl";
        }

        try {
            // 生成输出路径前缀
            String outputPrefix = "cover/" + System.currentTimeMillis();

            // 提交第一帧截图任务
            String taskId = videoProcessService.generateFirstFrameSnapshot(
                objectKey, bucket, outputPrefix);

            if (taskId == null) {
                return R.fail("提交截图任务失败");
            }

            return R.ok("提交截图任务成功，任务ID：" + taskId);
        } catch (Exception e) {
            log.error("视频第一帧截图异常", e);
            return R.fail("视频第一帧截图异常：" + e.getMessage());
        }
    }

    /**
     * 测试视频关键帧截取功能
     */
    @GetMapping("/test-key-frames")
    public R<String> testKeyFrameSnapshots(@RequestParam("objectKey") String objectKey,
                                         @RequestParam(value = "bucket", required = false) String bucket,
                                         @RequestParam(value = "maxFrames", required = false, defaultValue = "10") Integer maxFrames) {
        if (StringUtils.isEmpty(objectKey)) {
            return R.fail("视频对象键不能为空");
        }

        if (StringUtils.isEmpty(bucket)) {
            bucket = "video-ydwl";
        }

        try {
            // 生成输出路径前缀
            String outputPrefix = "keyframes/" + System.currentTimeMillis();

            // 提交关键帧截图任务
            String taskId = videoProcessService.generateKeyFrameSnapshots(
                objectKey, bucket, outputPrefix, maxFrames, 800, 450);

            if (taskId == null) {
                return R.fail("提交关键帧截图任务失败");
            }

            return R.ok("提交关键帧截图任务成功，任务ID：" + taskId);
        } catch (Exception e) {
            log.error("视频关键帧截取异常", e);
            return R.fail("视频关键帧截取异常：" + e.getMessage());
        }
    }

    /**
     * 测试视频百分比位置截图功能
     */
    @GetMapping("/test-percentage-frames")
    public R<String> testPercentageSnapshots(@RequestParam("objectKey") String objectKey,
                                           @RequestParam(value = "bucket", required = false) String bucket) {
        if (StringUtils.isEmpty(objectKey)) {
            return R.fail("视频对象键不能为空");
        }

        if (StringUtils.isEmpty(bucket)) {
            bucket = "video-ydwl";
        }

        try {
            // 生成输出路径前缀
            String outputPrefix = "percentage/" + System.currentTimeMillis();

            // 设置百分比位置：开始、25%、50%、75%、结束
            int[] percentages = {0, 25, 50, 75, 100};

            // 提交百分比位置截图任务
            List<String> taskIds = videoProcessService.generatePercentageSnapshots(
                objectKey, bucket, outputPrefix, percentages);

            if (taskIds == null || taskIds.isEmpty()) {
                return R.fail("提交百分比位置截图任务失败");
            }

            return R.ok("提交百分比位置截图任务成功，成功任务数：" + taskIds.size());
        } catch (Exception e) {
            log.error("视频百分比位置截取异常", e);
            return R.fail("视频百分比位置截取异常：" + e.getMessage());
        }
    }

    /**
     * 测试自定义视频截帧功能
     */
    @GetMapping("/test-custom-frames")
    public R<String> testCustomSnapshots(@RequestParam("objectKey") String objectKey,
                                       @RequestParam(value = "bucket", required = false) String bucket,
                                       @RequestParam(value = "startTimeMs", required = false, defaultValue = "0") Integer startTimeMs,
                                       @RequestParam(value = "format", required = false, defaultValue = "jpg") String format,
                                       @RequestParam(value = "num", required = false, defaultValue = "5") Integer num,
                                       @RequestParam(value = "intervalMs", required = false, defaultValue = "5000") Integer intervalMs,
                                       @RequestParam(value = "width", required = false) Integer width,
                                       @RequestParam(value = "height", required = false) Integer height,
                                       @RequestParam(value = "scaleType", required = false, defaultValue = "stretch") String scaleType) {
        if (StringUtils.isEmpty(objectKey)) {
            return R.fail("视频对象键不能为空");
        }

        if (StringUtils.isEmpty(bucket)) {
            bucket = "video-ydwl";
        }

        try {
            // 生成输出路径前缀
            String outputPrefix = "custom/" + System.currentTimeMillis();

            // 提交自定义截图任务
            String taskId = videoProcessService.generateCustomSnapshots(
                objectKey, bucket, outputPrefix, startTimeMs, format, num, intervalMs, width, height, scaleType);

            if (taskId == null) {
                return R.fail("提交自定义截图任务失败");
            }

            return R.ok("提交自定义截图任务成功，任务ID：" + taskId);
        } catch (Exception e) {
            log.error("自定义视频截帧异常", e);
            return R.fail("自定义视频截帧异常：" + e.getMessage());
        }
    }
}
