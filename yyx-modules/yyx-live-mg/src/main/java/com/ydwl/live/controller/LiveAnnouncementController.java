package com.ydwl.live.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveAnnouncementVo;
import com.ydwl.live.domain.bo.LiveAnnouncementBo;
import com.ydwl.live.service.ILiveAnnouncementService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 直播公告管理控制器
 * 
 * <p>提供直播公告的完整管理功能，包括：</p>
 * <ul>
 *   <li>基础CRUD操作（增删改查）</li>
 *   <li>公告状态管理（发布、撤销、定时发布）</li>
 *   <li>按直播间查询有效公告</li>
 *   <li>公告统计和批量操作</li>
 * </ul>
 * 
 * <p><strong>权限控制：</strong></p>
 * <ul>
 *   <li>live:announcement:list - 查看公告列表</li>
 *   <li>live:announcement:query - 查看公告详情</li>
 *   <li>live:announcement:add - 新增公告</li>
 *   <li>live:announcement:edit - 编辑公告</li>
 *   <li>live:announcement:remove - 删除公告</li>
 *   <li>live:announcement:publish - 发布/撤销公告</li>
 * </ul>
 *
 * <AUTHOR> Yi
 * @version 2.1.4
 * @since 2025-05-21
 */
@Tag(name = "直播公告管理", description = "提供直播公告的增删改查和状态管理功能")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/announcement")
public class LiveAnnouncementController extends BaseController {

    private final ILiveAnnouncementService liveAnnouncementService;

    // ================== 基础CRUD操作 ==================

    /**
     * 分页查询直播公告列表
     * 
     * <p>支持多种查询条件的分页查询</p>
     *
     * @param bo 查询条件对象
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Operation(summary = "分页查询直播公告列表", description = "根据条件分页查询直播公告信息")
    @SaCheckPermission("live:announcement:list")
    @GetMapping("/list")
    public TableDataInfo<LiveAnnouncementVo> list(
            @Parameter(description = "查询条件") LiveAnnouncementBo bo, 
            @Parameter(description = "分页参数") PageQuery pageQuery) {
        return liveAnnouncementService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出直播公告列表到Excel
     * 
     * <p>根据查询条件导出公告数据</p>
     *
     * @param bo 查询条件
     * @param response HTTP响应对象
     */
    @Operation(summary = "导出直播公告列表", description = "将查询结果导出为Excel文件")
    @SaCheckPermission("live:announcement:export")
    @Log(title = "直播公告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(
            @Parameter(description = "查询条件") LiveAnnouncementBo bo, 
            HttpServletResponse response) {
        List<LiveAnnouncementVo> list = liveAnnouncementService.queryList(bo);
        ExcelUtil.exportExcel(list, "直播公告", LiveAnnouncementVo.class, response);
    }

    /**
     * 根据ID获取直播公告详细信息
     *
     * @param id 公告主键ID
     * @return 公告详细信息
     */
    @Operation(summary = "获取公告详情", description = "根据ID获取单个公告的详细信息")
    @SaCheckPermission("live:announcement:query")
    @GetMapping("/{id}")
    public R<LiveAnnouncementVo> getInfo(
            @Parameter(description = "公告ID", required = true)
            @NotNull(message = "主键不能为空")
            @PathVariable Long id) {
        return R.ok(liveAnnouncementService.queryById(id));
    }

    /**
     * 新增直播公告
     * 
     * <p>创建新的直播公告，默认状态为草稿</p>
     *
     * @param bo 公告信息
     * @return 操作结果
     */
    @Operation(summary = "新增直播公告", description = "创建新的直播公告")
    @SaCheckPermission("live:announcement:add")
    @Log(title = "直播公告", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(
            @Parameter(description = "公告信息", required = true)
            @Validated(AddGroup.class) @RequestBody LiveAnnouncementBo bo) {
        return toAjax(liveAnnouncementService.insertByBo(bo));
    }

    /**
     * 修改直播公告
     * 
     * <p>更新现有公告的信息</p>
     *
     * @param bo 公告信息
     * @return 操作结果
     */
    @Operation(summary = "修改直播公告", description = "更新现有公告的信息")
    @SaCheckPermission("live:announcement:edit")
    @Log(title = "直播公告", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(
            @Parameter(description = "公告信息", required = true)
            @Validated(EditGroup.class) @RequestBody LiveAnnouncementBo bo) {
        return toAjax(liveAnnouncementService.updateByBo(bo));
    }

    /**
     * 批量删除直播公告
     *
     * @param ids 要删除的公告ID数组
     * @return 操作结果
     */
    @Operation(summary = "删除直播公告", description = "批量删除指定的直播公告")
    @SaCheckPermission("live:announcement:remove")
    @Log(title = "直播公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(
            @Parameter(description = "公告ID数组", required = true)
            @NotEmpty(message = "主键不能为空")
            @PathVariable Long[] ids) {
        return toAjax(liveAnnouncementService.deleteWithValidByIds(List.of(ids), true));
    }

    // ================== 增强功能API ==================

    /**
     * 获取指定直播间的有效公告列表
     * 
     * <p>返回当前时间内有效的公告，按创建时间倒序</p>
     *
     * @param liveId 直播间ID
     * @return 有效公告列表
     */
    @Operation(summary = "获取直播间有效公告", description = "获取指定直播间当前有效的公告列表")
    @GetMapping("/active/{liveId}")
    public R<List<LiveAnnouncementVo>> getActiveAnnouncements(
            @Parameter(description = "直播间ID", required = true)
            @NotNull(message = "直播间ID不能为空")
            @PathVariable Long liveId) {
        List<LiveAnnouncementVo> announcements = liveAnnouncementService.getActiveAnnouncementsByLiveId(liveId);
        return R.ok(announcements);
    }

    /**
     * 立即发布公告
     * 
     * <p>将公告状态设置为上线，立即生效且永久有效</p>
     *
     * @param announcementId 公告ID
     * @return 操作结果
     */
    @Operation(summary = "立即发布公告", description = "立即发布指定公告，永久有效")
    @SaCheckPermission("live:announcement:publish")
    @Log(title = "发布公告", businessType = BusinessType.UPDATE)
    @PostMapping("/{announcementId}/publish")
    public R<Void> publishNow(
            @Parameter(description = "公告ID", required = true)
            @NotNull(message = "公告ID不能为空")
            @PathVariable Long announcementId) {
        return toAjax(liveAnnouncementService.publishAnnouncementNow(announcementId));
    }

    /**
     * 定时发布公告
     * 
     * <p>设置公告在指定时间发布，可设置持续时长</p>
     *
     * @param announcementId 公告ID
     * @param scheduledTime 定时发布时间
     * @param duration 持续时长（分钟），为null表示永久有效
     * @return 操作结果
     */
    @Operation(summary = "定时发布公告", description = "设置公告定时发布")
    @SaCheckPermission("live:announcement:publish")
    @Log(title = "定时发布公告", businessType = BusinessType.UPDATE)
    @PostMapping("/{announcementId}/schedule")
    public R<Void> schedulePublish(
            @Parameter(description = "公告ID", required = true)
            @NotNull(message = "公告ID不能为空")
            @PathVariable Long announcementId,
            
            @Parameter(description = "定时发布时间", required = true)
            @NotNull(message = "定时发布时间不能为空")
            @RequestParam LocalDateTime scheduledTime,
            
            @Parameter(description = "持续时长（分钟）")
            @RequestParam(required = false) Integer duration) {
        return toAjax(liveAnnouncementService.scheduleAnnouncement(announcementId, scheduledTime, duration));
    }

    /**
     * 撤销公告
     * 
     * <p>将公告状态设置为下线</p>
     *
     * @param announcementId 公告ID
     * @return 操作结果
     */
    @Operation(summary = "撤销公告", description = "撤销指定公告，设置为下线状态")
    @SaCheckPermission("live:announcement:publish")
    @Log(title = "撤销公告", businessType = BusinessType.UPDATE)
    @PostMapping("/{announcementId}/unpublish")
    public R<Void> unpublish(
            @Parameter(description = "公告ID", required = true)
            @NotNull(message = "公告ID不能为空")
            @PathVariable Long announcementId) {
        return toAjax(liveAnnouncementService.unpublishAnnouncement(announcementId));
    }

    /**
     * 获取最新公告
     * 
     * <p>获取指定直播间最新发布的公告</p>
     *
     * @param liveId 直播间ID
     * @param limit 返回数量限制
     * @return 最新公告列表
     */
    @Operation(summary = "获取最新公告", description = "获取指定直播间最新的公告")
    @GetMapping("/latest/{liveId}")
    public R<List<LiveAnnouncementVo>> getLatestAnnouncements(
            @Parameter(description = "直播间ID", required = true)
            @NotNull(message = "直播间ID不能为空")
            @PathVariable Long liveId,
            
            @Parameter(description = "返回数量限制，默认为1")
            @RequestParam(defaultValue = "1") Integer limit) {
        List<LiveAnnouncementVo> announcements = liveAnnouncementService.getLatestAnnouncements(liveId, limit);
        return R.ok(announcements);
    }

    /**
     * 检查公告是否有效
     * 
     * <p>检查指定公告是否在当前时间有效</p>
     *
     * @param announcementId 公告ID
     * @return 是否有效
     */
    @Operation(summary = "检查公告有效性", description = "检查指定公告是否在当前时间有效")
    @GetMapping("/{announcementId}/active")
    public R<Boolean> isActive(
            @Parameter(description = "公告ID", required = true)
            @NotNull(message = "公告ID不能为空")
            @PathVariable Long announcementId) {
        Boolean isActive = liveAnnouncementService.isAnnouncementActive(announcementId);
        return R.ok(isActive);
    }

    /**
     * 复制公告到其他直播间
     * 
     * <p>将一个公告复制到多个直播间</p>
     *
     * @param sourceAnnouncementId 源公告ID
     * @param targetLiveIds 目标直播间ID列表
     * @return 复制成功的数量
     */
    @Operation(summary = "复制公告", description = "将公告复制到其他直播间")
    @SaCheckPermission("live:announcement:add")
    @Log(title = "复制公告", businessType = BusinessType.INSERT)
    @PostMapping("/{sourceAnnouncementId}/copy")
    public R<Integer> copyToLives(
            @Parameter(description = "源公告ID", required = true)
            @NotNull(message = "源公告ID不能为空")
            @PathVariable Long sourceAnnouncementId,
            
            @Parameter(description = "目标直播间ID列表", required = true)
            @NotEmpty(message = "目标直播间列表不能为空")
            @RequestBody List<Long> targetLiveIds) {
        Integer successCount = liveAnnouncementService.copyAnnouncementToLives(sourceAnnouncementId, targetLiveIds);
        return R.ok(successCount);
    }

    /**
     * 获取公告统计信息
     * 
     * <p>返回指定直播间的公告统计数据</p>
     *
     * @param liveId 直播间ID
     * @return 统计信息
     */
    @Operation(summary = "获取公告统计", description = "获取指定直播间的公告统计信息")
    @SaCheckPermission("live:announcement:list")
    @GetMapping("/statistics/{liveId}")
    public R<Map<String, Object>> getStatistics(
            @Parameter(description = "直播间ID", required = true)
            @NotNull(message = "直播间ID不能为空")
            @PathVariable Long liveId) {
        Map<String, Object> statistics = liveAnnouncementService.getAnnouncementStatistics(liveId);
        return R.ok(statistics);
    }

    /**
     * 更新过期公告状态
     * 
     * <p>定时任务调用，批量更新过期公告的状态</p>
     *
     * @return 更新的公告数量
     */
    @Operation(summary = "更新过期公告", description = "批量更新过期公告状态（定时任务调用）")
    @SaCheckPermission("live:announcement:edit")
    @Log(title = "更新过期公告", businessType = BusinessType.UPDATE)
    @PostMapping("/update-expired")
    public R<Integer> updateExpired() {
        Integer updateCount = liveAnnouncementService.updateExpiredAnnouncements();
        return R.ok(updateCount);
    }
}
