package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 直播公告对象 live_announcement
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_announcement")
public class LiveAnnouncement extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 公告ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 直播ID
     */
    private Long liveId;

    /**
     * 公告内容
     */
    private String content;

    /**
     * 生效时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 状态(0-下线,1-上线)
     */
    private Long announcementStatus;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
