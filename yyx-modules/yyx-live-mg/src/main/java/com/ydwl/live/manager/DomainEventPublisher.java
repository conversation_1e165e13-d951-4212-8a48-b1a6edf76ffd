package com.ydwl.live.manager;

import com.ydwl.live.domain.model.valueobject.DomainEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 领域事件发布器
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DomainEventPublisher {
    
    private final ApplicationEventPublisher applicationEventPublisher;
    
    /**
     * 发布领域事件
     */
    public void publish(DomainEvent event) {
        log.info("发布领域事件: type={}, eventId={}, aggregateId={}", 
                event.getEventType(), event.getEventId(), event.getAggregateId().getValue());
        
        applicationEventPublisher.publishEvent(event);
    }
}
