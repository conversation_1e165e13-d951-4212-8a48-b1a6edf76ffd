package com.ydwl.live.service.impl;

import com.ydwl.live.domain.bo.LiveReplayBo;
import com.ydwl.live.domain.vo.LiveReplayVo;
import com.ydwl.live.service.ILiveReplayService;
import com.ydwl.live.service.IVideoProcessService;
import com.ydwl.live.util.AliyunOssVideoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 视频处理服务实现类
 * 整合了各种视频处理功能，包括封面图生成、多帧截图等
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VideoProcessServiceImpl implements IVideoProcessService {

    private final AliyunOssVideoUtil aliyunOssVideoUtil;
    private final ILiveReplayService liveReplayService;




    @Override
    public String generateMultiFrameSnapshots(String objectKey, String bucket, String outputPrefix,
                                           int intervalSeconds, Integer maxFrames) {
        try {
            if (StringUtils.isEmpty(objectKey) || StringUtils.isEmpty(bucket) || StringUtils.isEmpty(outputPrefix)) {
                log.error("生成多帧截图失败：参数不完整");
                return null;
            }

            // 调用阿里云OSS视频处理服务截取多帧图片
            String taskId = aliyunOssVideoUtil.submitIntervalFrameSnapshotJob(
                objectKey, bucket, outputPrefix, intervalSeconds, "jpg", maxFrames);
            if (taskId == null) {
                log.error("生成多帧截图失败：提交截图任务失败，objectKey: {}", objectKey);
                return null;
            }

            log.info("生成多帧截图任务提交成功，taskId: {}, objectKey: {}", taskId, objectKey);
            return taskId;
        } catch (Exception e) {
            log.error("生成多帧截图异常", e);
            return null;
        }
    }

    @Override
    public String generateFirstFrameSnapshot(String objectKey, String bucket, String outputPrefix) {
        try {
            log.info("[截图排查] 开始生成第一帧截图，参数检查 - objectKey: {}, bucket: {}, outputPrefix: {}",
                     objectKey, bucket, outputPrefix);

            if (StringUtils.isEmpty(objectKey)) {
                log.error("[截图排查] 生成第一帧截图失败：对象键为空");
                return null;
            }

            if (StringUtils.isEmpty(bucket)) {
                log.error("[截图排查] 生成第一帧截图失败：存储桶为空");
                return null;
            }

            if (StringUtils.isEmpty(outputPrefix)) {
                log.error("[截图排查] 生成第一帧截图失败：输出前缀为空");
                return null;
            }

            // 调用阿里云OSS视频处理服务截取第一帧图片
            String predictedCoverUrl = aliyunOssVideoUtil.submitFirstFrameSnapshotJob(
                objectKey, bucket, outputPrefix, "jpg", 800, 450);
            // 把https://video-ydwl.oss-cn-beijing.aliyuncs.com/替换成https://mps.play.ycyyx.com/
            if (predictedCoverUrl != null) {
                predictedCoverUrl = predictedCoverUrl.replace("https://video-ydwl.oss-cn-beijing.aliyuncs.com/", "https://mps.play.ycyyx.com/");
            }

            return predictedCoverUrl; // 返回预测的URL
        } catch (Exception e) {
            log.error("[截图排查] 生成第一帧截图异常，详细错误：", e);
            return null;
        }
    }

    @Override
    public boolean updateReplayCoverImage(Long liveId, String coverImgUrl) {
        try {
            if (liveId == null || StringUtils.isEmpty(coverImgUrl)) {
                log.error("更新回放封面图失败：参数不完整");
                return false;
            }

            // 查询回放记录
            LiveReplayBo queryBo = new LiveReplayBo();
            queryBo.setLiveId(liveId);
            List<LiveReplayVo> replays = liveReplayService.queryList(queryBo);

            if (replays.isEmpty()) {
                log.warn("未找到回放记录，无法更新封面图，liveId: {}", liveId);
                return false;
            }

            // 更新回放记录
            LiveReplayBo updateBo = new LiveReplayBo();
            updateBo.setId(replays.get(0).getId());
            updateBo.setCoverImgUrl(coverImgUrl);

            boolean updated = liveReplayService.updateByBo(updateBo);
            if (updated) {
                log.info("成功更新回放封面图，liveId: {}, coverUrl: {}", liveId, coverImgUrl);
                return true;
            } else {
                log.error("更新回放封面图失败，liveId: {}", liveId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新回放封面图异常", e);
            return false;
        }
    }

    @Override
    public String generateKeyFrameSnapshots(String objectKey, String bucket, String outputPrefix,
                                          Integer maxFrames, Integer width, Integer height) {
        try {
            log.info("开始生成关键帧截图，参数: objectKey={}, bucket={}, outputPrefix={}, maxFrames={}, width={}, height={}",
                    objectKey, bucket, outputPrefix, maxFrames, width, height);

            if (StringUtils.isEmpty(objectKey) || StringUtils.isEmpty(bucket) || StringUtils.isEmpty(outputPrefix)) {
                log.error("生成关键帧截图失败：参数不完整");
                return null;
            }

            // 调用阿里云OSS视频处理服务提取关键帧
            String taskId = aliyunOssVideoUtil.submitKeyFrameSnapshotJob(
                objectKey, bucket, outputPrefix, "jpg", maxFrames, width, height, "stretch");

            if (taskId == null) {
                log.error("生成关键帧截图失败：提交截图任务失败，objectKey: {}", objectKey);
                return null;
            }

            log.info("生成关键帧截图任务提交成功，taskId: {}, objectKey: {}", taskId, objectKey);
            return taskId;
        } catch (Exception e) {
            log.error("生成关键帧截图异常", e);
            return null;
        }
    }

    @Override
    public List<String> generatePercentageSnapshots(String objectKey, String bucket, String outputPrefix, int[] percentages) {
        try {
            log.info("开始生成百分比位置截图，参数: objectKey={}, bucket={}, outputPrefix={}, percentages={}",
                   objectKey, bucket, outputPrefix, percentages);

            if (StringUtils.isEmpty(objectKey) || StringUtils.isEmpty(bucket) || StringUtils.isEmpty(outputPrefix) || percentages == null || percentages.length == 0) {
                log.error("生成百分比位置截图失败：参数不完整");
                return null;
            }

            // 调用阿里云OSS视频处理服务按百分比位置截图
            List<String> taskIds = aliyunOssVideoUtil.submitPercentageSnapshotJobs(
                objectKey, bucket, outputPrefix, percentages, "jpg", 800, 450);

            if (taskIds == null || taskIds.isEmpty()) {
                log.error("生成百分比位置截图失败：提交截图任务失败，objectKey: {}", objectKey);
                return null;
            }

            log.info("生成百分比位置截图任务提交成功，成功任务数: {}, objectKey: {}", taskIds.size(), objectKey);
            return taskIds;
        } catch (Exception e) {
            log.error("生成百分比位置截图异常", e);
            return null;
        }
    }

    @Override
    public String generateCustomSnapshots(String objectKey, String bucket, String outputPrefix,
                                        int startTimeMs, String format, Integer num, Integer intervalMs,
                                        Integer width, Integer height, String scaleType) {
        try {
            log.info("开始生成自定义截图，参数: objectKey={}, bucket={}, outputPrefix={}, startTimeMs={}, format={}, num={}, intervalMs={}, width={}, height={}, scaleType={}",
                    objectKey, bucket, outputPrefix, startTimeMs, format, num, intervalMs, width, height, scaleType);

            if (StringUtils.isEmpty(objectKey) || StringUtils.isEmpty(bucket) || StringUtils.isEmpty(outputPrefix)) {
                log.error("生成自定义截图失败：参数不完整");
                return null;
            }

            // 调用阿里云OSS视频处理服务生成自定义截图
            String taskId = aliyunOssVideoUtil.submitMultiFrameSnapshotJob(
                objectKey, bucket, outputPrefix, startTimeMs, format, num, intervalMs, width, height, scaleType);

            if (taskId == null) {
                log.error("生成自定义截图失败：提交截图任务失败，objectKey: {}", objectKey);
                return null;
            }

            log.info("生成自定义截图任务提交成功，taskId: {}, objectKey: {}", taskId, objectKey);
            return taskId;
        } catch (Exception e) {
            log.error("生成自定义截图异常", e);
            return null;
        }
    }
}
