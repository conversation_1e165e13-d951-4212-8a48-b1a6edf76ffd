package com.ydwl.live.domain.bo;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 获取预签名上传URL请求对象
 *
 * <AUTHOR> Yi
 */
@Data
public class PreSignedUrlRequestBo {

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空")
    private Long liveId;

    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    /**
     * 文件类型
     */
    @NotBlank(message = "文件类型不能为空")
    private String contentType;

    /**
     * 最大文件大小（字节，可选，默认2GB）
     */
    private Long maxFileSize;

    /**
     * 视频时长（秒）
     */
    private Double videoDurationSeconds;

    /**
     * 视频分辨率
     */
    private String videoResolution;

    /**
     * 视频比特率（Kbps）
     */
    private Integer videoBitrateKbps;

    /**
     * 帧率
     */
    private Double frameRate;

    /**
     * 视频编码格式
     */
    private String videoCodec;

    /**
     * 音频编码格式
     */
    private String audioCodec;

    /**
     * 宽高比
     */
    private String aspectRatio;

    /**
     * 创建日期
     */
    private String createdDate;

    /**
     * 最后修改日期
     */
    private String lastModifiedDate;
} 