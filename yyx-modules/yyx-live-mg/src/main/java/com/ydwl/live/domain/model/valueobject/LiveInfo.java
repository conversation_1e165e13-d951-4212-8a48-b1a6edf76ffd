package com.ydwl.live.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 直播基本信息值对象
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
@EqualsAndHashCode
public class LiveInfo {
    
    /**
     * 直播分类ID
     */
    private final Long categoryId;
    
    /**
     * 直播标题
     */
    private final String title;
    
    /**
     * 直播封面图片URL
     */
    private final String coverImgUrl;
    
    /**
     * 计划开始时间
     */
    private final LocalDateTime planStartTime;
    
    /**
     * 实际开始时间
     */
    private final LocalDateTime actualStartTime;
    
    /**
     * 实际结束时间
     */
    private final LocalDateTime actualEndTime;
    
    /**
     * 实际直播时长(分钟)
     */
    private final Long durationMinutes;
    
    /**
     * 直播描述
     */
    private final String description;
    
    /**
     * 标签列表
     */
    private final String tagList;
    
    private LiveInfo(Long categoryId, String title, String coverImgUrl, 
                    LocalDateTime planStartTime, LocalDateTime actualStartTime,
                    LocalDateTime actualEndTime, Long durationMinutes,
                    String description, String tagList) {
        this.categoryId = categoryId;
        this.title = title;
        this.coverImgUrl = coverImgUrl;
        this.planStartTime = planStartTime;
        this.actualStartTime = actualStartTime;
        this.actualEndTime = actualEndTime;
        this.durationMinutes = durationMinutes;
        this.description = description;
        this.tagList = tagList;
    }
    
    /**
     * 创建直播基本信息
     */
    public static LiveInfo create(Long categoryId, String title, String coverImgUrl,
                                 LocalDateTime planStartTime, String description, String tagList) {
        return new LiveInfo(categoryId, title, coverImgUrl, planStartTime, 
                           null, null, null, description, tagList);
    }
    
    /**
     * 设置实际开始时间
     */
    public LiveInfo withActualStartTime(LocalDateTime actualStartTime) {
        return new LiveInfo(categoryId, title, coverImgUrl, planStartTime,
                           actualStartTime, actualEndTime, durationMinutes, description, tagList);
    }
    
    /**
     * 设置实际结束时间
     */
    public LiveInfo withActualEndTime(LocalDateTime actualEndTime) {
        return new LiveInfo(categoryId, title, coverImgUrl, planStartTime,
                           actualStartTime, actualEndTime, durationMinutes, description, tagList);
    }
    
    /**
     * 设置直播时长
     */
    public LiveInfo withDurationMinutes(Long durationMinutes) {
        return new LiveInfo(categoryId, title, coverImgUrl, planStartTime,
                           actualStartTime, actualEndTime, durationMinutes, description, tagList);
    }
    
    /**
     * 更新基本信息
     */
    public LiveInfo update(String title, String coverImgUrl, LocalDateTime planStartTime,
                          String description, String tagList) {
        return new LiveInfo(categoryId, title, coverImgUrl, planStartTime,
                           actualStartTime, actualEndTime, durationMinutes, description, tagList);
    }
}
