package com.ydwl.live.manager.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 创建直播命令
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Data
@Builder
public class LiveCreateCommand {
    
    /**
     * 直播分类ID
     */
    private Long categoryId;
    
    /**
     * 直播标题
     */
    private String title;
    
    /**
     * 直播封面图片URL
     */
    private String coverImgUrl;
    
    /**
     * 计划开始时间
     */
    private LocalDateTime planStartTime;
    
    /**
     * 直播描述
     */
    private String description;
    
    /**
     * 标签列表
     */
    private String tagList;
    
    /**
     * 是否需要报名
     */
    private boolean signupRequired;
    
    /**
     * 是否启用回放
     */
    private boolean replayEnabled;
    
    /**
     * 是否自动录制
     */
    private boolean autoRecord;
    
    /**
     * 是否启用聊天
     */
    private boolean chatEnabled;
    
    /**
     * 聊天延迟时间(秒)
     */
    private Long chatDelay;
    
    /**
     * 是否启用礼物
     */
    private boolean giftEnabled;
    
    /**
     * 默认画质
     */
    private String defaultQuality;
    
    /**
     * 访问权限级别
     */
    private Long accessLevel;
}
