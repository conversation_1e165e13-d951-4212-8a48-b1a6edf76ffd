package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveReplayBo;
import com.ydwl.live.domain.vo.LiveReplayVo;
import com.ydwl.live.domain.LiveReplay;
import com.ydwl.live.mapper.LiveReplayMapper;
import com.ydwl.live.service.ILiveReplayService;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;
/**
 * 直播回放Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LiveReplayServiceImpl implements ILiveReplayService {

    private final LiveReplayMapper baseMapper;

    /**
     * 查询直播回放
     *
     * @param id 主键
     * @return 直播回放
     */
    @Override
    public LiveReplayVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询直播回放列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播回放分页列表
     */
    @Override
    public TableDataInfo<LiveReplayVo> queryPageList(LiveReplayBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveReplay> lqw = buildQueryWrapper(bo);
        Page<LiveReplayVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 处理回放URL，添加动态签名
        List<LiveReplayVo> processedList = result.getRecords().stream()
            .map(this::matchingUrl)
            .collect(Collectors.toList());

        result.setRecords(processedList);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的直播回放列表
     *
     * @param bo 查询条件
     * @return 直播回放列表
     */
    @Override
    public List<LiveReplayVo> queryList(LiveReplayBo bo) {
        LambdaQueryWrapper<LiveReplay> lqw = buildQueryWrapper(bo);
        List<LiveReplayVo> list = baseMapper.selectVoList(lqw);

        // 处理回放URL，添加动态签名
        return list.stream()
            .map(this::matchingUrl)
            .collect(Collectors.toList());
    }

    /**
     * 为回放URL添加动态签名
     *
     * @param replay 回放对象
     * @return 处理后的回放对象
     */
    private LiveReplayVo matchingUrl(LiveReplayVo replay) {
        if (replay == null || StringUtils.isEmpty(replay.getReplayUrl())) {
            return replay;
        }

        try {
            // 提取完整的文件路径，不仅仅是文件名
            String objectPath = extractObjectPathFromUrl(replay.getReplayUrl());
            if (StringUtils.isNotEmpty(objectPath)) {
                String accessKeyId = "LTAI5tGA15ASheoXwvc7pYcN";
                String accessKeySecret = "******************************";
                String bucketName = "video-ydwl";
                String endpoint = "oss-cn-beijing.aliyuncs.com";

                // 设置过期时间 - 24小时后
                long expirationTimeInSeconds = System.currentTimeMillis() / 1000 + Duration.ofHours(24).toSeconds();

                // 使用正确的格式构建资源路径，没有斜杠前缀
                String resourcePath = "/" + bucketName + "/" + objectPath;

                if (objectPath.toLowerCase().endsWith(".m3u8")) {
                    // 为m3u8文件构建带hls/sign处理的URL
                    String queryString = "x-oss-process=hls/sign";
                    String stringToSign = "GET\n\n\n" + expirationTimeInSeconds + "\n" + resourcePath + "?" + queryString;

                    String signature = calculateSignature(stringToSign, accessKeySecret);
                    String encodedSignature = java.net.URLEncoder.encode(signature, "UTF-8");

                    String finalUrl = String.format("https://%s.%s/%s?OSSAccessKeyId=%s&Expires=%d&Signature=%s&%s",
                        bucketName, endpoint, objectPath, accessKeyId, expirationTimeInSeconds, encodedSignature, queryString);

                    replay.setReplayUrl(finalUrl);
                    log.info("生成m3u8签名URL: {}, 原始路径: {}", replay.getReplayUrl(), objectPath);
                } else {
                    // 普通文件，不需要额外处理参数
                    String stringToSign = "GET\n\n\n" + expirationTimeInSeconds + "\n" + resourcePath;

                    String signature = calculateSignature(stringToSign, accessKeySecret);
                    String encodedSignature = java.net.URLEncoder.encode(signature, "UTF-8");

                    String finalUrl = String.format("https://%s.%s/%s?OSSAccessKeyId=%s&Expires=%d&Signature=%s",
                        bucketName, endpoint, objectPath, accessKeyId, expirationTimeInSeconds, encodedSignature);

                    replay.setReplayUrl(finalUrl);
                    log.info("生成普通签名URL: {}, 原始路径: {}", replay.getReplayUrl(), objectPath);
                }
            }
        } catch (Exception e) {
            log.error("处理回放URL动态签名失败: {}", e.getMessage(), e);
            // 出错时保留原始URL
        }

        return replay;
    }

    /**
     * 计算OSS签名
     *
     * @param stringToSign 待签名字符串
     * @param accessKeySecret 访问密钥密码
     * @return 计算出的签名（Base64编码）
     * @throws Exception 如果签名过程发生错误
     */
    private String calculateSignature(String stringToSign, String accessKeySecret) throws Exception {
        // 使用HMAC-SHA1算法计算签名
        javax.crypto.Mac mac = javax.crypto.Mac.getInstance("HmacSHA1");
        javax.crypto.spec.SecretKeySpec keySpec = new javax.crypto.spec.SecretKeySpec(
            accessKeySecret.getBytes(java.nio.charset.StandardCharsets.UTF_8), "HmacSHA1");
        mac.init(keySpec);
        byte[] signatureBytes = mac.doFinal(stringToSign.getBytes(java.nio.charset.StandardCharsets.UTF_8));

        // Base64编码
        return java.util.Base64.getEncoder().encodeToString(signatureBytes);
    }

    /**
     * 从URL中提取OSS对象路径
     *
     * @param url 完整URL
     * @return 对象路径
     */
    private String extractObjectPathFromUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }

        try {
            // 移除URL参数
            String baseUrl = url.contains("?") ? url.substring(0, url.indexOf("?")) : url;

            // 解析URL以获取路径部分
            java.net.URL parsedUrl = new java.net.URL(baseUrl);
            String path = parsedUrl.getPath();

            // 如果路径以/开头，则移除
            if (path.startsWith("/")) {
                path = path.substring(1);
            }

            log.debug("从URL [{}] 提取的对象路径: [{}]", url, path);
            return path;
        } catch (Exception e) {
            log.warn("解析URL失败: {}, 将使用简单提取方法", url);

            // 尝试简单地从URL中提取文件路径
            // 首先找到域名后的第一个/
            int domainEnd = url.indexOf("//");
            if (domainEnd >= 0) {
                domainEnd = url.indexOf("/", domainEnd + 2);
                if (domainEnd >= 0) {
                    String path = url.substring(domainEnd + 1);
                    // 移除URL参数
                    if (path.contains("?")) {
                        path = path.substring(0, path.indexOf("?"));
                    }
                    log.debug("简单方式提取的对象路径: [{}]", path);
                    return path;
                }
            }

            // 如果上述方法失败，保留原始文件名提取逻辑作为后备
            String fileName = extractFileNameFromUrl(url);
            log.debug("后备方式提取的对象路径(仅文件名): [{}]", fileName);
            return fileName;
        }
    }

    /**
     * 从URL中提取文件名 (保留此方法以向后兼容)
     *
     * @param url 完整URL
     * @return 文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }

        // 移除URL参数
        String baseUrl = url.contains("?") ? url.substring(0, url.indexOf("?")) : url;

        // 获取最后一个斜杠后的内容作为文件名
        int lastSlashIndex = baseUrl.lastIndexOf("/");
        if (lastSlashIndex >= 0 && lastSlashIndex < baseUrl.length() - 1) {
            return baseUrl.substring(lastSlashIndex + 1);
        }

        return baseUrl;
    }

    private LambdaQueryWrapper<LiveReplay> buildQueryWrapper(LiveReplayBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveReplay> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveReplay::getId);
        lqw.eq(bo.getLiveId() != null, LiveReplay::getLiveId, bo.getLiveId());
        lqw.eq(bo.getTranscodeTaskId() != null, LiveReplay::getTranscodeTaskId, bo.getTranscodeTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getReplayUrl()), LiveReplay::getReplayUrl, bo.getReplayUrl());
        lqw.eq(bo.getStatus() != null, LiveReplay::getStatus, bo.getStatus());
        lqw.eq(bo.getDuration() != null, LiveReplay::getDuration, bo.getDuration());
        lqw.eq(bo.getAvailableTime() != null, LiveReplay::getAvailableTime, bo.getAvailableTime());
        lqw.eq(bo.getExpiryTime() != null, LiveReplay::getExpiryTime, bo.getExpiryTime());
        lqw.eq(bo.getAccessType() != null, LiveReplay::getAccessType, bo.getAccessType());
        lqw.eq(bo.getViewCount() != null, LiveReplay::getViewCount, bo.getViewCount());
        return lqw;
    }

    /**
     * 新增直播回放
     *
     * @param bo 直播回放
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveReplayBo bo) {
        LiveReplay add = MapstructUtils.convert(bo, LiveReplay.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改直播回放
     *
     * @param bo 直播回放
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveReplayBo bo) {
        LiveReplay update = MapstructUtils.convert(bo, LiveReplay.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveReplay entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除直播回放信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
