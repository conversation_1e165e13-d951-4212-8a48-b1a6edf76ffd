package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 转码输出文件对象 live_transcode_output
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_transcode_output")
public class LiveTranscodeOutput extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 输出ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 转码任务ID
     */
    private Long taskId;

    /**
     * 清晰度标识(SD-标清,HD-高清,FHD-超清)
     */
    private String definition;

    /**
     * 分辨率(如:1920x1080)
     */
    private String resolution;

    /**
     * 输出格式(mp4,hls等)
     */
    private String format;

    /**
     * 输出文件地址
     */
    private String url;

    /**
     * 分片时长(秒)
     */
    private Long segmentTime;

    /**
     * 文件大小(byte)
     */
    private Long fileSize;

    /**
     * 实际码率(kbps)
     */
    private Long bitRate;

    /**
     * 时长(秒)
     */
    private Long duration;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
