package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveTranscodeTaskVo;
import com.ydwl.live.domain.bo.LiveTranscodeTaskBo;
import com.ydwl.live.service.ILiveTranscodeTaskService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 转码任务
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/transcodeTask")
public class LiveTranscodeTaskController extends BaseController {

    private final ILiveTranscodeTaskService liveTranscodeTaskService;

    /**
     * 查询转码任务列表
     */
    @SaCheckPermission("live:transcodeTask:list")
    @GetMapping("/list")
    public TableDataInfo<LiveTranscodeTaskVo> list(LiveTranscodeTaskBo bo, PageQuery pageQuery) {
        return liveTranscodeTaskService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出转码任务列表
     */
    @SaCheckPermission("live:transcodeTask:export")
    @Log(title = "转码任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveTranscodeTaskBo bo, HttpServletResponse response) {
        List<LiveTranscodeTaskVo> list = liveTranscodeTaskService.queryList(bo);
        ExcelUtil.exportExcel(list, "转码任务", LiveTranscodeTaskVo.class, response);
    }

    /**
     * 获取转码任务详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:transcodeTask:query")
    @GetMapping("/{id}")
    public R<LiveTranscodeTaskVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveTranscodeTaskService.queryById(id));
    }

    /**
     * 新增转码任务
     */
    @SaCheckPermission("live:transcodeTask:add")
    @Log(title = "转码任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveTranscodeTaskBo bo) {
        return toAjax(liveTranscodeTaskService.insertByBo(bo));
    }

    /**
     * 修改转码任务
     */
    @SaCheckPermission("live:transcodeTask:edit")
    @Log(title = "转码任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveTranscodeTaskBo bo) {
        return toAjax(liveTranscodeTaskService.updateByBo(bo));
    }

    /**
     * 删除转码任务
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:transcodeTask:remove")
    @Log(title = "转码任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveTranscodeTaskService.deleteWithValidByIds(List.of(ids), true));
    }
}
