package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveTranscodeOutput;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 转码输出文件业务对象 live_transcode_output
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveTranscodeOutput.class, reverseConvertGenerate = false)
public class LiveTranscodeOutputBo extends BaseEntity {

    /**
     * 输出ID
     */
    @NotNull(message = "输出ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 转码任务ID
     */
    @NotNull(message = "转码任务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long taskId;

    /**
     * 业务ID (与 LiveTranscodeTask中的bizId一致，冗余用于查询)
     */
    private String bizId;

    /**
     * 关联直播ID (与 LiveTranscodeTask中的liveId一致，冗余用于查询)
     */
    private Long liveId;

    /**
     * 服务商使用的模板ID
     */
    private String providerTemplateId;

    /**
     * 清晰度标识(SD-标清,HD-高清,FHD-超清)
     */
    @NotBlank(message = "清晰度标识(SD-标清,HD-高清,FHD-超清)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String definition;

    /**
     * 分辨率(如:1920x1080)
     */
    @NotBlank(message = "分辨率(如:1920x1080)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String resolution;

    /**
     * 输出格式(mp4,hls等)
     */
    @NotBlank(message = "输出格式(mp4,hls等)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String format;

    /**
     * 输出文件地址
     */
    @NotBlank(message = "输出文件地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String url;

    /**
     * 分片时长(秒)
     */
    private Long segmentTime;

    /**
     * 文件大小(byte)
     */
    private Long fileSize;

    /**
     * 实际码率(kbps)
     */
    private Long bitRate;

    /**
     * 时长(毫秒)
     */
    private Long duration;

    /**
     * 输出状态 (1-成功, 0-失败, 2-处理中) - 根据回调中的具体输出项状态设置
     */
    private Long status;

}
