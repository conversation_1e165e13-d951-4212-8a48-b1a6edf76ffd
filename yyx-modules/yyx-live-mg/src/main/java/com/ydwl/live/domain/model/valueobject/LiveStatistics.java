package com.ydwl.live.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 直播统计信息值对象
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
@EqualsAndHashCode
public class LiveStatistics {
    
    /**
     * 观看人数
     */
    private final Long viewCount;
    
    /**
     * 点赞数
     */
    private final Long likeCount;
    
    /**
     * 分享数
     */
    private final Long shareCount;
    
    /**
     * 最大同时在线人数
     */
    private final Long maxOnlineCount;
    
    /**
     * 当前在线人数
     */
    private final Long currentOnlineCount;
    
    private LiveStatistics(Long viewCount, Long likeCount, Long shareCount, 
                          Long maxOnlineCount, Long currentOnlineCount) {
        this.viewCount = viewCount;
        this.likeCount = likeCount;
        this.shareCount = shareCount;
        this.maxOnlineCount = maxOnlineCount;
        this.currentOnlineCount = currentOnlineCount;
    }
    
    /**
     * 创建初始统计信息
     */
    public static LiveStatistics initial() {
        return new LiveStatistics(0L, 0L, 0L, 0L, 0L);
    }
    
    /**
     * 从现有数据创建
     */
    public static LiveStatistics of(Long viewCount, Long likeCount, Long shareCount, 
                                   Long maxOnlineCount, Long currentOnlineCount) {
        return new LiveStatistics(viewCount, likeCount, shareCount, maxOnlineCount, currentOnlineCount);
    }
    
    /**
     * 更新观看统计
     */
    public LiveStatistics updateView(Long newViewCount, Long newOnlineCount) {
        Long newMaxOnlineCount = Math.max(this.maxOnlineCount, newOnlineCount);
        return new LiveStatistics(newViewCount, likeCount, shareCount, newMaxOnlineCount, newOnlineCount);
    }
    
    /**
     * 更新互动统计
     */
    public LiveStatistics updateInteraction(Long newLikeCount, Long newShareCount) {
        return new LiveStatistics(viewCount, newLikeCount, newShareCount, maxOnlineCount, currentOnlineCount);
    }
    
    /**
     * 增加点赞数
     */
    public LiveStatistics addLike() {
        return new LiveStatistics(viewCount, likeCount + 1, shareCount, maxOnlineCount, currentOnlineCount);
    }
    
    /**
     * 增加分享数
     */
    public LiveStatistics addShare() {
        return new LiveStatistics(viewCount, likeCount, shareCount + 1, maxOnlineCount, currentOnlineCount);
    }
    
    /**
     * 更新在线人数
     */
    public LiveStatistics updateOnlineCount(Long onlineCount) {
        Long newMaxOnlineCount = Math.max(this.maxOnlineCount, onlineCount);
        return new LiveStatistics(viewCount, likeCount, shareCount, newMaxOnlineCount, onlineCount);
    }
    
    /**
     * 获取总互动数
     */
    public Long getTotalInteractions() {
        return likeCount + shareCount;
    }
    
    /**
     * 获取参与度（互动数/观看数）
     */
    public Double getEngagementRate() {
        if (viewCount == 0) {
            return 0.0;
        }
        return (double) getTotalInteractions() / viewCount;
    }
}
