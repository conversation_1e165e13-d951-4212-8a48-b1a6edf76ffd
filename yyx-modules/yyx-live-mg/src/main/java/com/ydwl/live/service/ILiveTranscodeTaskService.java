package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveTranscodeTaskVo;
import com.ydwl.live.domain.bo.LiveTranscodeTaskBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 转码任务Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveTranscodeTaskService {

    /**
     * 查询转码任务
     *
     * @param id 主键
     * @return 转码任务
     */
    LiveTranscodeTaskVo queryById(Long id);

    /**
     * 分页查询转码任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转码任务分页列表
     */
    TableDataInfo<LiveTranscodeTaskVo> queryPageList(LiveTranscodeTaskBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的转码任务列表
     *
     * @param bo 查询条件
     * @return 转码任务列表
     */
    List<LiveTranscodeTaskVo> queryList(LiveTranscodeTaskBo bo);

    /**
     * 新增转码任务
     *
     * @param bo 转码任务
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveTranscodeTaskBo bo);

    /**
     * 修改转码任务
     *
     * @param bo 转码任务
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveTranscodeTaskBo bo);

    /**
     * 校验并批量删除转码任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据业务ID查询转码任务
     *
     * @param bizId 业务ID
     * @return 转码任务
     */
    LiveTranscodeTaskVo queryByBizId(String bizId);
}
