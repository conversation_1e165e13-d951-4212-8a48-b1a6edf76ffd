package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveUserWalletVo;
import com.ydwl.live.domain.bo.LiveUserWalletBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 用户钱包Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveUserWalletService {

    /**
     * 查询用户钱包
     *
     * @param id 主键
     * @return 用户钱包
     */
    LiveUserWalletVo queryById(Long id);

    /**
     * 分页查询用户钱包列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户钱包分页列表
     */
    TableDataInfo<LiveUserWalletVo> queryPageList(LiveUserWalletBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户钱包列表
     *
     * @param bo 查询条件
     * @return 用户钱包列表
     */
    List<LiveUserWalletVo> queryList(LiveUserWalletBo bo);

    /**
     * 新增用户钱包
     *
     * @param bo 用户钱包
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveUserWalletBo bo);

    /**
     * 修改用户钱包
     *
     * @param bo 用户钱包
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveUserWalletBo bo);

    /**
     * 校验并批量删除用户钱包信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
