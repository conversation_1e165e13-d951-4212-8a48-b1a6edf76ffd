package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveBannedUser;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 直播禁言用户业务对象 live_banned_user
 *
 * <AUTHOR> <PERSON>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveBannedUser.class, reverseConvertGenerate = false)
public class LiveBannedUserBo extends BaseEntity {

    /**
     * 禁言记录ID
     */
    @NotNull(message = "禁言记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long liveId;

    /**
     * 被禁言用户ID
     */
    @NotNull(message = "被禁言用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 禁言原因
     */
    private String banReason;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 禁言时间
     */
    @NotNull(message = "禁言时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date banTime;

    /**
     * 解除禁言时间
     */
    private Date unbanTime;

    /**
     * 禁言状态(0-已解除,1-生效中)
     */
    @NotNull(message = "禁言状态(0-已解除,1-生效中)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long banStatus;


}
