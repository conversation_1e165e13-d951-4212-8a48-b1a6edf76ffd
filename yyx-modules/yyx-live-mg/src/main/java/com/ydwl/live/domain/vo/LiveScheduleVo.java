package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.live.domain.LiveSchedule;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 直播预告视图对象 live_schedule
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveSchedule.class)
public class LiveScheduleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 预告ID
     */
    @ExcelProperty(value = "预告ID")
    private Long id;

    /**
     * 预告标题
     */
    @ExcelProperty(value = "预告标题")
    private String title;

    /**
     * 主播ID
     */
    @ExcelProperty(value = "主播ID")
    private Long hostUserId;

    /**
     * 分类ID
     */
    @ExcelProperty(value = "分类ID")
    private Long categoryId;

    /**
     * 预告描述
     */
    @ExcelProperty(value = "预告描述")
    private String description;

    /**
     * 预告封面URL
     */
    @ExcelProperty(value = "预告封面URL")
    private String coverImgUrl;

    /**
     * 预定时间
     */
    @ExcelProperty(value = "预定时间")
    private Date scheduledTime;

    /**
     * 预计时长(单位:分钟)
     */
    @ExcelProperty(value = "预计时长(单位:分钟)")
    private Long estimatedDurationMinutes;

    /**
     * 状态(0-已取消,1-待开播)
     */
    @ExcelProperty(value = "状态(0-已取消,1-待开播)")
    private Long scheduleStatus;


}
