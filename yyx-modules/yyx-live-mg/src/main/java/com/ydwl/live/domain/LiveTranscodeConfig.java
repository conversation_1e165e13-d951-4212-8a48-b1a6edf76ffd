package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 转码配置对象 live_transcode_config
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_transcode_config")
public class LiveTranscodeConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 配置键
     */
    @TableField(value = "`key`")
    private String key;

    /**
     * 配置值
     */
    @TableField(value = "`value`")
    private String value;

    /**
     * 配置类型(fc-函数计算,mts-媒体转码)
     */
    @TableField(value = "`type`")
    private String type;

    /**
     * 说明
     */
    private String description;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
