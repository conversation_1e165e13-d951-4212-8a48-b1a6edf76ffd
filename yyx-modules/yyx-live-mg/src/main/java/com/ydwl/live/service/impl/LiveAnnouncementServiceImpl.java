package com.ydwl.live.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ydwl.live.exception.LiveException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ydwl.live.domain.bo.LiveAnnouncementBo;
import com.ydwl.live.domain.vo.LiveAnnouncementVo;
import com.ydwl.live.domain.LiveAnnouncement;
import com.ydwl.live.mapper.LiveAnnouncementMapper;
import com.ydwl.live.service.ILiveAnnouncementService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 直播公告Service业务层处理
 * 
 * <p>提供直播公告的完整业务逻辑处理，包括：</p>
 * <ul>
 *   <li>基础的CRUD操作</li>
 *   <li>公告状态管理（发布、撤销、定时发布）</li>
 *   <li>按直播间查询有效公告</li>
 *   <li>公告过期处理和统计功能</li>
 * </ul>
 * 
 * <p><strong>设计特点：</strong></p>
 * <ul>
 *   <li>支持公告的生命周期管理</li>
 *   <li>提供实时公告查询功能</li>
 *   <li>支持批量操作和统计分析</li>
 *   <li>完善的异常处理和数据校验</li>
 * </ul>
 *
 * <AUTHOR> Yi
 * @version 2.1.4
 * @since 2025-05-21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LiveAnnouncementServiceImpl implements ILiveAnnouncementService {

    /**
     * 公告数据访问对象
     */
    private final LiveAnnouncementMapper baseMapper;

    // ================== 基础CRUD方法 ==================

    /**
     * 根据主键查询直播公告
     *
     * @param id 主键
     * @return 直播公告视图对象
     */
    @Override
    public LiveAnnouncementVo queryById(Long id){
        if (id == null) {
            log.warn("查询公告时传入的ID为空");
            throw new IllegalArgumentException("公告ID不能为空");
        }
        
        log.debug("查询公告信息，ID: {}", id);
        LiveAnnouncementVo result = baseMapper.selectVoById(id);
        
        if (result == null) {
            log.info("未找到ID为{}的公告信息", id);
        } else {
            log.debug("成功查询到公告信息，ID: {}", id);
        }
        
        return result;
    }

    /**
     * 分页查询直播公告列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播公告分页列表
     */
    @Override
    public TableDataInfo<LiveAnnouncementVo> queryPageList(LiveAnnouncementBo bo, PageQuery pageQuery) {
        if (pageQuery == null) {
            log.warn("分页查询公告时分页参数为空");
            throw new IllegalArgumentException("分页参数不能为空");
        }
        
        log.debug("分页查询公告，页码: {}, 页大小: {}", pageQuery.getPageNum(), pageQuery.getPageSize());
        
        LambdaQueryWrapper<LiveAnnouncement> lqw = buildQueryWrapper(bo);
        Page<LiveAnnouncementVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        
        log.debug("分页查询完成，总数: {}, 当前页数据量: {}", result.getTotal(), result.getRecords().size());
        
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的直播公告列表
     *
     * @param bo 查询条件
     * @return 直播公告列表
     */
    @Override
    public List<LiveAnnouncementVo> queryList(LiveAnnouncementBo bo) {
        log.debug("查询公告列表，直播ID: {}", bo != null ? bo.getLiveId() : "全部");
        
        LambdaQueryWrapper<LiveAnnouncement> lqw = buildQueryWrapper(bo);
        List<LiveAnnouncementVo> result = baseMapper.selectVoList(lqw);
        
        log.debug("查询完成，共找到{}条公告", result.size());
        
        return result;
    }

    /**
     * 构建动态查询条件
     */
    private LambdaQueryWrapper<LiveAnnouncement> buildQueryWrapper(LiveAnnouncementBo bo) {
        Map<String, Object> params = bo != null ? bo.getParams() : null;
        LambdaQueryWrapper<LiveAnnouncement> lqw = Wrappers.lambdaQuery();
        
        // 默认按创建时间倒序排列，最新的在前
        lqw.orderByDesc(LiveAnnouncement::getCreateTime);
        
        if (bo != null) {
            lqw.eq(bo.getLiveId() != null, LiveAnnouncement::getLiveId, bo.getLiveId());
            lqw.like(StringUtils.isNotBlank(bo.getContent()), LiveAnnouncement::getContent, bo.getContent());
            lqw.eq(bo.getStartTime() != null, LiveAnnouncement::getStartTime, bo.getStartTime());
            lqw.eq(bo.getEndTime() != null, LiveAnnouncement::getEndTime, bo.getEndTime());
            lqw.eq(bo.getAnnouncementStatus() != null, LiveAnnouncement::getAnnouncementStatus, bo.getAnnouncementStatus());
            
            log.debug("构建查询条件完成，直播ID: {}, 状态: {}", bo.getLiveId(), bo.getAnnouncementStatus());
        }
        
        return lqw;
    }

    /**
     * 新增直播公告
     *
     * @param bo 直播公告
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(LiveAnnouncementBo bo) {
        if (bo == null) {
            log.warn("新增公告时业务对象为空");
            throw new IllegalArgumentException("公告信息不能为空");
        }
        
        log.debug("新增公告，直播ID: {}", bo.getLiveId());
        
        LiveAnnouncement add = MapstructUtils.convert(bo, LiveAnnouncement.class);
        
        // 数据校验
        validEntityBeforeSave(add);
        
        boolean flag = baseMapper.insert(add) > 0;
        
        if (flag) {
            bo.setId(add.getId());
            log.info("成功新增公告，ID: {}, 直播ID: {}", add.getId(), bo.getLiveId());
        } else {
            log.error("新增公告失败，直播ID: {}", bo.getLiveId());
        }
        
        return flag;
    }

    /**
     * 修改直播公告
     *
     * @param bo 直播公告
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(LiveAnnouncementBo bo) {
        if (bo == null || bo.getId() == null) {
            log.warn("修改公告时业务对象为空或ID为空");
            throw new IllegalArgumentException("公告信息和ID不能为空");
        }
        
        log.debug("修改公告，ID: {}", bo.getId());
        
        LiveAnnouncement update = MapstructUtils.convert(bo, LiveAnnouncement.class);
        
        // 数据校验
        validEntityBeforeSave(update);
        
        boolean flag = baseMapper.updateById(update) > 0;
        
        if (flag) {
            log.info("成功修改公告，ID: {}", bo.getId());
        } else {
            log.warn("修改公告失败或记录不存在，ID: {}", bo.getId());
        }
        
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveAnnouncement entity){
        if (entity == null) {
            throw new IllegalArgumentException("公告实体不能为空");
        }
        
        // 校验直播ID
        if (entity.getLiveId() == null) {
            throw LiveException.validationError("liveId", "null", "直播ID不能为空");
        }
        
        // 校验公告内容
        if (StringUtils.isBlank(entity.getContent())) {
            throw LiveException.validationError("content", entity.getContent(), "公告内容不能为空");
        }
        
        // 校验时间范围
        if (entity.getStartTime() != null && entity.getEndTime() != null) {
            if (entity.getEndTime().before(entity.getStartTime())) {
                throw LiveException.validationError("timeRange", 
                    entity.getStartTime() + " ~ " + entity.getEndTime(), 
                    "结束时间不能早于开始时间");
            }
        }
        
        log.debug("公告数据校验通过，实体ID: {}", entity.getId());
    }

    /**
     * 校验并批量删除直播公告信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量删除公告时ID集合为空");
            throw new IllegalArgumentException("删除的ID集合不能为空");
        }
        
        log.debug("批量删除公告，数量: {}, 是否校验: {}", ids.size(), isValid);
        
        if(Boolean.TRUE.equals(isValid)){
            // 执行删除前的业务校验
            validBeforeDelete(ids);
        }
        
        boolean flag = baseMapper.deleteByIds(ids) > 0;
        
        if (flag) {
            log.info("成功删除{}条公告", ids.size());
        } else {
            log.warn("删除公告失败或记录不存在，ID集合: {}", ids);
        }
        
        return flag;
    }
    
    /**
     * 删除前的业务校验
     */
    private void validBeforeDelete(Collection<Long> ids) {
        // 检查是否有正在生效的公告
        List<LiveAnnouncement> activeAnnouncements = baseMapper.selectBatchIds(ids).stream()
            .filter(announcement -> announcement.getAnnouncementStatus() != null && 
                    announcement.getAnnouncementStatus().equals(1L))
            .collect(Collectors.toList());
        
        if (!activeAnnouncements.isEmpty()) {
            log.warn("存在正在生效的公告，建议先撤销后再删除: {}", 
                activeAnnouncements.stream().map(LiveAnnouncement::getId).collect(Collectors.toList()));
            // 这里可以选择是否强制阻止删除，根据业务需求决定
        }
        
        log.debug("执行删除前校验，ID数量: {}", ids.size());
    }

    // ================== 增强的业务方法 ==================

    /**
     * 获取指定直播间的有效公告列表
     */
    @Override
    public List<LiveAnnouncementVo> getActiveAnnouncementsByLiveId(Long liveId) {
        if (liveId == null) {
            throw new IllegalArgumentException("直播ID不能为空");
        }
        
        log.debug("获取直播间有效公告，直播ID: {}", liveId);
        
        Date now = new Date();
        
        LambdaQueryWrapper<LiveAnnouncement> lqw = Wrappers.lambdaQuery();
        lqw.eq(LiveAnnouncement::getLiveId, liveId)
           .eq(LiveAnnouncement::getAnnouncementStatus, 1L) // 状态为上线
           .and(wrapper -> wrapper
               .le(LiveAnnouncement::getStartTime, now) // 开始时间 <= 当前时间
               .or()
               .isNull(LiveAnnouncement::getStartTime) // 或者开始时间为空
           )
           .and(wrapper -> wrapper
               .ge(LiveAnnouncement::getEndTime, now) // 结束时间 >= 当前时间
               .or()
               .isNull(LiveAnnouncement::getEndTime) // 或者结束时间为空（永久有效）
           )
           .orderByDesc(LiveAnnouncement::getCreateTime);
        
        List<LiveAnnouncementVo> result = baseMapper.selectVoList(lqw);
        
        log.debug("获取有效公告完成，直播ID: {}, 公告数量: {}", liveId, result.size());
        
        return result;
    }

    /**
     * 发布公告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean publishAnnouncement(Long announcementId, LocalDateTime startTime, LocalDateTime endTime) {
        if (announcementId == null) {
            throw new IllegalArgumentException("公告ID不能为空");
        }
        
        log.debug("发布公告，ID: {}, 开始时间: {}, 结束时间: {}", announcementId, startTime, endTime);
        
        // 检查公告是否存在
        LiveAnnouncement announcement = baseMapper.selectById(announcementId);
        if (announcement == null) {
            throw LiveException.liveNotFound(announcementId);
        }
        
        // 转换LocalDateTime为Date
        Date startDate = startTime != null ? Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()) : null;
        Date endDate = endTime != null ? Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()) : null;
        
        // 更新公告状态和时间
        LambdaUpdateWrapper<LiveAnnouncement> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(LiveAnnouncement::getId, announcementId)
                    .set(LiveAnnouncement::getAnnouncementStatus, 1L) // 设置为上线状态
                    .set(startDate != null, LiveAnnouncement::getStartTime, startDate)
                    .set(endDate != null, LiveAnnouncement::getEndTime, endDate);
        
        boolean flag = baseMapper.update(null, updateWrapper) > 0;
        
        if (flag) {
            log.info("成功发布公告，ID: {}", announcementId);
        } else {
            log.error("发布公告失败，ID: {}", announcementId);
        }
        
        return flag;
    }

    /**
     * 撤销公告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unpublishAnnouncement(Long announcementId) {
        if (announcementId == null) {
            throw new IllegalArgumentException("公告ID不能为空");
        }
        
        log.debug("撤销公告，ID: {}", announcementId);
        
        // 检查公告是否存在
        LiveAnnouncement announcement = baseMapper.selectById(announcementId);
        if (announcement == null) {
            throw LiveException.liveNotFound(announcementId);
        }
        
        // 更新公告状态为下线
        LambdaUpdateWrapper<LiveAnnouncement> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(LiveAnnouncement::getId, announcementId)
                    .set(LiveAnnouncement::getAnnouncementStatus, 0L); // 设置为下线状态
        
        boolean flag = baseMapper.update(null, updateWrapper) > 0;
        
        if (flag) {
            log.info("成功撤销公告，ID: {}", announcementId);
        } else {
            log.error("撤销公告失败，ID: {}", announcementId);
        }
        
        return flag;
    }

    /**
     * 立即发布公告
     */
    @Override
    public Boolean publishAnnouncementNow(Long announcementId) {
        return publishAnnouncement(announcementId, LocalDateTime.now(), null);
    }

    /**
     * 定时发布公告
     */
    @Override
    public Boolean scheduleAnnouncement(Long announcementId, LocalDateTime scheduledTime, Integer duration) {
        LocalDateTime endTime = null;
        if (duration != null && duration > 0) {
            endTime = scheduledTime.plusMinutes(duration);
        }
        
        return publishAnnouncement(announcementId, scheduledTime, endTime);
    }

    /**
     * 获取最新的公告内容
     */
    @Override
    public List<LiveAnnouncementVo> getLatestAnnouncements(Long liveId, Integer limit) {
        if (liveId == null) {
            throw new IllegalArgumentException("直播ID不能为空");
        }
        
        int queryLimit = (limit != null && limit > 0) ? limit : 1;
        
        log.debug("获取最新公告，直播ID: {}, 数量限制: {}", liveId, queryLimit);
        
        LambdaQueryWrapper<LiveAnnouncement> lqw = Wrappers.lambdaQuery();
        lqw.eq(LiveAnnouncement::getLiveId, liveId)
           .eq(LiveAnnouncement::getAnnouncementStatus, 1L) // 只查询已发布的
           .orderByDesc(LiveAnnouncement::getCreateTime)
           .last("LIMIT " + queryLimit);
        
        List<LiveAnnouncementVo> result = baseMapper.selectVoList(lqw);
        
        log.debug("获取最新公告完成，数量: {}", result.size());
        
        return result;
    }

    /**
     * 检查公告是否在有效期内
     */
    @Override
    public Boolean isAnnouncementActive(Long announcementId) {
        if (announcementId == null) {
            return false;
        }
        
        Date now = new Date();
        
        LambdaQueryWrapper<LiveAnnouncement> lqw = Wrappers.lambdaQuery();
        lqw.eq(LiveAnnouncement::getId, announcementId)
           .eq(LiveAnnouncement::getAnnouncementStatus, 1L)
           .and(wrapper -> wrapper
               .le(LiveAnnouncement::getStartTime, now)
               .or()
               .isNull(LiveAnnouncement::getStartTime)
           )
           .and(wrapper -> wrapper
               .ge(LiveAnnouncement::getEndTime, now)
               .or()
               .isNull(LiveAnnouncement::getEndTime)
           );
        
        return baseMapper.exists(lqw);
    }

    /**
     * 批量更新过期公告状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateExpiredAnnouncements() {
        Date now = new Date();
        
        log.debug("开始更新过期公告状态，当前时间: {}", now);
        
        LambdaUpdateWrapper<LiveAnnouncement> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(LiveAnnouncement::getAnnouncementStatus, 1L) // 当前是上线状态
                    .lt(LiveAnnouncement::getEndTime, now) // 结束时间小于当前时间
                    .isNotNull(LiveAnnouncement::getEndTime) // 结束时间不为空
                    .set(LiveAnnouncement::getAnnouncementStatus, 0L); // 设置为下线状态
        
        int count = baseMapper.update(null, updateWrapper);
        
        log.info("更新过期公告完成，更新数量: {}", count);
        
        return count;
    }

    /**
     * 复制公告到其他直播间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer copyAnnouncementToLives(Long sourceAnnouncementId, List<Long> targetLiveIds) {
        if (sourceAnnouncementId == null || targetLiveIds == null || targetLiveIds.isEmpty()) {
            throw new IllegalArgumentException("源公告ID和目标直播间ID列表不能为空");
        }
        
        log.debug("复制公告到其他直播间，源公告ID: {}, 目标直播间数量: {}", sourceAnnouncementId, targetLiveIds.size());
        
        // 获取源公告
        LiveAnnouncement sourceAnnouncement = baseMapper.selectById(sourceAnnouncementId);
        if (sourceAnnouncement == null) {
            throw LiveException.liveNotFound(sourceAnnouncementId);
        }
        
        int successCount = 0;
        
        for (Long targetLiveId : targetLiveIds) {
            try {
                LiveAnnouncement newAnnouncement = new LiveAnnouncement();
                // 复制所有字段，除了ID和直播ID
                newAnnouncement.setLiveId(targetLiveId);
                newAnnouncement.setContent(sourceAnnouncement.getContent());
                newAnnouncement.setStartTime(sourceAnnouncement.getStartTime());
                newAnnouncement.setEndTime(sourceAnnouncement.getEndTime());
                newAnnouncement.setAnnouncementStatus(0L); // 新复制的公告默认为下线状态
                
                if (baseMapper.insert(newAnnouncement) > 0) {
                    successCount++;
                    log.debug("成功复制公告到直播间，目标直播ID: {}", targetLiveId);
                }
            } catch (Exception e) {
                log.error("复制公告到直播间失败，目标直播ID: {}, 错误: {}", targetLiveId, e.getMessage());
            }
        }
        
        log.info("公告复制完成，成功数量: {}, 总数量: {}", successCount, targetLiveIds.size());
        
        return successCount;
    }

    /**
     * 获取公告统计信息
     */
    @Override
    public Map<String, Object> getAnnouncementStatistics(Long liveId) {
        if (liveId == null) {
            throw new IllegalArgumentException("直播ID不能为空");
        }
        
        log.debug("获取公告统计信息，直播ID: {}", liveId);
        
        // 总数
        LambdaQueryWrapper<LiveAnnouncement> totalWrapper = Wrappers.lambdaQuery();
        totalWrapper.eq(LiveAnnouncement::getLiveId, liveId);
        Long totalCount = baseMapper.selectCount(totalWrapper);
        
        // 已发布数量
        LambdaQueryWrapper<LiveAnnouncement> publishedWrapper = Wrappers.lambdaQuery();
        publishedWrapper.eq(LiveAnnouncement::getLiveId, liveId)
                       .eq(LiveAnnouncement::getAnnouncementStatus, 1L);
        Long publishedCount = baseMapper.selectCount(publishedWrapper);
        
        // 有效数量（当前时间范围内）
        Date now = new Date();
        LambdaQueryWrapper<LiveAnnouncement> activeWrapper = Wrappers.lambdaQuery();
        activeWrapper.eq(LiveAnnouncement::getLiveId, liveId)
                    .eq(LiveAnnouncement::getAnnouncementStatus, 1L)
                    .and(wrapper -> wrapper
                        .le(LiveAnnouncement::getStartTime, now)
                        .or()
                        .isNull(LiveAnnouncement::getStartTime)
                    )
                    .and(wrapper -> wrapper
                        .ge(LiveAnnouncement::getEndTime, now)
                        .or()
                        .isNull(LiveAnnouncement::getEndTime)
                    );
        Long activeCount = baseMapper.selectCount(activeWrapper);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCount", totalCount);
        statistics.put("publishedCount", publishedCount);
        statistics.put("activeCount", activeCount);
        statistics.put("draftCount", totalCount - publishedCount);
        
        log.debug("公告统计信息: {}", statistics);
        
        return statistics;
    }
}
