package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 交易记录对象 live_transaction
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_transaction")
public class LiveTransaction extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关联ID（订单/礼物记录）
     */
    private Long relatedId;

    /**
     * 交易类型（1-充值，2-消费，3-收入，4-退款）
     */
    private Long type;

    /**
     * 交易金额
     */
    private Long amount;

    /**
     * 交易后余额
     */
    private Long balance;

    /**
     * 交易详情
     */
    private String detail;

    /**
     * 目标类型
     */
    private Long targetType;

    /**
     * 目标ID
     */
    private Long targetId;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;


}
