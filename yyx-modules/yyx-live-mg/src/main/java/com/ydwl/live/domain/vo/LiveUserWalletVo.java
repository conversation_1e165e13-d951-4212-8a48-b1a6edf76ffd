package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveUserWallet;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用户钱包视图对象 live_user_wallet
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveUserWallet.class)
public class LiveUserWalletVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 钱包ID
     */
    @ExcelProperty(value = "钱包ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 余额(单位:元)
     */
    @ExcelProperty(value = "余额(单位:元)")
    private Long balance;

    /**
     * 累计充值(单位:元)
     */
    @ExcelProperty(value = "累计充值(单位:元)")
    private Long totalRecharge;

    /**
     * 累计消费(单位:元)
     */
    @ExcelProperty(value = "累计消费(单位:元)")
    private Long totalConsume;


}
