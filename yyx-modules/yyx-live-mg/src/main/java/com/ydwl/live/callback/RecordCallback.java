//package com.ydwl.live.callback;
//
//
//import com.esotericsoftware.minlog.Log;
//import com.ydwl.common.sse.dto.SseMessageDto;
//import com.ydwl.common.sse.utils.SseMessageUtils;
//import com.ydwl.live.callback.dto.OnDemandRecordCallbackDto;
//import com.ydwl.live.callback.vo.OnDemandRecordCallbackVo;
//import com.ydwl.live.domain.bo.LiveBo;
//import com.ydwl.live.service.ILiveService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//import java.util.*;
//
//
///**
// * 录制回调
// */
//@RequestMapping("/live/Record/call-back")
//@RestController
//@RequiredArgsConstructor
//public class RecordCallback {
//    private final ILiveService liveService;
//    /**
//     * 推流状态回调
//     * 推流开始 结束 异常 都会回调
//     */
//    @GetMapping("/streamStart")
//    public void onDemandRecordStart(         @RequestParam List<String> appname,
//                                             @RequestParam List<String> app,
//                                             @RequestParam List<String> action,
//                                             @RequestParam List<String> time,
//                                             @RequestParam List<String> id,
//                                             @RequestParam List<String> usrargs) throws Exception {
//        try {
//            if (action == null) {
//                return;
//            }
//            String s1 = action.get(0);
//            if (Objects.equals(s1, "publish")) {
//                SseMessageDto msg = new SseMessageDto();
//                msg.setMessage("推流开始了 - 流ID: " + id);
//                SseMessageUtils.publishMessage(msg);
//            } else if (s1.equals("publish_done")) {
//                SseMessageDto s = new SseMessageDto();
//                s.setMessage("推流断开了 - 流ID: " + id);
//                SseMessageUtils.publishMessage(s);
//            } else {
//                Log.info("未知的状态: " + action);
//            }
//        } catch (Exception e) {
//            Log.error("处理回调参数失败: " + e.getMessage());
//        }
//    }
//    /**
//     * 按需录制回调
//     * @return OnDemandRecordCallbackVo
//     */
//    @RequestMapping("/onDemandRecord")
//    public OnDemandRecordCallbackVo DemandRecordCallback(OnDemandRecordCallbackDto dto) throws Exception {
//        LiveBo liveBo = liveService.queryByIdToBo(Long.valueOf(dto.getStream()));
//        boolean needRecord = liveBo != null && liveBo.getStatus() == 1 && liveBo.getAutoRecord() == 1;  // 状态为直播中 且 自动录制为开启 才录制
//        OnDemandRecordCallbackVo onDemandRecordCallbackVo = new OnDemandRecordCallbackVo();
//        onDemandRecordCallbackVo.setNeedRecord(needRecord);
//        Log.info("录制状态回调: " + needRecord);
//        return onDemandRecordCallbackVo;
//    }
//
//    /**
//     * 录制状态回调
//     * 处理录制开始、暂停、结束等状态
//     */
//    @PostMapping("/recordStatus")
//    public void recordStatus(@RequestBody Map<String, Object> params) throws Exception {
//        try {
//            String stream = (String) params.get("stream");
//            // 检查是否是录制结束回调（通过检查是否存在uri字段）
//            if (params.containsKey("uri")) {
//                String uri = (String) params.get("uri");
//                Integer startTime = (Integer) params.get("start_time");
//                Integer stopTime = (Integer) params.get("stop_time");
//                String fileUrl = " https://ydwl-live-recording.oss-cn-beijing.aliyuncs.com" + "/" + uri;
//                // 更新数据库中的录制状态
//                LiveBo liveBo = new LiveBo();
//                liveBo.setId(Long.valueOf(stream));
//                liveBo.setOssAddress(fileUrl);
//                liveService.updateByBo(liveBo);
//                SseMessageDto s = new SseMessageDto();
//                s.setMessage("录制结束 文件已经存入OSS - 流ID: " + stream);
//                SseMessageUtils.publishMessage(s);
//                return;
//            }
//            // 处理其他状态回调
//            String event = (String) params.get("event");
//            if (event == null) {
//                Log.info("录制事件类型为空");
//                return;
//            }
//            switch (event) {
//                case "record_started":
//                    SseMessageDto s = new SseMessageDto();
//                    s.setMessage("录制开始 - 流ID: " + stream);
//                    SseMessageUtils.publishMessage(s);
//                    break;
//                case "record_paused":
//                    SseMessageDto y = new SseMessageDto();
//                    y.setMessage("录制暂停 - 流ID: " + stream);
//                    SseMessageUtils.publishMessage(y);
//                    break;
//                default:
//                    Log.info("未知的录制状态: " + event);
//            }
//        } catch (Exception e) {
//            Log.error("处理录制回调参数失败: " + e.getMessage());
//        }
//    }
//}
