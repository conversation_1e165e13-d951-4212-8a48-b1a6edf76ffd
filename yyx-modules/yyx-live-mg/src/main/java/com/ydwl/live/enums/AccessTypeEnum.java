package com.ydwl.live.enums;

import lombok.Getter;

/**
 * 枚举类，表示访问类型  0-公开，1-需要登录，2-需要报名，3-会员专享
 */
@Getter
public enum AccessTypeEnum {
    /**
     * 公开访问
     */
    PUBLIC(0, "公开访问"),
    /**
     * 需要登录
     */
    PRIVATE(1, "需要登录"),
    /**
     * 需要报名
     */
    PROTECTED(2, "需要报名"),
    /**
     * 会员专享
     */
    MEMBER_ONLY(3, "会员专享");

    private final int code;
    private final String description;

    // 定义带参构造函数
    AccessTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据访问类型代码获取枚举实例
     *
     * @param code 访问类型代码
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果访问类型代码未知
     */
    public static AccessTypeEnum fromCode(int code) {
        for (AccessTypeEnum type : AccessTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown access type code: " + code);
    }

    /**
     * 根据访问类型描述获取枚举实例
     *
     * @param description 访问类型描述
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果访问类型描述未知
     */
    public static AccessTypeEnum fromDescription(String description) {
        for (AccessTypeEnum type : AccessTypeEnum.values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown access type description: " + description);
    }
}
