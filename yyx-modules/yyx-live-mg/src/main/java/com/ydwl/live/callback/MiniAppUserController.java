//package com.ydwl.live.controller.miniapp;
//
//import ch.qos.logback.core.LogbackException;
//import cn.binarywang.wx.miniapp.api.WxMaService;
//import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
//import com.ydwl.common.core.domain.R;
//import com.ydwl.common.core.domain.model.LoginUser;
//import com.ydwl.common.satoken.utils.LoginHelper;
//import com.ydwl.common.web.core.BaseController;
//import com.ydwl.live.config.WxMnpDriver;
//import com.ydwl.live.domain.bo.UserBo;
//import com.ydwl.live.config.dto.DecryptPhoneNumberDto;
//import com.ydwl.live.domain.vo.UserVo;
//import com.ydwl.live.response.ApiResult;
//import com.ydwl.live.service.IUserService;
//import lombok.RequiredArgsConstructor;
//import me.chanjar.weixin.common.error.WxErrorException;
//import org.springframework.web.bind.annotation.*;
//
///**
// * App 端用户管理
// *
// * <AUTHOR> Yi
// * &#064;date  2025-02-26
// */
//
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/app/live/user")
//public class MiniAppUserController extends BaseController {
//
//    private final IUserService userService;
//    private  final WxMnpDriver wxMnpDriver;
//
//
//
//
//    /**
//     * 查询当前用户信息
//     */
//    @GetMapping("/get-userinfo")
//    public ApiResult<UserVo> list() {
//        LoginUser loginUser = LoginHelper.getLoginUser();
//        return ApiResult.data(userService.queryById(loginUser.getUserId()));
//    }
//
//    /**
//     * 解密手机号
//     * @param body DecryptPhoneNumberDto
//     * @return UserVo
//     */
//    @PostMapping("/decrypt-phone-number")
//    public R<UserVo> decryptPhoneNumber(@RequestBody DecryptPhoneNumberDto body)  {
//        WxMaService wxMaService = WxMnpDriver.mnp();
//        Long userId = LoginHelper.getUserId();
//        try {
//            WxMaPhoneNumberInfo wxMaPhoneNumberInfo = wxMaService.getUserService().getPhoneNumber(body.getCode());
//            UserBo user =  new UserBo();
//            user.setPhone(wxMaPhoneNumberInfo.getPhoneNumber());
//            user.setId(userId);
//            userService.updateByBo(user);
//        } catch (WxErrorException e) {
//            throw new LogbackException(e.getError().getErrorCode() + ", " + e.getError().getErrorMsg());
//        }
//        return R.ok( userService.queryById(userId));
//    }
//
//
//
//}
