package com.ydwl.live.domain.model;

import com.ydwl.live.domain.model.valueobject.*;
import com.ydwl.live.enums.LiveStatusEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 直播聚合根
 * 
 * 负责管理直播的完整生命周期和业务规则
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Getter
public class LiveAggregate {
    
    /**
     * 直播ID
     */
    private final LiveId liveId;
    
    /**
     * 直播基本信息
     */
    private LiveInfo liveInfo;
    
    /**
     * 直播状态
     */
    private LiveStatus status;
    
    /**
     * 直播设置
     */
    private LiveSettings settings;
    
    /**
     * 推流信息
     */
    private StreamInfo streamInfo;
    
    /**
     * 统计信息
     */
    private LiveStatistics statistics;
    
    /**
     * 领域事件列表
     */
    private final List<DomainEvent> domainEvents = new ArrayList<>();
    
    /**
     * 创建新的直播聚合
     */
    public static LiveAggregate create(LiveInfo liveInfo, LiveSettings settings) {
        LiveId liveId = LiveId.generate();
        LiveAggregate aggregate = new LiveAggregate(liveId, liveInfo, settings);
        
        // 发布直播创建事件
        aggregate.addDomainEvent(new LiveCreatedDomainEvent(liveId, liveInfo));
        
        log.info("创建直播聚合: liveId={}, title={}", liveId.getValue(), liveInfo.getTitle());
        return aggregate;
    }
    
    /**
     * 从持久化数据重建聚合
     */
    public static LiveAggregate rebuild(LiveId liveId, LiveInfo liveInfo, 
                                       LiveStatus status, LiveSettings settings,
                                       StreamInfo streamInfo, LiveStatistics statistics) {
        return new LiveAggregate(liveId, liveInfo, status, settings, streamInfo, statistics);
    }
    
    /**
     * 私有构造函数 - 创建新聚合
     */
    private LiveAggregate(LiveId liveId, LiveInfo liveInfo, LiveSettings settings) {
        this.liveId = liveId;
        this.liveInfo = liveInfo;
        this.status = LiveStatus.notStarted();
        this.settings = settings;
        this.statistics = LiveStatistics.initial();
        
        // 验证业务规则
        validateBusinessRules();
    }
    
    /**
     * 私有构造函数 - 重建聚合
     */
    private LiveAggregate(LiveId liveId, LiveInfo liveInfo, LiveStatus status, 
                         LiveSettings settings, StreamInfo streamInfo, LiveStatistics statistics) {
        this.liveId = liveId;
        this.liveInfo = liveInfo;
        this.status = status;
        this.settings = settings;
        this.streamInfo = streamInfo;
        this.statistics = statistics;
    }
    
    /**
     * 开始直播
     */
    public void startLive() {
        if (!canStartLive()) {
            throw new IllegalStateException("当前状态不允许开始直播: " + status.getValue());
        }
        
        this.status = LiveStatus.live();
        this.liveInfo = liveInfo.withActualStartTime(LocalDateTime.now());
        
        // 发布直播开始事件
        addDomainEvent(new LiveStartedDomainEvent(liveId));
        
        log.info("直播开始: liveId={}", liveId.getValue());
    }
    
    /**
     * 结束直播
     */
    public void endLive() {
        if (!canEndLive()) {
            throw new IllegalStateException("当前状态不允许结束直播: " + status.getValue());
        }
        
        LocalDateTime endTime = LocalDateTime.now();
        this.status = LiveStatus.ended();
        this.liveInfo = liveInfo.withActualEndTime(endTime);
        
        // 计算直播时长
        if (liveInfo.getActualStartTime() != null) {
            long durationMinutes = java.time.Duration.between(
                liveInfo.getActualStartTime(), endTime).toMinutes();
            this.liveInfo = liveInfo.withDurationMinutes(durationMinutes);
        }
        
        // 发布直播结束事件
        addDomainEvent(new LiveEndedDomainEvent(liveId, liveInfo.getDurationMinutes()));
        
        log.info("直播结束: liveId={}, 时长={}分钟", liveId.getValue(), liveInfo.getDurationMinutes());
    }
    
    /**
     * 设置推流信息
     */
    public void setStreamInfo(StreamInfo streamInfo) {
        this.streamInfo = streamInfo;
        
        // 发布推流信息更新事件
        addDomainEvent(new StreamInfoUpdatedDomainEvent(liveId, streamInfo));
        
        log.info("设置推流信息: liveId={}, streamId={}", liveId.getValue(), streamInfo.getStreamId());
    }
    
    /**
     * 更新观看统计
     */
    public void updateViewStatistics(long viewCount, long onlineCount) {
        this.statistics = statistics.updateView(viewCount, onlineCount);
        
        log.debug("更新观看统计: liveId={}, 观看数={}, 在线数={}", 
                 liveId.getValue(), viewCount, onlineCount);
    }
    
    /**
     * 更新互动统计
     */
    public void updateInteractionStatistics(long likeCount, long shareCount) {
        this.statistics = statistics.updateInteraction(likeCount, shareCount);
        
        log.debug("更新互动统计: liveId={}, 点赞数={}, 分享数={}", 
                 liveId.getValue(), likeCount, shareCount);
    }
    
    /**
     * 检查是否可以开始直播
     */
    private boolean canStartLive() {
        return status.canTransitionTo(LiveStatusEnum.LIVE);
    }
    
    /**
     * 检查是否可以结束直播
     */
    private boolean canEndLive() {
        return status.canTransitionTo(LiveStatusEnum.ENDED);
    }
    
    /**
     * 验证业务规则
     */
    private void validateBusinessRules() {
        if (liveInfo.getPlanStartTime() != null && 
            liveInfo.getPlanStartTime().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("计划开始时间不能早于当前时间");
        }
        
        if (liveInfo.getTitle() == null || liveInfo.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("直播标题不能为空");
        }
        
        if (liveInfo.getTitle().length() > 100) {
            throw new IllegalArgumentException("直播标题长度不能超过100个字符");
        }
    }
    
    /**
     * 添加领域事件
     */
    private void addDomainEvent(DomainEvent event) {
        this.domainEvents.add(event);
    }
    
    /**
     * 清除领域事件
     */
    public void clearDomainEvents() {
        this.domainEvents.clear();
    }
    
    /**
     * 获取领域事件副本
     */
    public List<DomainEvent> getDomainEvents() {
        return new ArrayList<>(domainEvents);
    }
}
