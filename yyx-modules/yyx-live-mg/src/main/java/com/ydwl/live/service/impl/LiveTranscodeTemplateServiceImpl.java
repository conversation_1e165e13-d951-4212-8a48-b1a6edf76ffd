package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveTranscodeTemplateBo;
import com.ydwl.live.domain.vo.LiveTranscodeTemplateVo;
import com.ydwl.live.domain.LiveTranscodeTemplate;
import com.ydwl.live.mapper.LiveTranscodeTemplateMapper;
import com.ydwl.live.service.ILiveTranscodeTemplateService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 视频转码模板Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveTranscodeTemplateServiceImpl implements ILiveTranscodeTemplateService {

    private final LiveTranscodeTemplateMapper baseMapper;

    /**
     * 查询视频转码模板
     *
     * @param id 主键
     * @return 视频转码模板
     */
    @Override
    public LiveTranscodeTemplateVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询视频转码模板列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频转码模板分页列表
     */
    @Override
    public TableDataInfo<LiveTranscodeTemplateVo> queryPageList(LiveTranscodeTemplateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveTranscodeTemplate> lqw = buildQueryWrapper(bo);
        Page<LiveTranscodeTemplateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的视频转码模板列表
     *
     * @param bo 查询条件
     * @return 视频转码模板列表
     */
    @Override
    public List<LiveTranscodeTemplateVo> queryList(LiveTranscodeTemplateBo bo) {
        LambdaQueryWrapper<LiveTranscodeTemplate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveTranscodeTemplate> buildQueryWrapper(LiveTranscodeTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveTranscodeTemplate> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveTranscodeTemplate::getId);
        lqw.like(StringUtils.isNotBlank(bo.getTemplateName()), LiveTranscodeTemplate::getTemplateName, bo.getTemplateName());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateType()), LiveTranscodeTemplate::getTemplateType, bo.getTemplateType());
        lqw.eq(StringUtils.isNotBlank(bo.getDefinition()), LiveTranscodeTemplate::getDefinition, bo.getDefinition());
        lqw.eq(StringUtils.isNotBlank(bo.getResolution()), LiveTranscodeTemplate::getResolution, bo.getResolution());
        lqw.eq(bo.getVideoBitrate() != null, LiveTranscodeTemplate::getVideoBitrate, bo.getVideoBitrate());
        lqw.eq(bo.getAudioBitrate() != null, LiveTranscodeTemplate::getAudioBitrate, bo.getAudioBitrate());
        lqw.eq(bo.getFps() != null, LiveTranscodeTemplate::getFps, bo.getFps());
        lqw.eq(StringUtils.isNotBlank(bo.getFormat()), LiveTranscodeTemplate::getFormat, bo.getFormat());
        lqw.eq(StringUtils.isNotBlank(bo.getCodec()), LiveTranscodeTemplate::getCodec, bo.getCodec());
        lqw.eq(StringUtils.isNotBlank(bo.getExternalId()), LiveTranscodeTemplate::getExternalId, bo.getExternalId());
        lqw.eq(bo.getStatus() != null, LiveTranscodeTemplate::getStatus, bo.getStatus());
        lqw.eq(bo.getIsDefault() != null, LiveTranscodeTemplate::getIsDefault, bo.getIsDefault());
        return lqw;
    }

    /**
     * 新增视频转码模板
     *
     * @param bo 视频转码模板
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveTranscodeTemplateBo bo) {
        LiveTranscodeTemplate add = MapstructUtils.convert(bo, LiveTranscodeTemplate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改视频转码模板
     *
     * @param bo 视频转码模板
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveTranscodeTemplateBo bo) {
        LiveTranscodeTemplate update = MapstructUtils.convert(bo, LiveTranscodeTemplate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveTranscodeTemplate entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除视频转码模板信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
