package com.ydwl.live.callback.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频上传状态枚举
 */
@Getter
@AllArgsConstructor
public enum UploadStatusEnum {

    UPLOADING(0L, "上传中"),
    COMPLETED(1L, "已完成"),
    FAILED(2L, "失败");

    private final Long code;
    private final String desc;

    public static UploadStatusEnum getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (UploadStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
