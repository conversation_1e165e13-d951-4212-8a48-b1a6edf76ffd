package com.ydwl.live.event;

import com.ydwl.live.domain.bo.LiveBo;
import lombok.Getter;

/**
 * 直播创建事件
 * <p>当新直播创建时触发的事件</p>
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Getter
public class LiveCreatedEvent extends LiveEvent {
    
    /**
     * 直播业务对象
     */
    private final LiveBo liveBo;
    
    public LiveCreatedEvent(Long liveId, LiveBo liveBo) {
        super(liveId);
        this.liveBo = liveBo;
    }
} 