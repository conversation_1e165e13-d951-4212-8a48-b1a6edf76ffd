package com.ydwl.live.enums;

import lombok.Getter;

/**
 * 枚举类，阿里云直播转码状态
 */
@Getter
public enum EncodingStatusEnum {
    /**
     * 编码已排队
     */
    QUEUED(0, "转码已排队"),
    /**
     * 编码正在进行中
     */
    PROCESSING(1, "转码正在进行中"),
    /**
     * 转码成功
     */
    SUCCESS(2, "编码成功"),
    /**
     * 编码失败
     */
    FAILED(3, "编码失败");

    private final int status;
    private final String description;

    // 定义带参构造函数
    EncodingStatusEnum(int status , String description) {
        this.status = status;
        this.description = description;
    }

    /**
     * 根据状态码获取枚举实例
     *
     * @param status 状态码
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果状态码未知
     */
    public static EncodingStatusEnum fromStatus(int status) {
        return switch (status) {
            case 0 -> QUEUED;
            case 1 -> PROCESSING;
            case 2 -> SUCCESS;
            case 3 -> FAILED;
            default -> throw new IllegalArgumentException("Unknown status: " + status);
        };
    }
    /**
     * 根据访问类型描述获取枚举实例
     *
     * @param description 访问类型描述
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果访问类型描述未知
     */
    public static EncodingStatusEnum fromDescription(String description) {
        for (EncodingStatusEnum type : EncodingStatusEnum.values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown access type description: " + description);
    }

    /**
     * 返回枚举值的描述信息
     *
     * @return 枚举值的描述信息
     */
    public String getDescription() {
        return switch (this) {
            case QUEUED -> "编码已排队";
            case PROCESSING -> "编码正在进行中";
            case SUCCESS -> "编码成功";
            case FAILED -> "编码失败";
            default -> "未知状态";
        };
    }
}
