package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 转码任务对象 live_transcode_task
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_transcode_task")
public class LiveTranscodeTask extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 转码任务ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 任务类型(fc-函数计算,mts-媒体转码服务)
     */
    private String taskType;

    /**
     * 关联直播ID
     */
    private Long liveId;

    /**
     * 源文件地址
     */
    private String sourceUrl;

    /**
     * 状态(0-待处理,1-处理中,2-已完成,3-已失败,4-已取消)
     */
    private Long status;

    /**
     * 转码模板ID
     */
    private Long templateId;

    /**
     * OSS存储桶名称
     */
    private String bucket;

    /**
     * OSS对象键名
     */
    private String objectKey;

    /**
     * 输出文件地址
     */
    private String outputUrl;

    /**
     * 视频时长(秒)
     */
    private Long duration;

    /**
     * 文件大小(byte)
     */
    private Long fileSize;

    /**
     * 转码进度(%)
     */
    private Long progress;

    /**
     * 业务ID(回调标识)
     */
    private String bizId;

    /**
     * 回调通知URL
     */
    private String callbackUrl;

    /**
     * 回调状态(0-未回调,1-已回调)
     */
    private Long callbackStatus;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 重试次数
     */
    private Long retryCount;

    /**
     * 优先级(1-10)
     */
    private Long priority;


}
