package com.ydwl.live.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * 推流信息值对象
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
@EqualsAndHashCode
public class StreamInfo {
    
    /**
     * 推流ID
     */
    private final Long streamId;
    
    /**
     * 推流地址
     */
    private final String pushUrl;
    
    /**
     * 推流密钥
     */
    private final String pushKey;
    
    /**
     * 播放地址列表
     */
    private final Map<String, String> playUrls;
    
    /**
     * 推流状态
     */
    private final StreamStatus status;
    
    private StreamInfo(Long streamId, String pushUrl, String pushKey, 
                      Map<String, String> playUrls, StreamStatus status) {
        this.streamId = streamId;
        this.pushUrl = pushUrl;
        this.pushKey = pushKey;
        this.playUrls = playUrls;
        this.status = status;
    }
    
    /**
     * 创建推流信息
     */
    public static StreamInfo create(Long streamId, String pushUrl, String pushKey, 
                                   Map<String, String> playUrls) {
        return new StreamInfo(streamId, pushUrl, pushKey, playUrls, StreamStatus.INACTIVE);
    }
    
    /**
     * 更新推流状态
     */
    public StreamInfo withStatus(StreamStatus status) {
        return new StreamInfo(streamId, pushUrl, pushKey, playUrls, status);
    }
    
    /**
     * 刷新推流地址
     */
    public StreamInfo refresh(String newPushUrl, String newPushKey, Map<String, String> newPlayUrls) {
        return new StreamInfo(streamId, newPushUrl, newPushKey, newPlayUrls, status);
    }
    
    /**
     * 是否正在推流
     */
    public boolean isActive() {
        return status == StreamStatus.ACTIVE;
    }
    
    /**
     * 推流状态枚举
     */
    public enum StreamStatus {
        INACTIVE(0L, "未推流"),
        ACTIVE(1L, "推流中"),
        ERROR(2L, "推流异常");
        
        private final Long value;
        private final String description;
        
        StreamStatus(Long value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public Long getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static StreamStatus fromValue(Long value) {
            for (StreamStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的推流状态: " + value);
        }
    }
}
