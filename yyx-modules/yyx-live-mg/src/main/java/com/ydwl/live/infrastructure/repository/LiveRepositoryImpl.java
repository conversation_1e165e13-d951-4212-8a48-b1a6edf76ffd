package com.ydwl.live.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ydwl.live.domain.Live;
import com.ydwl.live.domain.LiveStream;
import com.ydwl.live.domain.model.LiveAggregate;
import com.ydwl.live.domain.model.valueobject.*;
import com.ydwl.live.domain.repository.LiveRepository;
import com.ydwl.live.mapper.LiveMapper;
import com.ydwl.live.mapper.LiveStreamMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 直播仓储实现
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class LiveRepositoryImpl implements LiveRepository {
    
    private final LiveMapper liveMapper;
    private final LiveStreamMapper liveStreamMapper;
    
    @Override
    public void save(LiveAggregate aggregate) {
        // 转换聚合为持久化对象
        Live liveEntity = convertToEntity(aggregate);
        
        if (liveEntity.getId() == null) {
            // 新增
            liveMapper.insert(liveEntity);
            // 设置生成的ID回聚合
            // aggregate.setId(liveEntity.getId()); // 需要在聚合中添加此方法
        } else {
            // 更新
            liveMapper.updateById(liveEntity);
        }
        
        // 保存推流信息
        if (aggregate.getStreamInfo() != null) {
            saveStreamInfo(aggregate.getLiveId(), aggregate.getStreamInfo());
        }
        
        log.debug("保存直播聚合: liveId={}", aggregate.getLiveId().getValue());
    }
    
    @Override
    public Optional<LiveAggregate> findById(LiveId liveId) {
        Live liveEntity = liveMapper.selectById(liveId.getValue());
        if (liveEntity == null) {
            return Optional.empty();
        }
        
        // 查询推流信息
        LiveStream streamEntity = liveStreamMapper.selectOne(
            new LambdaQueryWrapper<LiveStream>()
                .eq(LiveStream::getLiveId, liveId.getValue())
        );
        
        // 重建聚合
        LiveAggregate aggregate = rebuildAggregate(liveEntity, streamEntity);
        return Optional.of(aggregate);
    }
    
    @Override
    public List<LiveAggregate> findByStatus(LiveStatus status) {
        List<Live> liveEntities = liveMapper.selectList(
            new LambdaQueryWrapper<Live>()
                .eq(Live::getStatus, status.getNumericValue())
        );
        
        return liveEntities.stream()
            .map(entity -> {
                LiveStream streamEntity = liveStreamMapper.selectOne(
                    new LambdaQueryWrapper<LiveStream>()
                        .eq(LiveStream::getLiveId, entity.getId())
                );
                return rebuildAggregate(entity, streamEntity);
            })
            .collect(Collectors.toList());
    }
    
    @Override
    public List<LiveAggregate> findByCategoryId(Long categoryId) {
        List<Live> liveEntities = liveMapper.selectList(
            new LambdaQueryWrapper<Live>()
                .eq(Live::getCategoryId, categoryId)
        );
        
        return liveEntities.stream()
            .map(entity -> {
                LiveStream streamEntity = liveStreamMapper.selectOne(
                    new LambdaQueryWrapper<LiveStream>()
                        .eq(LiveStream::getLiveId, entity.getId())
                );
                return rebuildAggregate(entity, streamEntity);
            })
            .collect(Collectors.toList());
    }
    
    @Override
    public void delete(LiveId liveId) {
        liveMapper.deleteById(liveId.getValue());
        
        // 删除相关推流信息
        liveStreamMapper.delete(
            new LambdaQueryWrapper<LiveStream>()
                .eq(LiveStream::getLiveId, liveId.getValue())
        );
        
        log.info("删除直播聚合: liveId={}", liveId.getValue());
    }
    
    @Override
    public boolean exists(LiveId liveId) {
        Long count = liveMapper.selectCount(
            new LambdaQueryWrapper<Live>()
                .eq(Live::getId, liveId.getValue())
        );
        return count > 0;
    }
    
    @Override
    public LiveId nextId() {
        // 简化实现，实际应该使用分布式ID生成器
        return LiveId.generate();
    }
    
    /**
     * 保存推流信息
     */
    private void saveStreamInfo(LiveId liveId, StreamInfo streamInfo) {
        LiveStream existingStream = liveStreamMapper.selectOne(
            new LambdaQueryWrapper<LiveStream>()
                .eq(LiveStream::getLiveId, liveId.getValue())
        );
        
        if (existingStream == null) {
            // 新增推流信息
            LiveStream streamEntity = new LiveStream();
            streamEntity.setLiveId(liveId.getValue());
            streamEntity.setPushUrl(streamInfo.getPushUrl());
            streamEntity.setPushKey(streamInfo.getPushKey());
            streamEntity.setStreamStatus(streamInfo.getStatus().getValue());
            liveStreamMapper.insert(streamEntity);
        } else {
            // 更新推流信息
            existingStream.setPushUrl(streamInfo.getPushUrl());
            existingStream.setPushKey(streamInfo.getPushKey());
            existingStream.setStreamStatus(streamInfo.getStatus().getValue());
            liveStreamMapper.updateById(existingStream);
        }
    }
    
    /**
     * 转换聚合为实体
     */
    private Live convertToEntity(LiveAggregate aggregate) {
        Live entity = new Live();
        entity.setId(aggregate.getLiveId().getValue());
        entity.setCategoryId(aggregate.getLiveInfo().getCategoryId());
        entity.setTitle(aggregate.getLiveInfo().getTitle());
        entity.setCoverImgUrl(aggregate.getLiveInfo().getCoverImgUrl());
        
        // 时间转换
        if (aggregate.getLiveInfo().getPlanStartTime() != null) {
            entity.setPlanStartTime(Date.from(
                aggregate.getLiveInfo().getPlanStartTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (aggregate.getLiveInfo().getActualStartTime() != null) {
            entity.setActualStartTime(Date.from(
                aggregate.getLiveInfo().getActualStartTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (aggregate.getLiveInfo().getActualEndTime() != null) {
            entity.setActualEndTime(Date.from(
                aggregate.getLiveInfo().getActualEndTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
        
        entity.setDurationMinutes(aggregate.getLiveInfo().getDurationMinutes());
        entity.setStatus(aggregate.getStatus().getNumericValue());
        entity.setDescription(aggregate.getLiveInfo().getDescription());
        entity.setTagList(aggregate.getLiveInfo().getTagList());
        
        // 设置统计信息
        entity.setViewCount(aggregate.getStatistics().getViewCount());
        entity.setLikeCount(aggregate.getStatistics().getLikeCount());
        entity.setShareCount(aggregate.getStatistics().getShareCount());
        entity.setMaxOnlineCount(aggregate.getStatistics().getMaxOnlineCount());
        
        // 设置直播设置
        entity.setSettingSignupRequired(aggregate.getSettings().isSignupRequired() ? 1L : 0L);
        entity.setSettingReplayEnabled(aggregate.getSettings().isReplayEnabled() ? 1L : 0L);
        entity.setSettingAutoRecord(aggregate.getSettings().isAutoRecord() ? 1L : 0L);
        entity.setSettingChatEnabled(aggregate.getSettings().isChatEnabled() ? 1L : 0L);
        entity.setSettingChatDelay(aggregate.getSettings().getChatDelay());
        entity.setSettingGiftEnabled(aggregate.getSettings().isGiftEnabled() ? 1L : 0L);
        entity.setSettingDefaultQuality(aggregate.getSettings().getDefaultQuality());
        entity.setSettingAccessLevel(aggregate.getSettings().getAccessLevel().getValue());
        
        return entity;
    }
    
    /**
     * 重建聚合
     */
    private LiveAggregate rebuildAggregate(Live entity, LiveStream streamEntity) {
        // 构建LiveInfo
        LiveInfo liveInfo = LiveInfo.create(
            entity.getCategoryId(),
            entity.getTitle(),
            entity.getCoverImgUrl(),
            entity.getPlanStartTime() != null ? 
                LocalDateTime.ofInstant(entity.getPlanStartTime().toInstant(), ZoneId.systemDefault()) : null,
            entity.getDescription(),
            entity.getTagList()
        );
        
        // 设置实际时间
        if (entity.getActualStartTime() != null) {
            liveInfo = liveInfo.withActualStartTime(
                LocalDateTime.ofInstant(entity.getActualStartTime().toInstant(), ZoneId.systemDefault()));
        }
        if (entity.getActualEndTime() != null) {
            liveInfo = liveInfo.withActualEndTime(
                LocalDateTime.ofInstant(entity.getActualEndTime().toInstant(), ZoneId.systemDefault()));
        }
        if (entity.getDurationMinutes() != null) {
            liveInfo = liveInfo.withDurationMinutes(entity.getDurationMinutes());
        }
        
        // 构建LiveStatus
        LiveStatus status = LiveStatus.of(entity.getStatus());
        
        // 构建LiveSettings
        LiveSettings settings = LiveSettings.create(
            entity.getSettingSignupRequired() == 1L,
            entity.getSettingReplayEnabled() == 1L,
            entity.getSettingAutoRecord() == 1L,
            entity.getSettingChatEnabled() == 1L,
            entity.getSettingChatDelay(),
            entity.getSettingGiftEnabled() == 1L,
            entity.getSettingDefaultQuality(),
            LiveSettings.AccessLevel.fromValue(entity.getSettingAccessLevel())
        );
        
        // 构建StreamInfo
        StreamInfo streamInfo = null;
        if (streamEntity != null) {
            streamInfo = StreamInfo.create(
                streamEntity.getId(),
                streamEntity.getPushUrl(),
                streamEntity.getPushKey(),
                new HashMap<>() // 播放地址需要根据实际情况构建
            ).withStatus(StreamInfo.StreamStatus.fromValue(streamEntity.getStreamStatus()));
        }
        
        // 构建LiveStatistics
        LiveStatistics statistics = LiveStatistics.of(
            entity.getViewCount() != null ? entity.getViewCount() : 0L,
            entity.getLikeCount() != null ? entity.getLikeCount() : 0L,
            entity.getShareCount() != null ? entity.getShareCount() : 0L,
            entity.getMaxOnlineCount() != null ? entity.getMaxOnlineCount() : 0L,
            0L // 当前在线人数需要从缓存或其他地方获取
        );
        
        // 重建聚合
        return LiveAggregate.rebuild(
            LiveId.of(entity.getId()),
            liveInfo,
            status,
            settings,
            streamInfo,
            statistics
        );
    }
}
