package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveArticleVo;
import com.ydwl.live.domain.bo.LiveArticleBo;
import com.ydwl.live.service.ILiveArticleService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 直播文章
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/article")
public class LiveArticleController extends BaseController {

    private final ILiveArticleService liveArticleService;

    /**
     * 查询直播文章列表
     */
    @SaCheckPermission("live:article:list")
    @GetMapping("/list")
    public TableDataInfo<LiveArticleVo> list(LiveArticleBo bo, PageQuery pageQuery) {
        return liveArticleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出直播文章列表
     */
    @SaCheckPermission("live:article:export")
    @Log(title = "直播文章", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveArticleBo bo, HttpServletResponse response) {
        List<LiveArticleVo> list = liveArticleService.queryList(bo);
        ExcelUtil.exportExcel(list, "直播文章", LiveArticleVo.class, response);
    }

    /**
     * 获取直播文章详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:article:query")
    @GetMapping("/{id}")
    public R<LiveArticleVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveArticleService.queryById(id));
    }

    /**
     * 新增直播文章
     */
    @SaCheckPermission("live:article:add")
    @Log(title = "直播文章", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveArticleBo bo) {
        return toAjax(liveArticleService.insertByBo(bo));
    }

    /**
     * 修改直播文章
     */
    @SaCheckPermission("live:article:edit")
    @Log(title = "直播文章", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveArticleBo bo) {
        return toAjax(liveArticleService.updateByBo(bo));
    }

    /**
     * 删除直播文章
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:article:remove")
    @Log(title = "直播文章", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveArticleService.deleteWithValidByIds(List.of(ids), true));
    }
}
