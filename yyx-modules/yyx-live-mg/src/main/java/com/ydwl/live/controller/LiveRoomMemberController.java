package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveRoomMemberVo;
import com.ydwl.live.domain.bo.LiveRoomMemberBo;
import com.ydwl.live.service.ILiveRoomMemberService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 直播间在线成员
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/roomMember")
public class LiveRoomMemberController extends BaseController {

    private final ILiveRoomMemberService liveRoomMemberService;

    /**
     * 查询直播间在线成员列表
     */
    @SaCheckPermission("live:roomMember:list")
    @GetMapping("/list")
    public TableDataInfo<LiveRoomMemberVo> list(LiveRoomMemberBo bo, PageQuery pageQuery) {
        return liveRoomMemberService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出直播间在线成员列表
     */
    @SaCheckPermission("live:roomMember:export")
    @Log(title = "直播间在线成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveRoomMemberBo bo, HttpServletResponse response) {
        List<LiveRoomMemberVo> list = liveRoomMemberService.queryList(bo);
        ExcelUtil.exportExcel(list, "直播间在线成员", LiveRoomMemberVo.class, response);
    }

    /**
     * 获取直播间在线成员详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:roomMember:query")
    @GetMapping("/{id}")
    public R<LiveRoomMemberVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveRoomMemberService.queryById(id));
    }

    /**
     * 新增直播间在线成员
     */
    @SaCheckPermission("live:roomMember:add")
    @Log(title = "直播间在线成员", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveRoomMemberBo bo) {
        return toAjax(liveRoomMemberService.insertByBo(bo));
    }

    /**
     * 修改直播间在线成员
     */
    @SaCheckPermission("live:roomMember:edit")
    @Log(title = "直播间在线成员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveRoomMemberBo bo) {
        return toAjax(liveRoomMemberService.updateByBo(bo));
    }

    /**
     * 删除直播间在线成员
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:roomMember:remove")
    @Log(title = "直播间在线成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveRoomMemberService.deleteWithValidByIds(List.of(ids), true));
    }
}
