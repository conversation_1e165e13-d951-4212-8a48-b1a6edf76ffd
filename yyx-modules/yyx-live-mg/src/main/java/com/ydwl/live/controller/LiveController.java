package com.ydwl.live.controller;

import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveVo;
import com.ydwl.live.domain.bo.LiveBo;
import com.ydwl.live.service.ILiveService;
import com.ydwl.live.service.ILiveDataService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 直播信息
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/live")
public class LiveController extends BaseController {

    private final ILiveService liveService;
    private final ILiveDataService liveDataService;

    /**
     * 查询直播信息列表（包含推流、回放等详细信息）
     */
    @SaCheckPermission("live:live:list")
    @GetMapping("/list/detail")
    public TableDataInfo<Map<String, Object>> listWithDetails(LiveBo bo, PageQuery pageQuery) {
        return liveService.queryPageListWithDetails(bo, pageQuery);
    }



    /**
     * 获取直播详细信息（包含推流和播放信息）
     *
     * @param id 主键
     */
    @SaCheckPermission("live:live:query")
    @GetMapping("/detail/{id}")
    public R<Map<String, Object>> getDetail(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        Map<String, Object> detail = liveDataService.getLiveDetail(id);
        return detail != null ? R.ok(detail) : R.fail("获取直播详情失败");
    }


    /**
     * 新增直播信息（自动生成推流地址）
     */
    @SaCheckPermission("live:live:add")
    @Log(title = "创建直播", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Map<String, Object>> create(@Validated(AddGroup.class) @RequestBody LiveBo bo) {
        return liveDataService.createLiveWithStream(bo);
    }

    /**
     * 修改直播信息
     */
    @SaCheckPermission("live:live:edit")
    @Log(title = "直播信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveBo bo) {
        return toAjax(liveService.updateByBo(bo));
    }

    /**
     * 刷新直播推流地址
     */
    @SaCheckPermission("live:live:edit")
    @Log(title = "刷新推流地址", businessType = BusinessType.UPDATE)
    @PutMapping("/refresh/{id}")
    public R<Map<String, Object>> refreshStream(@PathVariable Long id) {
        return liveDataService.refreshLiveStream(id);
    }

    /**
     * 删除直播信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:live:remove")
    @Log(title = "直播信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 按分类ID查询直播列表（分页）
     */
    @GetMapping("/category/{categoryId}")
    public TableDataInfo<LiveVo> listByCategory(
            @PathVariable @NotNull(message = "分类ID不能为空") Long categoryId,
            @RequestParam(required = false) Long status,
            PageQuery pageQuery) {
        return liveService.queryPageListByCategory(categoryId, status, pageQuery);
    }
    
    /**
     * 按分类ID查询直播列表（不分页）
     */
    @GetMapping("/category/{categoryId}/list")
    public R<List<LiveVo>> listByCategoryAll(
            @PathVariable @NotNull(message = "分类ID不能为空") Long categoryId,
            @RequestParam(required = false) Long status,
            @RequestParam(required = false) Integer limit) {
        List<LiveVo> list = liveService.queryListByCategory(categoryId, status, limit);
        return R.ok(list);
    }
    
    /**
     * 按分类路径查询直播列表（包含子分类，分页）
     */
    @GetMapping("/category/path/{path}")
    public TableDataInfo<LiveVo> listByCategoryPath(
            @PathVariable @NotBlank(message = "分类路径不能为空") String path,
            @RequestParam(required = false) Long status,
            PageQuery pageQuery) {
        return liveService.queryPageListByCategoryPath(path, status, pageQuery);
    }
}
