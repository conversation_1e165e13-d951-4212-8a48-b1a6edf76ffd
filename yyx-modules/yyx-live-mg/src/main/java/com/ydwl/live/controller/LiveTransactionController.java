package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveTransactionVo;
import com.ydwl.live.domain.bo.LiveTransactionBo;
import com.ydwl.live.service.ILiveTransactionService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 交易记录
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/transaction")
public class LiveTransactionController extends BaseController {

    private final ILiveTransactionService liveTransactionService;

    /**
     * 查询交易记录列表
     */
    @SaCheckPermission("live:transaction:list")
    @GetMapping("/list")
    public TableDataInfo<LiveTransactionVo> list(LiveTransactionBo bo, PageQuery pageQuery) {
        return liveTransactionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出交易记录列表
     */
    @SaCheckPermission("live:transaction:export")
    @Log(title = "交易记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveTransactionBo bo, HttpServletResponse response) {
        List<LiveTransactionVo> list = liveTransactionService.queryList(bo);
        ExcelUtil.exportExcel(list, "交易记录", LiveTransactionVo.class, response);
    }

    /**
     * 获取交易记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:transaction:query")
    @GetMapping("/{id}")
    public R<LiveTransactionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveTransactionService.queryById(id));
    }

    /**
     * 新增交易记录
     */
    @SaCheckPermission("live:transaction:add")
    @Log(title = "交易记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveTransactionBo bo) {
        return toAjax(liveTransactionService.insertByBo(bo));
    }

    /**
     * 修改交易记录
     */
    @SaCheckPermission("live:transaction:edit")
    @Log(title = "交易记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveTransactionBo bo) {
        return toAjax(liveTransactionService.updateByBo(bo));
    }

    /**
     * 删除交易记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:transaction:remove")
    @Log(title = "交易记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveTransactionService.deleteWithValidByIds(List.of(ids), true));
    }
}
