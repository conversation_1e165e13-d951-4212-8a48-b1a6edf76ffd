package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 用户订阅对象 live_user_subscribe
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_user_subscribe")
public class LiveUserSubscribe extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订阅ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 直播ID
     */
    private Long liveId;

    /**
     * 订阅时间
     */
    private Date subscribeTime;

    /**
     * 是否已通知(0-未通知,1-已通知)
     */
    private Long isNotified;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
