package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveVideoUpload;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 视频上传记录业务对象 live_video_upload
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveVideoUpload.class, reverseConvertGenerate = false)
public class LiveVideoUploadBo extends BaseEntity {

    /**
     * 上传ID
     */
    @NotNull(message = "上传ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long liveId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小(单位:字节)
     */
    @NotNull(message = "文件大小(单位:字节)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fileSizeBytes;

    /**
     * 文件类型
     */
    @NotBlank(message = "文件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileType;

    /**
     * OSS存储地址
     */
    @NotBlank(message = "OSS存储地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ossUrl;

    /**
     * 文件MD5
     */
    private String fileMd5;

    /**
     * 视频时长(单位:秒)
     */
    private Double videoDurationSeconds;

    /**
     * 视频分辨率
     */
    private String videoResolution;

    /**
     * 视频码率(单位:Kbps)
     */
    private Integer videoBitrateKbps;

    /**
     * 帧率
     */
    private Double frameRate;

    /**
     * 视频编码格式
     */
    private String videoCodec;

    /**
     * 音频编码格式
     */
    private String audioCodec;

    /**
     * 宽高比
     */
    private String aspectRatio;

    /**
     * 创建日期
     */
    private String createdDate;

    /**
     * 最后修改日期
     */
    private String lastModifiedDate;

    /**
     * 状态(0-上传中,1-已完成,2-失败)
     */
    @NotNull(message = "状态(0-上传中,1-已完成,2-失败)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long uploadStatus;

    /**
     * 上传进度(单位:%)
     */
    private Long uploadProgressPercent;

    /**
     * 上传完成时间
     */
    private Date uploadCompleteTime;

    /**
     * 错误信息
     */
    private String errorMessage;


}
