package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.Live;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 直播信息业务对象 live
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Live.class, reverseConvertGenerate = false)
public class LiveBo extends BaseEntity {

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 直播分类ID
     */
    @NotNull(message = "直播分类ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long categoryId;

    /**
     * 直播标题
     */
    @NotBlank(message = "直播标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 直播封面图片URL
     */
    @NotBlank(message = "直播封面图片URL不能为空", groups = { AddGroup.class, EditGroup.class })
    private String coverImgUrl;

    /**
     * 计划开始时间
     */
    @NotNull(message = "计划开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date planStartTime;

    /**
     * 实际开始时间
     */
    private Date actualStartTime;

    /**
     * 实际结束时间
     */
    private Date actualEndTime;

    /**
     * 实际直播时长(分钟)
     */
    private Long durationMinutes;

    /**
     * 直播状态(0-未开始,1-直播中,2-已结束,3-回放中)
     */
    @NotNull(message = "直播状态(0-未开始,1-直播中,2-已结束,3-回放中)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * 关联推流ID
     */
    private Long streamId;

    /**
     * 是否需要报名(0-否,1-是)
     */
    @NotNull(message = "是否需要报名(0-否,1-是)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long settingSignupRequired;

    /**
     * 是否启用回放(0-否,1-是)
     */
    private Long settingReplayEnabled;

    /**
     * 是否自动录制(0-否,1-是)
     */
    @NotNull(message = "是否自动录制(0-否,1-是)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long settingAutoRecord;

    /**
     * 是否启用聊天(0-否,1-是)
     */
    @NotNull(message = "是否启用聊天(0-否,1-是)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long settingChatEnabled;

    /**
     * 聊天延迟时间(秒)
     */
    private Long settingChatDelay;

    /**
     * 是否启用礼物(0-否,1-是)
     */
    @NotNull(message = "是否启用礼物(0-否,1-是)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long settingGiftEnabled;

    /**
     * 默认画质(HD-高清,SD-标清)
     */
    private String settingDefaultQuality;

    /**
     * 访问权限(0-公开,1-需登录,2-需报名,3-会员专享)
     */
    @NotNull(message = "访问权限(0-公开,1-需登录,2-需报名,3-会员专享)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long settingAccessLevel;

    /**
     * 直播描述
     */
    private String description;

    /**
     * 标签列表,多个标签用逗号分隔
     */
    private String tagList;

    /**
     * 录制视频存储地址
     */
    private String recordVideoUrl;

    /**
     * 观看人数
     */
    private Long viewCount;

    /**
     * 点赞数
     */
    private Long likeCount;

    /**
     * 分享数
     */
    private Long shareCount;

    /**
     * 最大同时在线人数
     */
    private Long maxOnlineCount;

    /**
     * 是否精选(0-否,1-是)
     */
    private Long isFeatured;

    /**
     * 是否有回放(0-无,1-有)
     */
    private Long hasReplay;


}
