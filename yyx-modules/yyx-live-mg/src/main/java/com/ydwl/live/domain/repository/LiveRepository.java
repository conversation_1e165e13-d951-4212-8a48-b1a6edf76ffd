package com.ydwl.live.domain.repository;

import com.ydwl.live.domain.model.LiveAggregate;
import com.ydwl.live.domain.model.valueobject.LiveId;
import com.ydwl.live.domain.model.valueobject.LiveStatus;

import java.util.List;
import java.util.Optional;

/**
 * 直播聚合仓储接口
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public interface LiveRepository {
    
    /**
     * 保存直播聚合
     */
    void save(LiveAggregate aggregate);
    
    /**
     * 根据ID查找直播聚合
     */
    Optional<LiveAggregate> findById(LiveId liveId);
    
    /**
     * 根据状态查找直播列表
     */
    List<LiveAggregate> findByStatus(LiveStatus status);
    
    /**
     * 根据分类ID查找直播列表
     */
    List<LiveAggregate> findByCategoryId(Long categoryId);
    
    /**
     * 删除直播聚合
     */
    void delete(LiveId liveId);
    
    /**
     * 检查直播是否存在
     */
    boolean exists(LiveId liveId);
    
    /**
     * 获取下一个可用的直播ID
     */
    LiveId nextId();
}
