package com.ydwl.live.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 直播事件发布器
 * <p>用于发布直播相关事件</p>
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LiveEventPublisher {
    
    private final ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布事件
     *
     * @param event 事件对象
     */
    public void publish(LiveEvent event) {
        log.info("发布事件: {}, ID: {}, 直播ID: {}", 
            event.getClass().getSimpleName(), 
            event.getEventId(), 
            event.getLiveId());
        eventPublisher.publishEvent(event);
    }
} 