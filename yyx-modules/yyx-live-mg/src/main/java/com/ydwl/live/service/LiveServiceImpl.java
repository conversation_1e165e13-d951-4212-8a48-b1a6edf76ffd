//package com.ydwl.live.service;
//
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.ydwl.common.core.exception.ServiceException;
//
//import com.ydwl.live.callback.dto.ChatMessageDTO;
//import com.ydwl.live.config.VideoProcessConfig;
//import com.ydwl.live.domain.Live;
//import com.ydwl.live.domain.bo.LiveBo;
//import com.ydwl.live.domain.vo.*;
//import com.ydwl.live.mapper.LiveMapper;
//import com.ydwl.live.service.impl.LiveArticleServiceImpl;
//import com.ydwl.live.service.impl.LiveStreamServiceImpl;
//import com.ydwl.live.service.impl.LiveVideoUploadServiceImpl;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.stereotype.Service;
//import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
//import software.amazon.awssdk.utils.StringUtils;
//
//import java.io.IOException;
//import java.util.*;
//import java.util.concurrent.CompletableFuture;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.TimeUnit;
//
///**
// * 直播信息Service业务层处理
// *
// * <AUTHOR> Yi
// * @date 2025-05-08
// */
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public abstract class LiveChatService extends ServiceImpl<LiveMapper, Live> implements ILiveService {
//
//    private final LiveMapper baseMapper;
//    private final LiveStreamServiceImpl liveStreamService;
//    private final LiveVideoUploadServiceImpl liveVideoUploadService;
//    private final LiveArticleServiceImpl liveArticleService;
//    private final RedisTemplate<String, Object> redisTemplate;
//    private final VideoProcessConfig videoProcessConfig;
//
//    // 存储所有活跃的SSE连接，使用Map存储每个直播间的所有客户端连接
//    private static final ConcurrentHashMap<String, Set<SseEmitter>> SSE_CACHE = new ConcurrentHashMap<>();
//
//    // Redis key前缀
//    private static final String LIVE_CHAT_KEY_PREFIX = "live:chat:";
//    private static final String LIVE_CHAT_HISTORY_KEY_PREFIX = "live:chat:history:";
//    private static final String LIVE_CHAT_STATUS_KEY_PREFIX = "live:chat:status:";
//
//    // 聊天记录保存时间（24小时）
//    private static final long CHAT_HISTORY_EXPIRE_TIME = 24 * 60 * 60;
//
//
//    /**
//     * 开启直播间聊天室
//     *
//     * @param liveId 直播ID
//     * @return 是否开启成功
//     */
//    @Override
//    public boolean openLiveChat(String liveId) {
//        // 检查直播间是否存在
//        Live live = getById(Long.valueOf(liveId));
//        if (live == null) {
//            throw new ServiceException("直播间不存在");
//        }
//
//        // 更新直播间聊天状态
//        Live updateLive = new Live();
//        updateLive.setId(Long.valueOf(liveId));
//        updateLive.setIsChatEnabled(1L); // 1表示开启
//        return updateById(updateLive);
//    }
//
//    /**
//     * 关闭直播间聊天室
//     *
//     * @param liveId 直播ID
//     * @return 是否关闭成功
//     */
//    @Override
//    public boolean closeLiveChat(String liveId) {
//        // 检查直播间是否存在
//        Live live = getById(Long.valueOf(liveId));
//        if (live == null) {
//            throw new ServiceException("直播间不存在");
//        }
//
//        // 更新直播间聊天状态
//        Live updateLive = new Live();
//        updateLive.setId(Long.valueOf(liveId));
//        updateLive.setIsChatEnabled(0L); // 0表示关闭
//        return updateById(updateLive);
//    }
//
//    /**
//     * 检查聊天室是否开启
//     *
//     * @param liveId 直播ID
//     * @return 是否开启
//     */
//    @Override
//    public boolean isLiveChatOpen(String liveId) {
//        // 检查直播间是否存在
//        Live live = getById(Long.valueOf(liveId));
//        if (live == null) {
//            throw new ServiceException("直播间不存在");
//        }
//
//        return live.getIsChatEnabled() == 1L;
//    }
//
//    /**
//     * 获取直播间聊天记录
//     * 使用SSE实时推送聊天记录
//     *
//     * @param liveId 直播ID
//     * @return SSE连接
//     */
//    @Override
//    public SseEmitter getLiveChat(String liveId) {
//        // 检查聊天室是否开启
//        if (!isLiveChatOpen(liveId)) {
//            throw new RuntimeException("聊天室未开启");
//        }
//
//        // 1. 创建SSE连接
//        SseEmitter emitter = new SseEmitter(0L); // 0表示不超时
//
//        // 2. 将连接加入缓存
//        SSE_CACHE.computeIfAbsent(liveId, k -> ConcurrentHashMap.newKeySet()).add(emitter);
//
//        // 3. 发送历史聊天记录
//        String historyKey = LIVE_CHAT_HISTORY_KEY_PREFIX + liveId;
//        List<Object> historyMessages = redisTemplate.opsForList().range(historyKey, 0, -1);
//        if (historyMessages != null && !historyMessages.isEmpty()) {
//            try {
//                for (Object message : historyMessages) {
//                    emitter.send(message);
//                }
//            } catch (IOException e) {
//                log.error("发送历史聊天记录失败", e);
//            }
//        }
//
//        // 4. 设置连接关闭时的清理操作
//        emitter.onCompletion(() -> {
//            removeEmitter(liveId, emitter);
//            log.info("SSE连接关闭，liveId: {}", liveId);
//        });
//
//        emitter.onTimeout(() -> {
//            removeEmitter(liveId, emitter);
//            log.info("SSE连接超时，liveId: {}", liveId);
//        });
//
//        emitter.onError((ex) -> {
//            removeEmitter(liveId, emitter);
//            log.error("SSE连接异常，liveId: {}", liveId, ex);
//        });
//
//        return emitter;
//    }
//
//    /**
//     * 移除SSE连接
//     */
//    private void removeEmitter(String liveId, SseEmitter emitter) {
//        Set<SseEmitter> emitters = SSE_CACHE.get(liveId);
//        if (emitters != null) {
//            emitters.remove(emitter);
//            if (emitters.isEmpty()) {
//                SSE_CACHE.remove(liveId);
//            }
//        }
//    }
//
//    /**
//     * 发送聊天消息
//     *
//     * @param message 聊天消息
//     * @return 是否发送成功
//     */
//    @Override
//    public Boolean sendChatMessage(ChatMessageDTO message) {
//        try {
//            // 检查聊天室是否开启
//            if (!isLiveChatOpen(message.getLiveId())) {
//                log.error("聊天室未开启，liveId: {}", message.getLiveId());
//                throw new RuntimeException("聊天室未开启");
//            }
//
//            // 1. 检查必要字段
//            if (message.getUserId() == null || message.getNickname() == null) {
//                log.error("用户信息不完整，message: {}", message);
//                throw new RuntimeException("用户信息不完整");
//            }
//
//            // 2. 设置发送时间
//            message.setSendTime(new Date());
//
//            // 3. 保存消息到Redis历史记录
//            String historyKey = LIVE_CHAT_HISTORY_KEY_PREFIX + message.getLiveId();
//            try {
//                redisTemplate.opsForList().rightPush(historyKey, message);
//                log.info("消息已保存到Redis，key: {}, message: {}", historyKey, message);
//            } catch (Exception e) {
//                log.error("保存消息到Redis失败", e);
//                throw new RuntimeException("保存消息失败");
//            }
//
//            // 4. 通过SSE推送给所有连接的客户端
//            Set<SseEmitter> emitters = SSE_CACHE.get(message.getLiveId());
//            if (emitters != null && !emitters.isEmpty()) {
//                List<SseEmitter> deadEmitters = new ArrayList<>();
//                boolean hasSuccess = false;
//
//                for (SseEmitter emitter : emitters) {
//                    try {
//                        emitter.send(message);
//                        hasSuccess = true;
//                    } catch (IOException e) {
//                        log.error("推送聊天消息失败", e);
//                        deadEmitters.add(emitter);
//                    }
//                }
//
//                // 移除失效的连接
//                deadEmitters.forEach(emitter -> removeEmitter(message.getLiveId(), emitter));
//
//                // 只要有一个客户端推送成功就返回true
//                return hasSuccess;
//            } else {
//                log.warn("没有活跃的SSE连接，liveId: {}", message.getLiveId());
//                // 即使没有活跃连接，消息也已经保存到Redis，所以返回true
//                return true;
//            }
//        } catch (Exception e) {
//            log.error("发送聊天消息失败，message: {}", message, e);
//            throw new RuntimeException("发送消息失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取历史聊天记录
//     *
//     * @param liveId 直播ID
//     * @param start 开始位置
//     * @param end 结束位置
//     * @return 聊天记录列表
//     */
//    @Override
//    public List<Object> getChatHistory(String liveId, long start, long end) {
//        // 检查直播间是否存在
//        Live live = getById(Long.valueOf(liveId));
//        if (live == null) {
//            throw new ServiceException("直播间不存在");
//        }
//
//        // 从Redis获取聊天记录
//        String key = LIVE_CHAT_HISTORY_KEY_PREFIX + liveId;
//        return redisTemplate.opsForList().range(key, start, end);
//    }
//
//    /**
//     * 获取历史聊天记录总数
//     *
//     * @param liveId 直播ID
//     * @return 聊天记录总数
//     */
//    @Override
//    public Long getChatHistorySize(String liveId) {
//        // 检查直播间是否存在
//        Live live = getById(Long.valueOf(liveId));
//        if (live == null) {
//            throw new ServiceException("直播间不存在");
//        }
//
//        // 从Redis获取聊天记录总数
//        String key = LIVE_CHAT_HISTORY_KEY_PREFIX + liveId;
//        return redisTemplate.opsForList().size(key);
//    }
//
//
//}
