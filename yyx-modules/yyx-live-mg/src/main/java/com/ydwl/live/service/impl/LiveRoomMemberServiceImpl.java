package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveRoomMemberBo;
import com.ydwl.live.domain.vo.LiveRoomMemberVo;
import com.ydwl.live.domain.LiveRoomMember;
import com.ydwl.live.mapper.LiveRoomMemberMapper;
import com.ydwl.live.service.ILiveRoomMemberService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 直播间在线成员Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveRoomMemberServiceImpl implements ILiveRoomMemberService {

    private final LiveRoomMemberMapper baseMapper;

    /**
     * 查询直播间在线成员
     *
     * @param id 主键
     * @return 直播间在线成员
     */
    @Override
    public LiveRoomMemberVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询直播间在线成员列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播间在线成员分页列表
     */
    @Override
    public TableDataInfo<LiveRoomMemberVo> queryPageList(LiveRoomMemberBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveRoomMember> lqw = buildQueryWrapper(bo);
        Page<LiveRoomMemberVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的直播间在线成员列表
     *
     * @param bo 查询条件
     * @return 直播间在线成员列表
     */
    @Override
    public List<LiveRoomMemberVo> queryList(LiveRoomMemberBo bo) {
        LambdaQueryWrapper<LiveRoomMember> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveRoomMember> buildQueryWrapper(LiveRoomMemberBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveRoomMember> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveRoomMember::getId);
        lqw.eq(bo.getLiveId() != null, LiveRoomMember::getLiveId, bo.getLiveId());
        lqw.eq(bo.getUserId() != null, LiveRoomMember::getUserId, bo.getUserId());
        lqw.eq(bo.getJoinTime() != null, LiveRoomMember::getJoinTime, bo.getJoinTime());
        lqw.eq(bo.getLeaveTime() != null, LiveRoomMember::getLeaveTime, bo.getLeaveTime());
        lqw.eq(bo.getWatchDurationSeconds() != null, LiveRoomMember::getWatchDurationSeconds, bo.getWatchDurationSeconds());
        lqw.eq(StringUtils.isNotBlank(bo.getClientIp()), LiveRoomMember::getClientIp, bo.getClientIp());
        lqw.eq(StringUtils.isNotBlank(bo.getClientDevice()), LiveRoomMember::getClientDevice, bo.getClientDevice());
        return lqw;
    }

    /**
     * 新增直播间在线成员
     *
     * @param bo 直播间在线成员
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveRoomMemberBo bo) {
        LiveRoomMember add = MapstructUtils.convert(bo, LiveRoomMember.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改直播间在线成员
     *
     * @param bo 直播间在线成员
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveRoomMemberBo bo) {
        LiveRoomMember update = MapstructUtils.convert(bo, LiveRoomMember.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveRoomMember entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除直播间在线成员信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
