package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveArticleVo;
import com.ydwl.live.domain.bo.LiveArticleBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 直播文章Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveArticleService {

    /**
     * 查询直播文章
     *
     * @param id 主键
     * @return 直播文章
     */
    LiveArticleVo queryById(Long id);

    /**
     * 分页查询直播文章列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播文章分页列表
     */
    TableDataInfo<LiveArticleVo> queryPageList(LiveArticleBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的直播文章列表
     *
     * @param bo 查询条件
     * @return 直播文章列表
     */
    List<LiveArticleVo> queryList(LiveArticleBo bo);

    /**
     * 新增直播文章
     *
     * @param bo 直播文章
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveArticleBo bo);

    /**
     * 修改直播文章
     *
     * @param bo 直播文章
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveArticleBo bo);

    /**
     * 校验并批量删除直播文章信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
