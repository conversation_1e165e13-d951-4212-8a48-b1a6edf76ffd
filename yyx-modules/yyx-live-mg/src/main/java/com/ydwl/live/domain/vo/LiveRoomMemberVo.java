package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.live.domain.LiveRoomMember;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 直播间在线成员视图对象 live_room_member
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveRoomMember.class)
public class LiveRoomMemberVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成员ID
     */
    @ExcelProperty(value = "成员ID")
    private Long id;

    /**
     * 直播ID
     */
    @ExcelProperty(value = "直播ID")
    private Long liveId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 加入时间
     */
    @ExcelProperty(value = "加入时间")
    private Date joinTime;

    /**
     * 离开时间
     */
    @ExcelProperty(value = "离开时间")
    private Date leaveTime;

    /**
     * 观看时长(单位:秒)
     */
    @ExcelProperty(value = "观看时长(单位:秒)")
    private Long watchDurationSeconds;

    /**
     * 客户端IP地址
     */
    @ExcelProperty(value = "客户端IP地址")
    private String clientIp;

    /**
     * 设备信息
     */
    @ExcelProperty(value = "设备信息")
    private String clientDevice;


}
