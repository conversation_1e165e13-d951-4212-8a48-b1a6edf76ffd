package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveCategory;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 直播分类业务对象 live_category
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveCategory.class, reverseConvertGenerate = false)
public class LiveCategoryBo extends BaseEntity {

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String categoryName;

    /**
     * 分类编码(唯一标识)
     */
    private String categoryCode;

    /**
     * 父分类ID(0表示顶级分类)
     */
    private Long parentId;

    /**
     * 分类路径(格式:1,2,3)
     */
    private String path;

    /**
     * 树排序序号
     */
    private Long treeSort;

    /**
     * 树层级(从1开始)
     */
    private Long treeLevel;

    /**
     * 分类图标URL
     */
    private String iconUrl;

    /**
     * 分类封面图URL
     */
    private String coverImgUrl;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 状态(0-禁用,1-启用)
     */
    private Long status;

    /**
     * 该分类下直播数量
     */
    private Long liveCount;

    /**
     * 分类浏览次数
     */
    private Long viewCount;


}
