package com.ydwl.live.controller;

import com.ydwl.LiveTranscoding.domain.dto.FcTranscodeCallbackResponseDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsTranscodeCallbackResponseDto;
import com.ydwl.LiveTranscoding.domain.vo.UniversalTranscodeCallbackVo;
import com.ydwl.LiveTranscoding.callback.UniversalTranscodeCallbackHandler;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.live.domain.bo.LiveReplayBo;
import com.ydwl.live.domain.bo.LiveTranscodeOutputBo;
import com.ydwl.live.domain.bo.LiveTranscodeTaskBo;
import com.ydwl.live.domain.vo.LiveReplayVo;
import com.ydwl.live.domain.vo.LiveTranscodeTaskVo;
import com.ydwl.live.service.ILiveReplayService;
import com.ydwl.live.service.ILiveTranscodeOutputService;
import com.ydwl.live.service.ILiveTranscodeTaskService;
import com.ydwl.live.service.ITranscodeCallbackService;
import com.ydwl.live.service.IVideoProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 转码回调控制器
 * 用于接收阿里云转码服务的回调通知
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/transcode")
public class LiveTranscodeCallbackController extends BaseController {

    private final ILiveTranscodeTaskService liveTranscodeTaskService;
    private final ILiveTranscodeOutputService liveTranscodeOutputService;
    private final ILiveReplayService liveReplayService;
    private final ITranscodeCallbackService transcodeCallbackService;
    private final UniversalTranscodeCallbackHandler callbackHandler;
    private final IVideoProcessService videoProcessService;

    /**
     * FC转码回调接口
     * 接收阿里云函数计算转码完成的回调通知（MTS标准格式）
     *
     * @param callbackData FC转码回调数据
     * @return 处理结果
     */
    @Log(title = "FC转码回调", businessType = BusinessType.OTHER)
    @PostMapping("/fc/callback")
    public R<String> fcTranscodeCallback(@RequestBody FcTranscodeCallbackResponseDto callbackData) {
        try {
            log.info("接收到FC转码回调通知: {}", callbackData);

            String userData = callbackData.getBusinessId();
            String eventType = callbackData.getEventType();

            if (userData == null) {
                log.warn("FC转码回调数据中缺少UserData字段");
                return R.fail("回调数据格式错误：缺少UserData");
            }
            
            // 检查任务状态，避免重复处理
            LiveTranscodeTaskVo taskVo = liveTranscodeTaskService.queryByBizId(userData);
            if (taskVo != null && taskVo.getStatus() != null && taskVo.getStatus() == 2L) {
                log.info("FC转码回调：任务已完成，避免重复处理。BizID: {}", userData);
                return R.ok("任务已处理，不重复处理");
            }

            // 委托给服务层处理
            if ("TranscodeSuccess".equals(eventType)) {
                transcodeCallbackService.handleFcTranscodeSuccess(callbackData);
            } else if ("TranscodeFail".equals(eventType)) {
                transcodeCallbackService.handleFcTranscodeError(callbackData);
            } else {
                log.warn("未知的FC转码事件类型: {}", eventType);
                return R.fail("未知的转码事件类型: " + eventType);
            }

            return R.ok("FC转码回调处理成功");

        } catch (Exception e) {
            log.error("处理FC转码回调失败", e);
            return R.fail("处理FC转码回调失败: " + e.getMessage());
        }
    }

    /**
     * MTS转码回调接口
     * 接收阿里云媒体转码服务完成的回调通知
     *
     * @param callbackData MTS转码回调数据
     * @return 处理结果
     */
    @Log(title = "MTS转码回调", businessType = BusinessType.OTHER)
    @PostMapping("/mts/callback")
    public R<String> mtsTranscodeCallback(@RequestBody MtsTranscodeCallbackResponseDto callbackData) {
        try {
            log.info("接收到MTS转码回调通知: {}", callbackData);

            String jobId = callbackData.getJobId();
            String state = callbackData.getState();

            if (jobId == null) {
                log.warn("MTS转码回调数据中缺少JobId字段");
                return R.fail("回调数据格式错误：缺少JobId");
            }

            // 委托给服务层处理
            if ("Success".equals(state)) {
                transcodeCallbackService.handleMtsTranscodeSuccess(UniversalTranscodeCallbackVo.fromMtsCallback(callbackData));
            } else if ("Fail".equals(state)) {
                transcodeCallbackService.handleMtsTranscodeError(UniversalTranscodeCallbackVo.fromMtsCallback(callbackData));
            } else {
                log.warn("未知的MTS转码状态: {}", state);
                return R.fail("未知的转码状态: " + state);
            }

            return R.ok("MTS转码回调处理成功");

        } catch (Exception e) {
            log.error("处理MTS转码回调失败", e);
            return R.fail("处理MTS转码回调失败: " + e.getMessage());
        }
    }

    /**
     * 接收转码完成回调（通用接口，保持向后兼容）
     *
     * @param callbackData 回调数据
     * @return 处理结果
     */
    @Log(title = "转码回调", businessType = BusinessType.OTHER)
    @PostMapping("/callback")
    public R<String> transcodeCallback(@RequestBody Map<String, Object> callbackData) {
        try {
            log.info("接收到转码回调通知: {}", callbackData);

            // 尝试将回调数据解析为统一格式
            // 优先判断是否为FC格式回调（Type、EventType字段）
            if (callbackData.containsKey("Type") && callbackData.containsKey("EventType")) {
                String eventType = (String) callbackData.get("EventType");
                
                // 尝试将回调数据转换为FC转码回调
                if (eventType != null && (eventType.equals("TranscodeSuccess") || eventType.equals("TranscodeFail"))) {
                    try {
                        // 从UserData获取bizId（兼容FC标准）
                        String userData = (String) callbackData.get("UserData");
                        
                        if (userData != null) {
                            // 检查任务状态，避免重复处理
                            LiveTranscodeTaskVo taskVo = liveTranscodeTaskService.queryByBizId(userData);
                            if (taskVo != null && taskVo.getStatus() != null && taskVo.getStatus() == 2L) {
                                log.info("FC转码回调：任务已完成，避免重复处理。BizID: {}", userData);
                                return R.ok("任务已处理，不重复处理");
                            }
                            
                            // 使用专用的FC回调处理服务
                            if ("TranscodeSuccess".equals(eventType)) {
                                // 调用FC转码成功处理逻辑
                                transcodeCallbackService.handleFcTranscodeSuccess(mapToFcCallback(callbackData));
                            } else if ("TranscodeFail".equals(eventType)) {
                                // 调用FC转码失败处理逻辑
                                transcodeCallbackService.handleFcTranscodeError(mapToFcCallback(callbackData));
                            }
                            
                            return R.ok("FC转码回调处理成功");
                        }
                    } catch (Exception e) {
                        log.error("处理FC格式转码回调失败", e);
                    }
                }
            }

            // 尝试使用传统格式解析（bizId、status字段）
            String bizId = (String) callbackData.get("bizId");
            String status = (String) callbackData.get("status");
            String outputPath = (String) callbackData.get("outputPath");
            String masterPlaylist = (String) callbackData.get("masterPlaylist");
            String errorMessage = (String) callbackData.get("errorMessage");

            // 尝试从UserData获取bizId（兼容FC标准）
            if (bizId == null) {
                bizId = (String) callbackData.get("UserData");
            }

            if (bizId == null) {
                log.warn("回调数据中缺少bizId字段");
                return R.fail("回调数据格式错误：缺少bizId");
            }

            // 根据状态更新转码任务
            if ("success".equals(status)) {
                handleTranscodeSuccess(bizId, outputPath, masterPlaylist, callbackData);
            } else if ("error".equals(status)) {
                handleTranscodeError(bizId, errorMessage);
            } else {
                log.warn("未知的转码状态: {}", status);
                return R.fail("未知的转码状态: " + status);
            }

            return R.ok("回调处理成功");

        } catch (Exception e) {
            log.error("处理转码回调失败", e);
            return R.fail("处理转码回调失败: " + e.getMessage());
        }
    }

    /**
     * 将通用Map转换为FC回调对象
     */
    private FcTranscodeCallbackResponseDto mapToFcCallback(Map<String, Object> callbackData) {
        FcTranscodeCallbackResponseDto fcCallback = new FcTranscodeCallbackResponseDto();
        fcCallback.setType((String) callbackData.get("Type"));
        fcCallback.setEventType((String) callbackData.get("EventType"));
        fcCallback.setJobId((String) callbackData.get("JobId"));
        fcCallback.setState((String) callbackData.get("State"));
        fcCallback.setCode((String) callbackData.get("Code"));
        fcCallback.setMessage((String) callbackData.get("Message"));
        fcCallback.setUserData((String) callbackData.get("UserData"));
        
        // 解析Input字段
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> inputMap = (Map<String, Object>) callbackData.get("Input");
            if (inputMap != null) {
                FcTranscodeCallbackResponseDto.InputDTO inputDTO = new FcTranscodeCallbackResponseDto.InputDTO();
                inputDTO.setBucket((String) inputMap.get("Bucket"));
                inputDTO.setLocation((String) inputMap.get("Location"));
                inputDTO.setObject((String) inputMap.get("Object"));
                fcCallback.setInput(inputDTO);
            }
        } catch (Exception e) {
            log.warn("解析Input字段失败", e);
        }
        
        // 解析Outputs字段
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> outputsList = (List<Map<String, Object>>) callbackData.get("Outputs");
            if (outputsList != null && !outputsList.isEmpty()) {
                List<FcTranscodeCallbackResponseDto.OutputDTO> outputs = outputsList.stream()
                    .map(outputMap -> {
                        FcTranscodeCallbackResponseDto.OutputDTO outputDTO = new FcTranscodeCallbackResponseDto.OutputDTO();
                        outputDTO.setObjectName((String) outputMap.get("ObjectName"));
                        outputDTO.setBucket((String) outputMap.get("Bucket"));
                        outputDTO.setLocation((String) outputMap.get("Location"));
                        outputDTO.setTemplateId((String) outputMap.get("TemplateId"));
                        outputDTO.setState((String) outputMap.get("State"));
                        outputDTO.setCode((String) outputMap.get("Code"));
                        outputDTO.setMessage((String) outputMap.get("Message"));
                        outputDTO.setWidth((String) outputMap.get("Width"));
                        outputDTO.setHeight((String) outputMap.get("Height"));
                        outputDTO.setDuration((String) outputMap.get("Duration"));
                        outputDTO.setFilesize((String) outputMap.get("Filesize"));
                        outputDTO.setBitrate((String) outputMap.get("Bitrate"));
                        outputDTO.setFps((String) outputMap.get("Fps"));
                        return outputDTO;
                    })
                    .toList();
                fcCallback.setOutputs(outputs);
            }
        } catch (Exception e) {
            log.warn("解析Outputs字段失败", e);
        }
        
        return fcCallback;
    }

    /**
     * 获取转码任务状态
     *
     * @param bizId 业务ID
     * @return 任务状态
     */
    @GetMapping("/status/{bizId}")
    public R<Map<String, Object>> getTranscodeStatus(@PathVariable String bizId) {
        try {
            log.info("查询转码任务状态，BizId: {}", bizId);

            // 根据bizId查询转码任务状态
            LiveTranscodeTaskVo taskVo = liveTranscodeTaskService.queryByBizId(bizId);
            if (taskVo == null) {
                return R.fail("未找到对应的转码任务");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskVo.getId());
            result.put("bizId", taskVo.getBizId());
            result.put("status", taskVo.getStatus());
            result.put("progress", taskVo.getProgress());
            result.put("outputUrl", taskVo.getOutputUrl());
            result.put("errorMsg", taskVo.getErrorMsg());
            result.put("startTime", taskVo.getStartTime());
            result.put("endTime", taskVo.getEndTime());
            result.put("retryCount", taskVo.getRetryCount());

            // 状态描述
            String statusDesc = getStatusDescription(taskVo.getStatus());
            result.put("statusDesc", statusDesc);

            return R.ok(result);

        } catch (Exception e) {
            log.error("查询转码任务状态失败，BizId: {}", bizId, e);
            return R.fail("查询转码任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 处理转码成功的回调
     */
    private void handleTranscodeSuccess(String bizId, String outputPath, String masterPlaylist,
                                      Map<String, Object> callbackData) {
        try {
            log.info("转码成功，BizId: {}, 输出路径: {}, 主播放列表: {}", bizId, outputPath, masterPlaylist);

            // 1. 根据bizId查找转码任务
            LiveTranscodeTaskVo taskVo = liveTranscodeTaskService.queryByBizId(bizId);
            if (taskVo == null) {
                log.error("未找到对应的转码任务，BizId: {}", bizId);
                return;
            }

            // 2. 更新转码任务状态为已完成
            LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
            taskBo.setId(taskVo.getId());
            taskBo.setStatus(2L); // 已完成
            
            // 确定主播放列表URL
            String outputUrl = null;
            
            // 优先使用传入的masterPlaylist
            if (masterPlaylist != null) {
                outputUrl = masterPlaylist;
            } 
            // 其次使用outputPath
            else if (outputPath != null) {
                outputUrl = outputPath;
            } 
            // 再次尝试从FC格式的Outputs中查找m3u8文件
            else if (callbackData.containsKey("Outputs")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> fcOutputs = (List<Map<String, Object>>) callbackData.get("Outputs");
                if (fcOutputs != null && !fcOutputs.isEmpty()) {
                    // 寻找m3u8文件作为主播放列表
                    for (Map<String, Object> output : fcOutputs) {
                        String objectName = (String) output.get("ObjectName");
                        if (objectName != null && objectName.endsWith(".m3u8")) {
                            String bucket = (String) output.get("Bucket");
                            String location = (String) output.get("Location");
                            // 构建OSS URL
                            if (bucket != null) {
                                if ("video-ydwl".equals(bucket)) {
                                    // 使用新的域名前缀
                                    outputUrl = "https://mps.play.ycyyx.com/" + objectName;
                                } else {
                                    // 保留原有的构建方式作为备选
                                    outputUrl = "https://" + bucket + "." + location + ".aliyuncs.com/" + objectName;
                                }
                            } else {
                                outputUrl = objectName;
                            }
                            break;
                        }
                    }
                    
                    // 如果没找到m3u8文件，使用第一个输出
                    if (outputUrl == null && !fcOutputs.isEmpty()) {
                        Map<String, Object> firstOutput = fcOutputs.get(0);
                        String objectName = (String) firstOutput.get("ObjectName");
                        String bucket = (String) firstOutput.get("Bucket");
                        String location = (String) firstOutput.get("Location");
                        if (objectName != null && bucket != null) {
                            if ("video-ydwl".equals(bucket)) {
                                // 使用新的域名前缀
                                outputUrl = "https://mps.play.ycyyx.com/" + objectName;
                            } else {
                                // 保留原有的构建方式作为备选
                                outputUrl = "https://" + bucket + "." + location + ".aliyuncs.com/" + objectName;
                            }
                        } else if (objectName != null) {
                            outputUrl = objectName;
                        }
                    }
                }
            }
            
            // 设置输出URL
            taskBo.setOutputUrl(outputUrl);
            taskBo.setProgress(100L);
            taskBo.setEndTime(new Date());
            taskBo.setCallbackStatus(1L); // 已回调
            
            // 设置JobId（如果存在）
            if (callbackData.containsKey("JobId")) {
                taskBo.setProviderJobId((String) callbackData.get("JobId"));
            }

            boolean taskUpdated = liveTranscodeTaskService.updateByBo(taskBo);
            if (!taskUpdated) {
                log.error("更新转码任务状态失败，BizId: {}", bizId);
                return;
            }

            // 3. 创建转码输出记录
            createTranscodeOutputRecords(taskVo.getId(), callbackData);

            // 4. 创建或更新直播回放记录
            createOrUpdateLiveReplay(taskVo, outputUrl);

            log.info("转码成功处理完成，BizId: {}", bizId);

        } catch (Exception e) {
            log.error("处理转码成功回调失败，BizId: {}", bizId, e);
        }
    }

    /**
     * 创建转码输出记录
     */
    private void createTranscodeOutputRecords(Long taskId, Map<String, Object> callbackData) {
        try {
            // 尝试解析不同格式的输出
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> outputs = (List<Map<String, Object>>) callbackData.get("outputs");
            
            // 如果没有找到传统格式的outputs，尝试FC格式的Outputs
            if ((outputs == null || outputs.isEmpty()) && callbackData.containsKey("Outputs")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> fcOutputs = (List<Map<String, Object>>) callbackData.get("Outputs");
                if (fcOutputs != null && !fcOutputs.isEmpty()) {
                    int successCount = 0;
                    for (Map<String, Object> fcOutput : fcOutputs) {
                        LiveTranscodeOutputBo outputBo = new LiveTranscodeOutputBo();
                        outputBo.setTaskId(taskId);
                        
                        // 从FC格式转换为传统格式
                        String width = (String) fcOutput.get("Width");
                        String height = (String) fcOutput.get("Height");
                        String resolution = (width != null && height != null) ? width + "x" + height : null;
                        String objectName = (String) fcOutput.get("ObjectName");
                        
                        outputBo.setDefinition(fcOutput.get("TemplateId") != null ? 
                                             (String) fcOutput.get("TemplateId") : 
                                             getDefinitionFromResolution(resolution));
                        outputBo.setResolution(resolution);
                        outputBo.setFormat(objectName != null && objectName.contains(".") ? 
                                         objectName.substring(objectName.lastIndexOf(".") + 1) : "hls");
                        
                        // 构建URL（实际应用中应该有OSS域名配置）
                        String bucket = (String) fcOutput.get("Bucket");
                        String location = (String) fcOutput.get("Location");
                        if (bucket != null && objectName != null) {
                            if ("video-ydwl".equals(bucket)) {
                                // 使用新的域名前缀
                                outputBo.setUrl("https://mps.play.ycyyx.com/" + objectName);
                            } else {
                                // 保留原有的构建方式作为备选
                                outputBo.setUrl("https://" + bucket + "." + location + ".aliyuncs.com/" + objectName);
                            }
                        } else {
                            outputBo.setUrl(objectName);
                        }
                        
                        // 转换数值字段
                        if (fcOutput.get("Duration") != null) {
                            try {
                                outputBo.setDuration(Math.round(Double.parseDouble((String) fcOutput.get("Duration"))));
                            } catch (NumberFormatException e) {
                                log.warn("无法解析转码时长: {}", fcOutput.get("Duration"));
                            }
                        }
                        
                        if (fcOutput.get("Filesize") != null) {
                            try {
                                outputBo.setFileSize(Long.parseLong((String) fcOutput.get("Filesize")));
                            } catch (NumberFormatException e) {
                                log.warn("无法解析文件大小: {}", fcOutput.get("Filesize"));
                            }
                        }
                        
                        if (fcOutput.get("Bitrate") != null) {
                            try {
                                outputBo.setBitRate(Long.parseLong((String) fcOutput.get("Bitrate")));
                            } catch (NumberFormatException e) {
                                log.warn("无法解析码率: {}", fcOutput.get("Bitrate"));
                            }
                        }
                        
                        outputBo.setStatus("Success".equalsIgnoreCase((String) fcOutput.get("State")) ? 1L : 0L);
                        
                        if (liveTranscodeOutputService.insertByBo(outputBo)) {
                            successCount++;
                        }
                    }
                    
                    log.info("FC转码输出记录创建：总计{}条，成功{}条", fcOutputs.size(), successCount);
                    return; // 处理完FC格式后直接返回
                }
            }

            // 如果有传统格式的outputs，按原方式处理
            if (outputs != null && !outputs.isEmpty()) {
                for (Map<String, Object> output : outputs) {
                    LiveTranscodeOutputBo outputBo = new LiveTranscodeOutputBo();
                    outputBo.setTaskId(taskId);
                    outputBo.setDefinition((String) output.get("definition"));
                    outputBo.setResolution((String) output.get("resolution"));
                    outputBo.setFormat((String) output.get("format"));
                    outputBo.setUrl((String) output.get("url"));
                    outputBo.setSegmentTime(getLongValue(output, "segmentTime"));
                    outputBo.setFileSize(getLongValue(output, "fileSize"));
                    outputBo.setBitRate(getLongValue(output, "bitRate"));
                    outputBo.setDuration(getLongValue(output, "duration"));

                    liveTranscodeOutputService.insertByBo(outputBo);
                }
            } else {
                // 如果没有详细的输出信息，创建一个默认记录
                LiveTranscodeOutputBo outputBo = new LiveTranscodeOutputBo();
                outputBo.setTaskId(taskId);
                outputBo.setDefinition("AUTO");
                outputBo.setResolution("AUTO");
                outputBo.setFormat("hls");
                outputBo.setUrl((String) callbackData.get("masterPlaylist"));

                liveTranscodeOutputService.insertByBo(outputBo);
            }

        } catch (Exception e) {
            log.error("创建转码输出记录失败，任务ID: {}", taskId, e);
        }
    }
    
    /**
     * 根据分辨率获取清晰度
     */
    private String getDefinitionFromResolution(String resolution) {
        if (resolution == null) {
            return "AUTO";
        }
        
        try {
            String[] parts = resolution.split("x");
            int height = Integer.parseInt(parts[1]);
            
            if (height <= 240) return "LD"; // 流畅
            if (height <= 480) return "SD"; // 标清
            if (height <= 720) return "HD"; // 高清
            if (height <= 1080) return "FHD"; // 超清
            return "UHD"; // 超高清
            
        } catch (Exception e) {
            return "AUTO";
        }
    }

    /**
     * 创建或更新直播回放记录
     */
    private void createOrUpdateLiveReplay(LiveTranscodeTaskVo taskVo, String replayUrl) {
        try {
            // 先查询是否已存在该直播的回放记录
            LiveReplayBo queryBo = new LiveReplayBo();
            queryBo.setLiveId(taskVo.getLiveId());
            List<LiveReplayVo> existingReplays = liveReplayService.queryList(queryBo);
            
            // 处理OSS路径，转换为完整的HTTPS链接
            String finalReplayUrl = replayUrl;
            if (replayUrl != null) {
                // 如果是相对路径，则添加域名前缀
                if (!replayUrl.startsWith("http")) {
                    // 移除oss://前缀如果存在
                    if (replayUrl.startsWith("oss://")) {
                        finalReplayUrl = replayUrl.substring(6);
                    }
                    // 如果以video-ydwl/开头，直接添加域名前缀
                    if (finalReplayUrl.startsWith("video-ydwl/") || finalReplayUrl.equals("video-ydwl")) {
                        finalReplayUrl = "https://mps.play.ycyyx.com/" + finalReplayUrl;
                    } 
                    // 否则检查是否包含存储桶名称
                    else if (!finalReplayUrl.startsWith("https://")) {
                        finalReplayUrl = "https://mps.play.ycyyx.com/video-ydwl/" + finalReplayUrl;
                    }
                }
            }
            
            LiveReplayBo replayBo = new LiveReplayBo();
            replayBo.setLiveId(taskVo.getLiveId());
            replayBo.setTranscodeTaskId(taskVo.getId());
            replayBo.setReplayUrl(finalReplayUrl);
            replayBo.setStatus(1L); // 可用
            replayBo.setDuration(taskVo.getDuration());
            replayBo.setAvailableTime(new Date());
            replayBo.setAccessType(0L); // 默认公开
            replayBo.setViewCount(0L);

            // 设置过期时间（默认30天后过期）
            Date expiryTime = new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000);
            replayBo.setExpiryTime(expiryTime);

            boolean success = false;
            if (!existingReplays.isEmpty()) {
                // 已存在记录，进行更新
                LiveReplayVo existingReplay = existingReplays.get(0);
                replayBo.setId(existingReplay.getId());
                success = liveReplayService.updateByBo(replayBo);
                if (success) {
                    log.info("直播回放记录更新成功，直播ID: {}, 回放地址: {}", taskVo.getLiveId(), finalReplayUrl);
                } else {
                    log.error("直播回放记录更新失败，直播ID: {}", taskVo.getLiveId());
                }
            } else {
                // 不存在记录，进行插入
                success = liveReplayService.insertByBo(replayBo);
                if (success) {
                    log.info("直播回放记录创建成功，直播ID: {}, 回放地址: {}", taskVo.getLiveId(), finalReplayUrl);
                } else {
                    log.error("直播回放记录创建失败，直播ID: {}", taskVo.getLiveId());
                }
            }
            
            // 提取视频对象键，用于生成封面图
            try {
                // 从replayUrl中提取视频在OSS中的对象键
                String objectKey = extractObjectKeyFromUrl(finalReplayUrl);
                log.info("提取到的视频对象键: {}", objectKey);
                
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(objectKey)) {
                    // 1. 生成封面图（第一帧截图）
                    String coverOutputPrefix = "cover/" + taskVo.getId();
                    String firstFrameTaskId = videoProcessService.generateFirstFrameSnapshot(
                        objectKey, "video-ydwl", coverOutputPrefix);
                        
                    if (firstFrameTaskId != null) {
                        log.info("成功提交回放封面图生成任务，TaskID: {}, 对象键: {}", firstFrameTaskId, objectKey);
                    } else {
                        log.warn("提交回放封面图生成任务失败，对象键: {}", objectKey);
                    }
                } else {
                    log.warn("无法从回放URL提取对象键，跳过生成封面图：{}", finalReplayUrl);
                }
            } catch (Exception e) {
                log.error("生成回放封面图异常", e);
            }
        } catch (Exception e) {
            log.error("创建/更新直播回放记录失败，直播ID: {}", taskVo.getLiveId(), e);
        }
    }
    
    /**
     * 从URL中提取对象键
     */
    private String extractObjectKeyFromUrl(String url) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(url)) {
            return null;
        }
        
        // 移除协议部分
        String processedUrl = url;
        if (url.startsWith("https://")) {
            processedUrl = url.substring(8);
        } else if (url.startsWith("http://")) {
            processedUrl = url.substring(7);
        }
        
        // 移除域名部分
        int domainEndIndex = processedUrl.indexOf('/');
        if (domainEndIndex > 0) {
            processedUrl = processedUrl.substring(domainEndIndex + 1);
        }
        
        // 处理m3u8链接
        if (processedUrl.endsWith(".m3u8")) {
            // 尝试找到mp4源文件
            processedUrl = processedUrl.substring(0, processedUrl.length() - 5) + ".mp4";
        }
        
        log.info("从URL提取的对象键: {}", processedUrl);
        return processedUrl;
    }

    /**
     * 处理转码失败的回调
     */
    private void handleTranscodeError(String bizId, String errorMessage) {
        try {
            log.error("转码失败，BizId: {}, 错误信息: {}", bizId, errorMessage);

            // 1. 根据bizId查找转码任务
            LiveTranscodeTaskVo taskVo = liveTranscodeTaskService.queryByBizId(bizId);
            if (taskVo == null) {
                log.error("未找到对应的转码任务，BizId: {}", bizId);
                return;
            }

            // 2. 更新转码任务状态为失败
            LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
            taskBo.setId(taskVo.getId());
            taskBo.setStatus(3L); // 失败
            taskBo.setErrorMsg(errorMessage);
            taskBo.setEndTime(new Date());
            taskBo.setCallbackStatus(1L); // 已回调

            // 增加重试次数
            Long retryCount = taskVo.getRetryCount() != null ? taskVo.getRetryCount() : 0L;
            taskBo.setRetryCount(retryCount + 1);

            boolean taskUpdated = liveTranscodeTaskService.updateByBo(taskBo);
            if (!taskUpdated) {
                log.error("更新转码任务状态失败，BizId: {}", bizId);
                return;
            }

            // 3. 根据重试策略决定是否重新提交转码任务
            if (retryCount < 3) { // 最多重试3次
                log.info("转码失败，准备重试，BizId: {}, 当前重试次数: {}", bizId, retryCount + 1);
            } else {
                log.warn("转码失败，已达到最大重试次数，BizId: {}", bizId);
            }

            log.info("转码失败处理完成，BizId: {}", bizId);

        } catch (Exception e) {
            log.error("处理转码失败回调失败，BizId: {}", bizId, e);
        }
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(Long status) {
        if (status == null) return "未知";

        switch (status.intValue()) {
            case 0: return "待处理";
            case 1: return "处理中";
            case 2: return "已完成";
            case 3: return "已失败";
            case 4: return "已取消";
            default: return "未知状态";
        }
    }

    /**
     * 从Map中获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;

        if (value instanceof Number) {
            return ((Number) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
