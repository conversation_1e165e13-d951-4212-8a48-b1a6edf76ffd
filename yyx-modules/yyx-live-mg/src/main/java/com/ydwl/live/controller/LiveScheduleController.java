package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveScheduleVo;
import com.ydwl.live.domain.bo.LiveScheduleBo;
import com.ydwl.live.service.ILiveScheduleService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 直播预告
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/schedule")
public class LiveScheduleController extends BaseController {

    private final ILiveScheduleService liveScheduleService;

    /**
     * 查询直播预告列表
     */
    @SaCheckPermission("live:schedule:list")
    @GetMapping("/list")
    public TableDataInfo<LiveScheduleVo> list(LiveScheduleBo bo, PageQuery pageQuery) {
        return liveScheduleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出直播预告列表
     */
    @SaCheckPermission("live:schedule:export")
    @Log(title = "直播预告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveScheduleBo bo, HttpServletResponse response) {
        List<LiveScheduleVo> list = liveScheduleService.queryList(bo);
        ExcelUtil.exportExcel(list, "直播预告", LiveScheduleVo.class, response);
    }

    /**
     * 获取直播预告详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:schedule:query")
    @GetMapping("/{id}")
    public R<LiveScheduleVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveScheduleService.queryById(id));
    }

    /**
     * 新增直播预告
     */
    @SaCheckPermission("live:schedule:add")
    @Log(title = "直播预告", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveScheduleBo bo) {
        return toAjax(liveScheduleService.insertByBo(bo));
    }

    /**
     * 修改直播预告
     */
    @SaCheckPermission("live:schedule:edit")
    @Log(title = "直播预告", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveScheduleBo bo) {
        return toAjax(liveScheduleService.updateByBo(bo));
    }

    /**
     * 删除直播预告
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:schedule:remove")
    @Log(title = "直播预告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveScheduleService.deleteWithValidByIds(List.of(ids), true));
    }
}
