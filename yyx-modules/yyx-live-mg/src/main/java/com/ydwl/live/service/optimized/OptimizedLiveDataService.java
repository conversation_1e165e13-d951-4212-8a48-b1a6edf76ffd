package com.ydwl.live.service.optimized;

import com.ydwl.common.core.domain.R;
import com.ydwl.live.domain.bo.LiveBo;
import com.ydwl.live.domain.model.valueobject.StreamInfo;
import com.ydwl.live.event.LiveCreatedEvent;
import com.ydwl.live.event.LiveEventPublisher;
import com.ydwl.live.event.LiveStreamRefreshEvent;
import com.ydwl.live.service.ILiveDataService;
import com.ydwl.live.service.ILiveService;
import com.ydwl.live.service.ILiveStreamService;
import com.ydwl.live.service.impl.LiveStateManager;
import com.ydwl.live.service.model.LiveStreamInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.HashMap;
import java.util.Map;

/**
 * 优化后的直播数据服务
 * 
 * 解决事务边界、缓存一致性等问题
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OptimizedLiveDataService implements ILiveDataService {
    
    private final ILiveService liveService;
    private final ILiveStreamService liveStreamService;
    private final OptimizedLiveStateManager stateManager;
    private final LiveEventPublisher eventPublisher;
    
    /**
     * 获取直播详情（带缓存）
     */
    @Override
    @Cacheable(value = "live:detail", key = "#liveId", unless = "#result == null")
    public Map<String, Object> getLiveDetail(Long liveId) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取直播基本信息
            var liveInfo = liveService.queryById(liveId);
            if (liveInfo == null) {
                log.warn("直播不存在：liveId={}", liveId);
                return null;
            }
            result.put("liveInfo", liveInfo);
            
            // 获取推流信息
            LiveStreamInfo streamInfo = liveStreamService.getStreamInfoByLiveId(liveId);
            if (streamInfo != null) {
                result.put("streamInfo", streamInfo);
                result.put("playUrls", streamInfo.getPlayUrls());
            }
            
            // 获取直播状态（从缓存）
            LiveStateManager.LiveStatus status = stateManager.getLiveStatus(liveId);
            if (status != null) {
                result.put("liveStatus", status.getValue());
                result.put("liveStatusText", status.name());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取直播详情异常：liveId={}", liveId, e);
            return null;
        }
    }
    
    /**
     * 创建直播并生成推流信息（优化事务边界）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"live:detail", "live:list"}, allEntries = true)
    public R<Map<String, Object>> createLiveWithStream(LiveBo bo) {
        try {
            // 1. 核心业务逻辑在事务内完成
            boolean success = liveService.insertByBo(bo);
            if (!success) {
                log.error("创建直播基本信息失败");
                return R.fail("创建直播失败");
            }
            
            Long liveId = bo.getId();
            if (liveId == null) {
                log.error("获取直播ID失败");
                return R.fail("创建直播失败：无法获取直播ID");
            }
            
            // 2. 创建推流信息
            LiveStreamInfo streamInfo = liveStreamService.createStreamForLive(liveId, bo.getTitle());
            if (streamInfo == null) {
                log.error("创建推流信息失败：liveId={}", liveId);
                throw new RuntimeException("创建推流信息失败");
            }
            
            // 3. 事务提交后发布事件（避免事务回滚导致事件丢失）
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        eventPublisher.publish(new LiveCreatedEvent(liveId, bo));
                        log.info("直播创建事件发布成功：liveId={}", liveId);
                    } catch (Exception e) {
                        log.error("发布直播创建事件失败：liveId={}", liveId, e);
                    }
                }
            });
            
            // 4. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("liveId", liveId);
            result.put("streamId", streamInfo.getId());
            result.put("pushUrl", streamInfo.getPushUrl());
            result.put("playUrls", streamInfo.getPlayUrls());
            
            log.info("创建直播成功：liveId={}, title={}", liveId, bo.getTitle());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("创建直播异常", e);
            return R.fail("创建直播失败：" + e.getMessage());
        }
    }
    
    /**
     * 刷新推流信息（优化事务边界）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "live:detail", key = "#liveId")
    public R<Map<String, Object>> refreshLiveStream(Long liveId) {
        try {
            // 1. 检查直播是否存在
            var live = liveService.queryById(liveId);
            if (live == null) {
                return R.fail("直播不存在");
            }
            
            // 2. 刷新推流信息
            LiveStreamInfo streamInfo = liveStreamService.refreshStreamInfo(liveId);
            if (streamInfo == null) {
                return R.fail("刷新推流信息失败");
            }
            
            // 3. 事务提交后发布事件
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        eventPublisher.publish(new LiveStreamRefreshEvent(liveId));
                        log.info("推流刷新事件发布成功：liveId={}", liveId);
                    } catch (Exception e) {
                        log.error("发布推流刷新事件失败：liveId={}", liveId, e);
                    }
                }
            });
            
            // 4. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("liveId", liveId);
            result.put("streamId", streamInfo.getId());
            result.put("pushUrl", streamInfo.getPushUrl());
            result.put("playUrls", streamInfo.getPlayUrls());
            
            log.info("刷新推流信息成功：liveId={}", liveId);
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("刷新推流信息异常：liveId={}", liveId, e);
            return R.fail("刷新推流信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新直播状态（使用优化后的状态管理器）
     */
    @Override
    @CacheEvict(value = {"live:detail", "live:status"}, key = "#liveId")
    public R<Void> updateLiveStatus(Long liveId, LiveStateManager.LiveStatus status) {
        try {
            boolean success = stateManager.updateLiveStatusSafely(liveId, status);
            return success ? R.ok() : R.fail("更新直播状态失败");
        } catch (IllegalArgumentException e) {
            log.warn("更新直播状态参数错误：liveId={}, status={}, error={}", liveId, status, e.getMessage());
            return R.fail(e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("更新直播状态不合法：liveId={}, status={}, error={}", liveId, status, e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("更新直播状态异常：liveId={}, status={}", liveId, status, e);
            return R.fail("更新直播状态失败：系统繁忙，请稍后重试");
        }
    }
    
    /**
     * 批量获取直播状态（用于列表查询优化）
     */
    @Cacheable(value = "live:status:batch", key = "#liveIds.hashCode()")
    public Map<Long, LiveStateManager.LiveStatus> batchGetLiveStatus(java.util.List<Long> liveIds) {
        Map<Long, LiveStateManager.LiveStatus> statusMap = new HashMap<>();
        
        for (Long liveId : liveIds) {
            try {
                LiveStateManager.LiveStatus status = stateManager.getLiveStatus(liveId);
                if (status != null) {
                    statusMap.put(liveId, status);
                }
            } catch (Exception e) {
                log.warn("获取直播状态失败：liveId={}", liveId, e);
            }
        }
        
        return statusMap;
    }
    
    /**
     * 预热缓存
     */
    public void warmupCache(Long liveId) {
        try {
            // 预热直播详情缓存
            getLiveDetail(liveId);
            
            // 预热状态缓存
            stateManager.getLiveStatus(liveId);
            
            log.debug("缓存预热完成：liveId={}", liveId);
        } catch (Exception e) {
            log.warn("缓存预热失败：liveId={}", liveId, e);
        }
    }
}
