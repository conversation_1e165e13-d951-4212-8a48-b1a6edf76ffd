package com.ydwl.live.domain.bo;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 确认上传请求对象
 *
 * <AUTHOR> Yi
 */
@Data
public class ConfirmUploadBo {

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空")
    private Long liveId;

    /**
     * 对象键（OSS中的文件路径）
     */
    @NotBlank(message = "对象键不能为空")
    private String objectKey;

    /**
     * 原始文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    /**
     * 文件类型
     */
    @NotBlank(message = "文件类型不能为空")
    private String contentType;

    /**
     * 文件MD5值（可选）
     */
    private String fileMd5;

    /**
     * 视频时长（秒，可选）
     */
    private Double videoDurationSeconds;

    /**
     * 视频分辨率（可选）
     */
    private String videoResolution;

    /**
     * 视频码率（Kbps，可选）
     */
    private Integer videoBitrateKbps;

    /**
     * 帧率
     */
    private Double frameRate;

    /**
     * 视频编码格式
     */
    private String videoCodec;

    /**
     * 音频编码格式
     */
    private String audioCodec;

    /**
     * 宽高比
     */
    private String aspectRatio;

    /**
     * 创建日期
     */
    private String createdDate;

    /**
     * 最后修改日期
     */
    private String lastModifiedDate;
} 