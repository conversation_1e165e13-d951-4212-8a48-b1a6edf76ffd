package com.ydwl.live.domain.service;

import com.ydwl.live.domain.model.LiveAggregate;
import com.ydwl.live.domain.model.valueobject.*;
import com.ydwl.live.domain.repository.LiveRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 直播领域服务
 * 
 * 处理跨聚合的业务逻辑和复杂的业务规则
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LiveDomainService {
    
    private final LiveRepository liveRepository;
    private final StreamGenerationService streamGenerationService;
    
    /**
     * 创建直播
     */
    public LiveAggregate createLive(LiveInfo liveInfo, LiveSettings settings) {
        // 验证业务规则
        validateLiveCreation(liveInfo);
        
        // 创建直播聚合
        LiveAggregate aggregate = LiveAggregate.create(liveInfo, settings);
        
        // 生成推流信息
        StreamInfo streamInfo = streamGenerationService.generateStreamInfo(
            aggregate.getLiveId(), liveInfo.getTitle());
        aggregate.setStreamInfo(streamInfo);
        
        // 保存聚合
        liveRepository.save(aggregate);
        
        log.info("创建直播成功: liveId={}, title={}", 
                aggregate.getLiveId().getValue(), liveInfo.getTitle());
        
        return aggregate;
    }
    
    /**
     * 开始直播
     */
    public void startLive(LiveId liveId) {
        LiveAggregate aggregate = liveRepository.findById(liveId)
            .orElseThrow(() -> new IllegalArgumentException("直播不存在: " + liveId.getValue()));
        
        // 验证是否可以开始直播
        validateLiveStart(aggregate);
        
        // 开始直播
        aggregate.startLive();
        
        // 保存聚合
        liveRepository.save(aggregate);
        
        log.info("开始直播: liveId={}", liveId.getValue());
    }
    
    /**
     * 结束直播
     */
    public void endLive(LiveId liveId) {
        LiveAggregate aggregate = liveRepository.findById(liveId)
            .orElseThrow(() -> new IllegalArgumentException("直播不存在: " + liveId.getValue()));
        
        // 结束直播
        aggregate.endLive();
        
        // 保存聚合
        liveRepository.save(aggregate);
        
        log.info("结束直播: liveId={}", liveId.getValue());
    }
    
    /**
     * 刷新推流信息
     */
    public StreamInfo refreshStreamInfo(LiveId liveId) {
        LiveAggregate aggregate = liveRepository.findById(liveId)
            .orElseThrow(() -> new IllegalArgumentException("直播不存在: " + liveId.getValue()));
        
        // 生成新的推流信息
        StreamInfo newStreamInfo = streamGenerationService.refreshStreamInfo(
            liveId, aggregate.getLiveInfo().getTitle());
        
        // 更新聚合
        aggregate.setStreamInfo(newStreamInfo);
        
        // 保存聚合
        liveRepository.save(aggregate);
        
        log.info("刷新推流信息: liveId={}, streamId={}", 
                liveId.getValue(), newStreamInfo.getStreamId());
        
        return newStreamInfo;
    }
    
    /**
     * 检查并处理超时的直播
     */
    public void handleTimeoutLives() {
        List<LiveAggregate> liveAggregates = liveRepository.findByStatus(LiveStatus.live());
        
        for (LiveAggregate aggregate : liveAggregates) {
            if (isLiveTimeout(aggregate)) {
                log.info("检测到超时直播，自动结束: liveId={}", aggregate.getLiveId().getValue());
                aggregate.endLive();
                liveRepository.save(aggregate);
            }
        }
    }
    
    /**
     * 验证直播创建规则
     */
    private void validateLiveCreation(LiveInfo liveInfo) {
        // 检查标题是否重复
        if (isDuplicateTitle(liveInfo.getTitle())) {
            throw new IllegalArgumentException("直播标题已存在: " + liveInfo.getTitle());
        }
        
        // 检查计划开始时间
        if (liveInfo.getPlanStartTime() != null && 
            liveInfo.getPlanStartTime().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("计划开始时间不能早于当前时间");
        }
    }
    
    /**
     * 验证直播开始规则
     */
    private void validateLiveStart(LiveAggregate aggregate) {
        // 检查推流信息是否存在
        if (aggregate.getStreamInfo() == null) {
            throw new IllegalStateException("推流信息不存在，无法开始直播");
        }
        
        // 可以添加其他业务规则，如权限检查等
    }
    
    /**
     * 检查标题是否重复
     */
    private boolean isDuplicateTitle(String title) {
        // 这里应该查询数据库检查标题是否重复
        // 简化实现，实际应该调用仓储方法
        return false;
    }
    
    /**
     * 检查直播是否超时
     */
    private boolean isLiveTimeout(LiveAggregate aggregate) {
        if (aggregate.getLiveInfo().getActualStartTime() == null) {
            return false;
        }
        
        // 检查是否超过最大直播时长（例如12小时）
        LocalDateTime maxEndTime = aggregate.getLiveInfo().getActualStartTime().plusHours(12);
        return LocalDateTime.now().isAfter(maxEndTime);
    }
}
