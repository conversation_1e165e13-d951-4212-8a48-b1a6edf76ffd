package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 直播禁言用户对象 live_banned_user
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_banned_user")
public class LiveBannedUser extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 禁言记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 直播ID
     */
    private Long liveId;

    /**
     * 被禁言用户ID
     */
    private Long userId;

    /**
     * 禁言原因
     */
    private String banReason;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 禁言时间
     */
    private Date banTime;

    /**
     * 解除禁言时间
     */
    private Date unbanTime;

    /**
     * 禁言状态(0-已解除,1-生效中)
     */
    private Long banStatus;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
