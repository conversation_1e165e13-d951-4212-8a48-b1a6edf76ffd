package com.ydwl.live.domain.model.valueobject;

import lombok.Getter;

/**
 * 直播创建领域事件
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
public class LiveCreatedDomainEvent extends DomainEvent {
    
    private final LiveInfo liveInfo;
    
    public LiveCreatedDomainEvent(LiveId liveId, LiveInfo liveInfo) {
        super(liveId);
        this.liveInfo = liveInfo;
    }
    
    @Override
    public String getEventType() {
        return "LiveCreated";
    }
}
