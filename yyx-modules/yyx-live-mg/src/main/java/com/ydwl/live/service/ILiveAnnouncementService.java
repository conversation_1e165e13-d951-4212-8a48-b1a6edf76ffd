package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveAnnouncementVo;
import com.ydwl.live.domain.bo.LiveAnnouncementBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 直播公告Service接口
 * 
 * <p>提供直播公告的完整业务功能，包括：</p>
 * <ul>
 *   <li>基础CRUD操作</li>
 *   <li>公告状态管理（发布、撤销、过期处理）</li>
 *   <li>按直播间查询有效公告</li>
 *   <li>定时公告功能</li>
 *   <li>公告权限控制</li>
 * </ul>
 *
 * <AUTHOR> Yi
 * @version 2.1.4
 * @since 2025-05-21
 */
public interface ILiveAnnouncementService {

    /**
     * 查询直播公告
     *
     * @param id 主键
     * @return 直播公告
     */
    LiveAnnouncementVo queryById(Long id);

    /**
     * 分页查询直播公告列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播公告分页列表
     */
    TableDataInfo<LiveAnnouncementVo> queryPageList(LiveAnnouncementBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的直播公告列表
     *
     * @param bo 查询条件
     * @return 直播公告列表
     */
    List<LiveAnnouncementVo> queryList(LiveAnnouncementBo bo);

    /**
     * 新增直播公告
     *
     * @param bo 直播公告
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveAnnouncementBo bo);

    /**
     * 修改直播公告
     *
     * @param bo 直播公告
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveAnnouncementBo bo);

    /**
     * 校验并批量删除直播公告信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    // ================== 增强的业务方法 ==================

    /**
     * 获取指定直播间的有效公告列表
     * 
     * <p>返回当前时间有效的公告，按优先级和创建时间排序</p>
     *
     * @param liveId 直播ID
     * @return 有效公告列表
     * 
     * @apiNote
     * <ul>
     *   <li>只返回状态为"上线"的公告</li>
     *   <li>只返回在有效时间范围内的公告</li>
     *   <li>按创建时间倒序排列</li>
     * </ul>
     */
    List<LiveAnnouncementVo> getActiveAnnouncementsByLiveId(Long liveId);

    /**
     * 发布公告
     * 
     * <p>将公告状态设置为上线，并设置生效时间</p>
     *
     * @param announcementId 公告ID
     * @param startTime 生效时间，为null表示立即生效
     * @param endTime 结束时间，为null表示永久有效
     * @return 是否发布成功
     * 
     * @throws com.ydwl.live.exception.LiveException 当公告不存在或状态不允许发布时抛出
     */
    Boolean publishAnnouncement(Long announcementId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 撤销公告
     * 
     * <p>将公告状态设置为下线</p>
     *
     * @param announcementId 公告ID
     * @return 是否撤销成功
     * 
     * @throws com.ydwl.live.exception.LiveException 当公告不存在时抛出
     */
    Boolean unpublishAnnouncement(Long announcementId);

    /**
     * 立即发布公告
     * 
     * <p>快捷方法，立即发布公告并设置为永久有效</p>
     *
     * @param announcementId 公告ID
     * @return 是否发布成功
     */
    Boolean publishAnnouncementNow(Long announcementId);

    /**
     * 定时发布公告
     * 
     * <p>设置公告在指定时间自动发布</p>
     *
     * @param announcementId 公告ID
     * @param scheduledTime 定时发布时间
     * @param duration 持续时长（分钟），为null表示永久有效
     * @return 是否设置成功
     */
    Boolean scheduleAnnouncement(Long announcementId, LocalDateTime scheduledTime, Integer duration);

    /**
     * 获取最新的公告内容
     * 
     * <p>获取指定直播间最新发布的公告，用于实时推送</p>
     *
     * @param liveId 直播ID
     * @param limit 返回数量限制，默认为1
     * @return 最新公告列表
     */
    List<LiveAnnouncementVo> getLatestAnnouncements(Long liveId, Integer limit);

    /**
     * 检查公告是否在有效期内
     *
     * @param announcementId 公告ID
     * @return 是否在有效期内
     */
    Boolean isAnnouncementActive(Long announcementId);

    /**
     * 批量更新过期公告状态
     * 
     * <p>定时任务调用，将过期的公告状态设置为下线</p>
     *
     * @return 更新的公告数量
     */
    Integer updateExpiredAnnouncements();

    /**
     * 复制公告到其他直播间
     * 
     * <p>将一个公告复制到多个直播间</p>
     *
     * @param sourceAnnouncementId 源公告ID
     * @param targetLiveIds 目标直播间ID列表
     * @return 复制成功的数量
     */
    Integer copyAnnouncementToLives(Long sourceAnnouncementId, List<Long> targetLiveIds);

    /**
     * 获取公告统计信息
     * 
     * <p>返回指定直播间的公告统计数据</p>
     *
     * @param liveId 直播ID
     * @return 统计信息Map，包含总数、有效数、过期数等
     */
    java.util.Map<String, Object> getAnnouncementStatistics(Long liveId);
}
