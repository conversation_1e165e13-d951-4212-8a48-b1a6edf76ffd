package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveBannedUserVo;
import com.ydwl.live.domain.bo.LiveBannedUserBo;
import com.ydwl.live.service.ILiveBannedUserService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 直播禁言用户
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/bannedUser")
public class LiveBannedUserController extends BaseController {

    private final ILiveBannedUserService liveBannedUserService;

    /**
     * 查询直播禁言用户列表
     */
    @SaCheckPermission("live:bannedUser:list")
    @GetMapping("/list")
    public TableDataInfo<LiveBannedUserVo> list(LiveBannedUserBo bo, PageQuery pageQuery) {
        return liveBannedUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出直播禁言用户列表
     */
    @SaCheckPermission("live:bannedUser:export")
    @Log(title = "直播禁言用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveBannedUserBo bo, HttpServletResponse response) {
        List<LiveBannedUserVo> list = liveBannedUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "直播禁言用户", LiveBannedUserVo.class, response);
    }

    /**
     * 获取直播禁言用户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:bannedUser:query")
    @GetMapping("/{id}")
    public R<LiveBannedUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveBannedUserService.queryById(id));
    }

    /**
     * 新增直播禁言用户
     */
    @SaCheckPermission("live:bannedUser:add")
    @Log(title = "直播禁言用户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveBannedUserBo bo) {
        return toAjax(liveBannedUserService.insertByBo(bo));
    }

    /**
     * 修改直播禁言用户
     */
    @SaCheckPermission("live:bannedUser:edit")
    @Log(title = "直播禁言用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveBannedUserBo bo) {
        return toAjax(liveBannedUserService.updateByBo(bo));
    }

    /**
     * 删除直播禁言用户
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:bannedUser:remove")
    @Log(title = "直播禁言用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveBannedUserService.deleteWithValidByIds(List.of(ids), true));
    }
}
