package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveEventConfigVo;
import com.ydwl.live.domain.bo.LiveEventConfigBo;
import com.ydwl.live.service.ILiveEventConfigService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 事件配置
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/eventConfig")
public class LiveEventConfigController extends BaseController {

    private final ILiveEventConfigService liveEventConfigService;

    /**
     * 查询事件配置列表
     */
    @SaCheckPermission("live:eventConfig:list")
    @GetMapping("/list")
    public TableDataInfo<LiveEventConfigVo> list(LiveEventConfigBo bo, PageQuery pageQuery) {
        return liveEventConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出事件配置列表
     */
    @SaCheckPermission("live:eventConfig:export")
    @Log(title = "事件配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveEventConfigBo bo, HttpServletResponse response) {
        List<LiveEventConfigVo> list = liveEventConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "事件配置", LiveEventConfigVo.class, response);
    }

    /**
     * 获取事件配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:eventConfig:query")
    @GetMapping("/{id}")
    public R<LiveEventConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveEventConfigService.queryById(id));
    }

    /**
     * 新增事件配置
     */
    @SaCheckPermission("live:eventConfig:add")
    @Log(title = "事件配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveEventConfigBo bo) {
        return toAjax(liveEventConfigService.insertByBo(bo));
    }

    /**
     * 修改事件配置
     */
    @SaCheckPermission("live:eventConfig:edit")
    @Log(title = "事件配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveEventConfigBo bo) {
        return toAjax(liveEventConfigService.updateByBo(bo));
    }

    /**
     * 删除事件配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:eventConfig:remove")
    @Log(title = "事件配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveEventConfigService.deleteWithValidByIds(List.of(ids), true));
    }
}
