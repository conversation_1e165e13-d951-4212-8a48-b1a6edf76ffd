package com.ydwl.live.service.impl;

import com.alibaba.fastjson.JSON;
import com.ydwl.LiveTranscoding.domain.dto.FcTranscodeCallbackResponseDto;
import com.ydwl.LiveTranscoding.domain.vo.UniversalTranscodeCallbackVo;
import com.ydwl.live.domain.bo.LiveReplayBo;
import com.ydwl.live.domain.bo.LiveTranscodeOutputBo;
import com.ydwl.live.domain.bo.LiveTranscodeTaskBo;
import com.ydwl.live.domain.vo.LiveReplayVo;
import com.ydwl.live.domain.vo.LiveTranscodeOutputVo;
import com.ydwl.live.domain.vo.LiveTranscodeTaskVo;
import com.ydwl.live.service.ILiveReplayService;
import com.ydwl.live.service.ILiveTranscodeOutputService;
import com.ydwl.live.service.ILiveTranscodeTaskService;
import com.ydwl.live.service.ITranscodeCallbackService;
import com.ydwl.live.service.IVideoProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.util.Date;
import java.util.List;

/**
 * 转码回调服务实现类
 * 处理FC和MTS转码回调业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TranscodeCallbackServiceImpl implements ITranscodeCallbackService {

    private final ILiveTranscodeTaskService liveTranscodeTaskService;
    private final ILiveTranscodeOutputService liveTranscodeOutputService;
    private final ILiveReplayService liveReplayService;
    private final IVideoProcessService videoProcessService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFcTranscodeSuccess(FcTranscodeCallbackResponseDto callbackData) {
        String bizId = callbackData.getBusinessId();
        log.info("处理FC转码成功回调，BizID: {}", bizId);
        log.info("<UNK>FcTranscodeCallbackResponseDto: {}", JSON.toJSONString(callbackData));
        LiveTranscodeTaskVo task = liveTranscodeTaskService.queryByBizId(bizId);
        if (task == null) {
            log.error("FC转码成功回调：未找到对应的转码任务，BizID: {}", bizId);
            return;
        }

        String masterPlaylistUrl = callbackData.getMasterPlaylistUrl();
        log.info("FC转码成功，BizID: {}, MasterPlaylistURL: {}, TaskIdFromCallback: {}",
                 bizId, masterPlaylistUrl, callbackData.getJobId());

        LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
        taskBo.setId(task.getId());
        taskBo.setStatus(2L);
        taskBo.setOutputUrl(masterPlaylistUrl);
        taskBo.setProviderJobId(callbackData.getJobId());
        taskBo.setProgress(100L);
        taskBo.setEndTime(new Date());
        taskBo.setCallbackTime(new Date());
        taskBo.setCallbackStatus(1L);
        boolean taskUpdated = liveTranscodeTaskService.updateByBo(taskBo);
        if (!taskUpdated) {
            log.error("FC转码成功回调：更新转码任务状态失败，TaskID: {}", task.getId());
            throw new RuntimeException("更新转码任务状态失败，TaskID: " + task.getId());
        }

        createFcOutputRecords(task, callbackData.getOutputs());

        if (task.getLiveId() != null) {
            createLiveReplay(task, masterPlaylistUrl, "fc_transcode");
        }
        log.info("FC转码成功回调处理完毕，BizID: {}", bizId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFcTranscodeError(FcTranscodeCallbackResponseDto callbackData) {
        String bizId = callbackData.getBusinessId();
        String errorMessage = callbackData.getErrorMessage();
        String providerJobId = callbackData.getJobId();
        log.error("处理FC转码失败回调，BizID: {}, ProviderJobID: {}, 错误信息: {}", bizId, providerJobId, errorMessage);

        LiveTranscodeTaskVo task = liveTranscodeTaskService.queryByBizId(bizId);
        if (task == null) {
            log.error("FC转码失败回调：未找到对应的转码任务，BizID: {}", bizId);
            return;
        }

        LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
        taskBo.setId(task.getId());
        taskBo.setStatus(3L);
        taskBo.setProviderJobId(providerJobId);
        taskBo.setErrorMsg(errorMessage != null ? errorMessage.substring(0, Math.min(errorMessage.length(), 1000)) : "FC Transcode Failed");
        taskBo.setEndTime(new Date());
        taskBo.setCallbackTime(new Date());
        taskBo.setCallbackStatus(1L);
        long currentRetryCount = task.getRetryCount() != null ? task.getRetryCount() : 0L;
        taskBo.setRetryCount(currentRetryCount + 1);

        boolean taskUpdated = liveTranscodeTaskService.updateByBo(taskBo);
        if (!taskUpdated) {
            log.error("FC转码失败回调：更新转码任务状态失败，TaskID: {}", task.getId());
            throw new RuntimeException("更新转码任务状态失败，TaskID: " + task.getId());
        }
        log.info("FC转码失败回调处理完毕，BizID: {}", bizId);
    }

    private void createFcOutputRecords(LiveTranscodeTaskVo task, List<FcTranscodeCallbackResponseDto.OutputDTO> fcOutputs) {
        if (CollectionUtils.isEmpty(fcOutputs)) {
            log.warn("FC转码成功回调：输出列表为空，不创建详细输出记录。BizID: {}", task.getBizId());
            return;
        }
        int successCount = 0;
        for (FcTranscodeCallbackResponseDto.OutputDTO fcOutput : fcOutputs) {
            // 先检查是否已存在相同的输出记录
            String resolution = fcOutput.getWidth() + "x" + fcOutput.getHeight();

            // 查询是否已存在相同分辨率的输出记录
            LiveTranscodeOutputVo existingOutput = liveTranscodeOutputService.queryByTaskIdAndDefinition(
                task.getId(), resolution, null);

            // 如果已存在相同的输出记录，则跳过
            if (existingOutput != null) {
                log.info("FC转码成功回调：已存在相同的输出记录，跳过创建。TaskID: {}, 分辨率: {}",
                         task.getId(), resolution);
                successCount++; // 视为成功处理
                continue;
            }

            LiveTranscodeOutputBo outputBo = new LiveTranscodeOutputBo();
            outputBo.setTaskId(task.getId());
            outputBo.setBizId(task.getBizId());
            outputBo.setLiveId(task.getLiveId());
            String definitionRes = extractResolutionFromTemplateId(fcOutput.getTemplateId(), fcOutput.getObjectName());
            outputBo.setDefinition(definitionRes);
            outputBo.setResolution(resolution);
            outputBo.setUrl(fcOutput.getObjectName());
            outputBo.setFormat(determineFormatFromObjectName(fcOutput.getObjectName()));
            if (StringUtils.hasText(fcOutput.getDuration())) {
                try {
                    outputBo.setDuration((long) (Double.parseDouble(fcOutput.getDuration()) * 1000));
                } catch (NumberFormatException e) {
                    log.warn("FC转码回调：无法解析时长 {}, BizID: {}", fcOutput.getDuration(), task.getBizId());
                }
            }
            if (StringUtils.hasText(fcOutput.getFilesize())) {
                try {
                    outputBo.setFileSize(Long.parseLong(fcOutput.getFilesize()));
                } catch (NumberFormatException e) {
                    log.warn("FC转码回调：无法解析文件大小 {}, BizID: {}", fcOutput.getFilesize(), task.getBizId());
                }
            }
            if (StringUtils.hasText(fcOutput.getBitrate())) {
                try {
                    outputBo.setBitRate(Long.parseLong(fcOutput.getBitrate()));
                } catch (NumberFormatException e) {
                    log.warn("FC转码回调：无法解析码率 {}, BizID: {}", fcOutput.getBitrate(), task.getBizId());
                }
            }
            outputBo.setProviderTemplateId(fcOutput.getTemplateId());
            outputBo.setStatus("Success".equalsIgnoreCase(fcOutput.getState()) ? 1L : 0L);

            if (liveTranscodeOutputService.insertByBo(outputBo)) {
                successCount++;
            }
        }
        log.info("FC转码成功回调：为TaskID {} 尝试保存 {} 条输出记录，成功 {} 条。", task.getId(), fcOutputs.size(), successCount);
    }

    private void createLiveReplay(LiveTranscodeTaskVo task, String replayUrl, String transcodeType) {
        if (task.getLiveId() == null) {
            log.info("任务 {} (BizID: {}) 未关联LiveId，不创建回放记录。", task.getId(), task.getBizId());
            return;
        }

        // 先查询是否已存在该直播的回放记录
        LiveReplayBo queryBo = new LiveReplayBo();
        queryBo.setLiveId(task.getLiveId());
        List<LiveReplayVo> existingReplays = liveReplayService.queryList(queryBo);

        // 处理OSS路径，转换为完整的HTTPS链接
        String finalReplayUrl = task.getObjectKey();
        String ossPath = finalReplayUrl.substring(6); // "oss://".length() = 6
        // 确保使用m3u8扩展名结尾
        if (ossPath.endsWith(".mp4")) {
            finalReplayUrl = finalReplayUrl.substring(0, finalReplayUrl.length() - 4) + ".m3u8";
        }
        LiveReplayBo replayBo = new LiveReplayBo();
        replayBo.setLiveId(task.getLiveId());
        replayBo.setBizId(task.getBizId());
        replayBo.setTranscodeTaskId(task.getId());
        replayBo.setReplayUrl("https://mps.play.ycyyx.com/"+finalReplayUrl);
        replayBo.setStatus(1L);
        if (task.getDuration() != null) {
            replayBo.setDuration(task.getDuration());
        } else if (task.getOutputs() != null && !task.getOutputs().isEmpty()) {
            // 取第一个输出的时长
            replayBo.setDuration(task.getOutputs().get(0).getDuration() / 1000); // 转换为秒
        }
        replayBo.setAvailableTime(new Date());
        replayBo.setAccessType(0L);
        replayBo.setViewCount(0L);
        Date expiryTime = new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000);
        replayBo.setExpiryTime(expiryTime);
        replayBo.setSourceType(transcodeType);

        // 使用视频URL生成封面图
        try {
            log.info("[截图排查] 开始从视频URL提取对象键进行截图: {}", replayBo.getReplayUrl());

            // 从replayUrl中提取视频在OSS中的对象键
            String objectKey = extractObjectKeyFromUrl(replayBo.getReplayUrl());
            log.info("[截图排查] 提取到的对象键: {}", objectKey);

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(objectKey)) {
                // 生成输出路径前缀
                String outputPrefix = String.valueOf(task.getId());
                log.info("[截图排查] 生成的输出文件名部分 (原 outputPrefix): {}, bucket: video-ydwl", outputPrefix);

                // 提交第一帧截图任务生成封面，并获取预测的URL
                String predictedCoverUrl = videoProcessService.generateFirstFrameSnapshot(
                    objectKey, "video-ydwl", outputPrefix);

                if (predictedCoverUrl != null) {
                    replayBo.setCoverImgUrl(predictedCoverUrl); // 直接使用预测的URL
                } else {
                    log.warn("[截图排查] 获取预测的回放封面图URL失败，对象键: {}, 请检查截图任务是否成功提交以及预测逻辑是否正确", objectKey);
                    // 即使截图预测URL失败，依然继续创建/更新回放记录，封面图字段将为空或保持旧值
                }
            } else {
                log.warn("[截图排查] 无法从回放URL提取对象键，跳过生成封面图：{}", replayBo.getReplayUrl());
            }
        } catch (Exception e) {
            log.error("[截图排查] 生成回放封面图异常", e);
        }

        if (existingReplays.isEmpty()) {
            // 如果不存在回放记录，则创建新的
            liveReplayService.insertByBo(replayBo);
            log.info("为直播 {} 创建回放记录，回放URL: {}", task.getLiveId(), replayBo.getReplayUrl());
        } else {
            // 如果已存在回放记录，则更新它
            replayBo.setId(existingReplays.get(0).getId());
            liveReplayService.updateByBo(replayBo);
            log.info("更新直播 {} 的回放记录，回放URL: {}", task.getLiveId(), replayBo.getReplayUrl());
        }
    }

    /**
     * 从URL中提取对象键
     */
    private String extractObjectKeyFromUrl(String url) {
        log.info("[截图排查] 开始从URL提取对象键: {}", url);

        if (org.apache.commons.lang3.StringUtils.isEmpty(url)) {
            log.warn("[截图排查] URL为空，无法提取对象键");
            return null;
        }

        // 移除协议部分
        String processedUrl = url;
        if (url.startsWith("https://")) {
            processedUrl = url.substring(8);
            log.info("[截图排查] 移除https://协议: {}", processedUrl);
        } else if (url.startsWith("http://")) {
            processedUrl = url.substring(7);
            log.info("[截图排查] 移除http://协议: {}", processedUrl);
        }

        // 移除域名部分
        int domainEndIndex = processedUrl.indexOf('/');
        if (domainEndIndex > 0) {
            processedUrl = processedUrl.substring(domainEndIndex + 1);
            log.info("[截图排查] 移除域名部分: {}", processedUrl);
        }

        // 处理m3u8链接
        if (processedUrl.endsWith(".m3u8")) {
            // 尝试找到mp4源文件
            processedUrl = processedUrl.substring(0, processedUrl.length() - 5) + ".mp4";
            log.info("[截图排查] 将m3u8转换为mp4文件路径: {}", processedUrl);
        }

        log.info("[截图排查] 最终提取的对象键: {}", processedUrl);
        return processedUrl;
    }

    private String extractResolutionFromTemplateId(String templateId, String objectName) {
        if (StringUtils.hasText(templateId)) {
            if (templateId.contains("480p") || templateId.contains("480P")) return "480P";
            if (templateId.contains("720p") || templateId.contains("720P")) return "720P";
            if (templateId.contains("1080p") || templateId.contains("1080P")) return "1080P";
            if (templateId.contains("2k") || templateId.contains("2K")) return "2K";
            if (templateId.contains("4k") || templateId.contains("4K")) return "4K";
        }
        if (StringUtils.hasText(objectName)) {
            String lowerName = objectName.toLowerCase();
            if (lowerName.contains("_480p") || lowerName.contains("/480p/")) return "480P";
            if (lowerName.contains("_720p") || lowerName.contains("/720p/")) return "720P";
            if (lowerName.contains("_1080p") || lowerName.contains("/1080p/")) return "1080P";
            if (lowerName.contains("_2k") || lowerName.contains("/2k/")) return "2K";
            if (lowerName.contains("_4k") || lowerName.contains("/4k/")) return "4K";
        }
        return "UNKNOWN";
    }

    private String determineFormatFromObjectName(String objectName) {
        if (StringUtils.hasText(objectName)) {
            if (objectName.toLowerCase().endsWith(".m3u8")) return "HLS";
            if (objectName.toLowerCase().endsWith(".mp4")) return "MP4";
        }
        return "UNKNOWN";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleMtsTranscodeSuccess(UniversalTranscodeCallbackVo mtsCallback) {
        String bizId = mtsCallback.getUserData();
        String outputUrl = mtsCallback.getOutputUrl();
        String providerJobId = mtsCallback.getJobId();

        log.info("处理MTS转码成功回调，BizID: {}, ProviderJobID: {}, OutputURL: {}", bizId, providerJobId, outputUrl);
        if (bizId == null) {
            log.error("MTS转码成功回调BizID (UserData)为空，无法处理！回调详情: {}", JSON.toJSONString(mtsCallback));
            return;
        }

        LiveTranscodeTaskVo task = liveTranscodeTaskService.queryByBizId(bizId);
        if (task == null) {
             log.error("MTS转码成功回调：未找到对应的转码任务，BizID: {}", bizId);
            return;
        }

        LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
        taskBo.setId(task.getId());
        taskBo.setStatus(2L);
        taskBo.setOutputUrl(outputUrl);
        taskBo.setProviderJobId(providerJobId);
        taskBo.setProgress(100L);
        taskBo.setEndTime(new Date());
        taskBo.setCallbackTime(new Date());
        taskBo.setCallbackStatus(1L);
        boolean taskUpdated = liveTranscodeTaskService.updateByBo(taskBo);
        if (!taskUpdated) {
            log.error("MTS转码成功回调：更新转码任务状态失败，TaskID: {}", task.getId());
            throw new RuntimeException("更新转码任务状态失败，TaskID: " + task.getId());
        }

        createMtsOutputRecords(task, mtsCallback.getOutputs());

        if (task.getLiveId() != null) {
            createLiveReplay(task, outputUrl, "mts_transcode");
        }
        log.info("MTS转码成功回调处理完毕，BizID: {}", bizId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleMtsTranscodeError(UniversalTranscodeCallbackVo mtsCallback) {
        String bizId = mtsCallback.getUserData();
        String errorMessage = mtsCallback.getMessage();
        String providerJobId = mtsCallback.getJobId();
        String errorCode = mtsCallback.getCode();

        log.error("处理MTS转码失败回调，BizID: {}, ProviderJobID: {}, ErrorCode: {}, 错误信息: {}",
                  bizId, providerJobId, errorCode, errorMessage);
        if (bizId == null) {
            log.error("MTS转码失败回调BizID (UserData)为空，无法处理！回调详情: {}", JSON.toJSONString(mtsCallback));
            return;
        }

        LiveTranscodeTaskVo task = liveTranscodeTaskService.queryByBizId(bizId);
        if (task == null) {
            log.error("MTS转码失败回调：未找到对应的转码任务，BizID: {}", bizId);
            return;
        }

        LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
        taskBo.setId(task.getId());
        taskBo.setStatus(3L);
        taskBo.setProviderJobId(providerJobId);
        taskBo.setErrorMsg(("ErrorCode: " + errorCode + ", Message: " + errorMessage).substring(0, Math.min(1000, ("ErrorCode: " + errorCode + ", Message: " + errorMessage).length() )));
        taskBo.setEndTime(new Date());
        taskBo.setCallbackTime(new Date());
        taskBo.setCallbackStatus(1L);
        Long currentRetryCount = task.getRetryCount() != null ? task.getRetryCount() : 0L;
        taskBo.setRetryCount(currentRetryCount + 1);

        boolean taskUpdated = liveTranscodeTaskService.updateByBo(taskBo);
         if (!taskUpdated) {
            log.error("MTS转码失败回调：更新转码任务状态失败，TaskID: {}", task.getId());
            throw new RuntimeException("更新转码任务状态失败，TaskID: " + task.getId());
        }
        log.info("MTS转码失败回调处理完毕，BizID: {}", bizId);
    }

    private void createMtsOutputRecords(LiveTranscodeTaskVo task, List<UniversalTranscodeCallbackVo.OutputInfo> mtsOutputs) {
        if (CollectionUtils.isEmpty(mtsOutputs)) {
            log.warn("MTS转码成功回调：输出列表为空，不创建详细输出记录。BizID: {}", task.getBizId());
            return;
        }
        int successCount = 0;
        for (UniversalTranscodeCallbackVo.OutputInfo mtsOutput : mtsOutputs) {
            LiveTranscodeOutputBo outputBo = new LiveTranscodeOutputBo();
            outputBo.setTaskId(task.getId());
            outputBo.setBizId(task.getBizId());
            outputBo.setLiveId(task.getLiveId());
            String resolution = extractResolutionFromTemplateId(mtsOutput.getTemplateId(), mtsOutput.getObjectName());
            outputBo.setDefinition(resolution);
            outputBo.setResolution(mtsOutput.getWidth() + "x" + mtsOutput.getHeight());
            outputBo.setUrl(mtsOutput.getObjectName());
            outputBo.setFormat(determineFormatFromObjectName(mtsOutput.getObjectName()));
            if (StringUtils.hasText(mtsOutput.getDuration())) {
                try {
                    outputBo.setDuration((long) (Double.parseDouble(mtsOutput.getDuration()) * 1000));
                } catch (NumberFormatException e) {
                    log.warn("MTS转码回调：无法解析时长 {}, BizID: {}", mtsOutput.getDuration(), task.getBizId());
                }
            }
            if (StringUtils.hasText(mtsOutput.getFilesize())) {
                try {
                    outputBo.setFileSize(Long.parseLong(mtsOutput.getFilesize()));
                } catch (NumberFormatException e) {
                    log.warn("MTS转码回调：无法解析文件大小 {}, BizID: {}", mtsOutput.getFilesize(), task.getBizId());
                }
            }
            if (StringUtils.hasText(mtsOutput.getBitrate())) {
                try {
                    outputBo.setBitRate(Long.parseLong(mtsOutput.getBitrate()));
                } catch (NumberFormatException e) {
                    log.warn("MTS转码回调：无法解析码率 {}, BizID: {}", mtsOutput.getBitrate(), task.getBizId());
                }
            }
            outputBo.setProviderTemplateId(mtsOutput.getTemplateId());
            outputBo.setStatus("Success".equalsIgnoreCase(mtsOutput.getState()) ? 1L : 0L);
            if(liveTranscodeOutputService.insertByBo(outputBo)){
                successCount++;
            }
        }
        log.info("MTS转码成功回调：为TaskID {} 尝试保存 {} 条输出记录，成功 {} 条。", task.getId(), mtsOutputs.size(), successCount);
    }
}
