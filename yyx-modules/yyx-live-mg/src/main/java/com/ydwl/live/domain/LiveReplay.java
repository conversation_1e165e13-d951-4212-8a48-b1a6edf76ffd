package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 直播回放对象 live_replay
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_replay")
public class LiveReplay extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 回放ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联直播ID
     */
    private Long liveId;

    /**
     * 关联转码任务ID
     */
    private Long transcodeTaskId;

    /**
     * 回放地址
     */
    private String replayUrl;

    /**
     * 封面图片URL
     */
    private String coverImgUrl;

    /**
     * 状态(0-生成中,1-可用,2-已过期)
     */
    private Long status;

    /**
     * 视频时长(秒)
     */
    private Long duration;

    /**
     * 回放可用时间
     */
    private Date availableTime;

    /**
     * 回放过期时间
     */
    private Date expiryTime;

    /**
     * 访问类型(0-公开,1-需登录,2-需报名,3-会员专享)
     */
    private Long accessType;

    /**
     * 观看次数
     */
    private Long viewCount;


}
