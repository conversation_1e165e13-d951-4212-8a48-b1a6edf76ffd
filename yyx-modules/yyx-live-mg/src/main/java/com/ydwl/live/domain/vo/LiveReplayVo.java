package com.ydwl.live.domain.vo;

import java.util.Date;
import com.ydwl.common.translation.annotation.Translation;
import com.ydwl.common.translation.constant.TransConstant;
import com.ydwl.live.domain.LiveReplay;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;



/**
 * 直播回放视图对象 live_replay
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveReplay.class)
public class LiveReplayVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 回放ID
     */
    @ExcelProperty(value = "回放ID")
    private Long id;

    /**
     * 关联直播ID
     */
    @ExcelProperty(value = "关联直播ID")
    private Long liveId;

    /**
     * 关联转码任务ID
     */
    @ExcelProperty(value = "关联转码任务ID")
    private Long transcodeTaskId;

    /**
     * 回放地址
     */
    @ExcelProperty(value = "回放地址")
    @Translation(type = TransConstant.M3U8VIDEO_URL_TO_PUBLIC_URL)
    private String replayUrl;

    /**
     * 封面图片URL
     */
    @ExcelProperty(value = "封面图片URL")
    @Translation(type = TransConstant.PRIVATE_OSS_URL_TO_TEMP_URL)
    private String coverImgUrl;

    /**
     * 状态(0-生成中,1-可用,2-已过期)
     */
    @ExcelProperty(value = "状态(0-生成中,1-可用,2-已过期)")
    private Long status;

    /**
     * 视频时长(秒)
     */
    @ExcelProperty(value = "视频时长(秒)")
    private Long duration;

    /**
     * 回放可用时间
     */
    @ExcelProperty(value = "回放可用时间")
    private Date availableTime;

    /**
     * 回放过期时间
     */
    @ExcelProperty(value = "回放过期时间")
    private Date expiryTime;

    /**
     * 访问类型(0-公开,1-需登录,2-需报名,3-会员专享)
     */
    @ExcelProperty(value = "访问类型(0-公开,1-需登录,2-需报名,3-会员专享)")
    private Long accessType;

    /**
     * 观看次数
     */
    @ExcelProperty(value = "观看次数")
    private Long viewCount;


}
