package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 直播预告对象 live_schedule
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_schedule")
public class LiveSchedule extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 预告ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 预告标题
     */
    private String title;

    /**
     * 主播ID
     */
    private Long hostUserId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 预告描述
     */
    private String description;

    /**
     * 预告封面URL
     */
    private String coverImgUrl;

    /**
     * 预定时间
     */
    private Date scheduledTime;

    /**
     * 预计时长(单位:分钟)
     */
    private Long estimatedDurationMinutes;

    /**
     * 状态(0-已取消,1-待开播)
     */
    private Long scheduleStatus;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
