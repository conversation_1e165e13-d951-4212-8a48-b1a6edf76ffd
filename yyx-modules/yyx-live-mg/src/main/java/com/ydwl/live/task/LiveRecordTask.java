//package com.ydwl.live.task;
//
//import com.ydwl.live.domain.Live;
//import com.ydwl.live.domain.bo.LiveBo;
//import com.ydwl.live.service.impl.LiveServiceImpl;
//import com.ydwl.live.util.AliyunLiveRecordUtil;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.time.ZoneOffset;
//import java.time.format.DateTimeFormatter;
//import java.util.List;
//
////定时任务 查询录制文件地址更新到live表
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class LiveRecordTask {
//
//    private final LiveServiceImpl liveService;
//    private final AliyunLiveRecordUtil aliyunLiveRecordUtil;
//
//    private static final String APP_NAME = "ydwl-live";
//    private static final String DOMAIN_NAME = "play.live.ycyyx.com";
//
//    // 添加定时任务注解，每5分钟执行一次
//    @Scheduled(fixedRate = 300000)
//    public void updateLiveRecord() {
//        try {
//            // 获取已结束的直播列表
//            List<Live> endedLives = liveService.getLiveStatusList();
//            log.info("Found {} ended lives to check for recordings", endedLives.size());
//
//            for (Live live : endedLives) {
//                try {
//                    // 构造查询参数
//                    String streamName =  live.getId().toString();
//
//                    // 计算查询时间范围（查询最近24小时的录制内容）
//                    LocalDateTime endTime = LocalDateTime.now();
//                    LocalDateTime startTime = endTime.minusHours(24);
//
//                    // 格式化时间为阿里云要求的格式
//                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
//                    String endTimeStr = endTime.atOffset(ZoneOffset.UTC).format(formatter);
//                    String startTimeStr = startTime.atOffset(ZoneOffset.UTC).format(formatter);
//
//                    // 查询录制内容
//                    var recordList = aliyunLiveRecordUtil.getLiveRecordContent(
//                        DOMAIN_NAME,
//                        APP_NAME,
//                        streamName,
//                        startTimeStr,
//                        endTimeStr
//                    );
//
//                    // 如果有录制内容，更新到数据库
//                    if (recordList != null && !recordList.getRecordContentInfo().isEmpty()) {
//                        // 获取最新的录制文件地址
//                        String ossUrl = recordList.getRecordContentInfo().get(0).getOssObjectPrefix();
//
//                        // 更新直播记录
//                        LiveBo liveBo = new LiveBo();
//                        liveBo.setId(live.getId());
//                        liveBo.setOssAddress(ossUrl);
//                        liveService.updateByBo(liveBo);
//
//                        log.info("Updated recording URL for live id: {}, URL: {}", live.getId(), ossUrl);
//                    } else {
//                        log.info("No recording found for live id: {}", live.getId());
//                    }
//                } catch (Exception e) {
//                    log.error("Error processing recording for live id: " + live.getId(), e);
//                }
//            }
//        } catch (Exception e) {
//            log.error("Error in updateLiveRecord task", e);
//        }
//    }
//}
