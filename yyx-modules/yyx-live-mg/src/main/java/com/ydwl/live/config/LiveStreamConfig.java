package com.ydwl.live.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 直播推流配置
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ydwl.live.stream")
public class LiveStreamConfig {

    /**
     * 推流域名
     */
    private String pushDomain = "push.live.ycyyx.com";

    /**
     * 播放域名
     */
    private String playDomain = "play.live.ycyyx.com";

    /**
     * 推流应用名称
     */
    private String appName = "live";

    /**
     * 推流鉴权密钥
     */
    private String pushSecretKey = "2p5n2R8aA6tTQtu2";

    /**
     * 播放鉴权密钥
     */
    private String playSecretKey = "LznJujBX81mOJM7E";

    /**
     * 播放URL有效期（秒）
     */
    private long playUrlExpireSeconds = 86400;
} 