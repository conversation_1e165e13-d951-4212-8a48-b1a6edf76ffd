package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveTransactionVo;
import com.ydwl.live.domain.bo.LiveTransactionBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 交易记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveTransactionService {

    /**
     * 查询交易记录
     *
     * @param id 主键
     * @return 交易记录
     */
    LiveTransactionVo queryById(Long id);

    /**
     * 分页查询交易记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 交易记录分页列表
     */
    TableDataInfo<LiveTransactionVo> queryPageList(LiveTransactionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的交易记录列表
     *
     * @param bo 查询条件
     * @return 交易记录列表
     */
    List<LiveTransactionVo> queryList(LiveTransactionBo bo);

    /**
     * 新增交易记录
     *
     * @param bo 交易记录
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveTransactionBo bo);

    /**
     * 修改交易记录
     *
     * @param bo 交易记录
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveTransactionBo bo);

    /**
     * 校验并批量删除交易记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
