package com.ydwl.live.callback.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 访问类型枚举
 */
@Getter
@AllArgsConstructor
public enum AccessTypeEnum {

    PUBLIC(0L, "公开"),
    LOGIN_REQUIRED(1L, "需登录"),
    SIGNUP_REQUIRED(2L, "需报名"),
    MEMBER_ONLY(3L, "会员专享");

    private final Long code;
    private final String desc;

    public static AccessTypeEnum getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (AccessTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
