package com.ydwl.live.controller;

import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.live.domain.bo.LiveBo;
import com.ydwl.live.domain.vo.GetStreamVo;
import com.ydwl.live.service.ILiveStreamService;
import com.ydwl.live.service.impl.LiveDataService;
import com.ydwl.live.service.impl.LiveStateManager;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 直播数据控制器
 * 提供直播推流地址生成、刷新和状态管理相关API
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/data")
public class LiveDataController extends BaseController {

    private final LiveDataService liveDataService;
    private final ILiveStreamService liveStreamService;

    /**
     * 创建直播并自动生成推流地址
     */
    @Log(title = "创建直播", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Map<String, Object>> createLive(@Validated(AddGroup.class) @RequestBody LiveBo bo) {
        return liveDataService.createLiveWithStream(bo);
    }

    /**
     * 获取直播详情（包含推流和播放地址）
     *
     * @param liveId 直播ID
     */
    @GetMapping("/detail/{liveId}")
    public R<Map<String, Object>> getLiveDetail(@PathVariable Long liveId) {
        Map<String, Object> detail = liveDataService.getLiveDetail(liveId);
        return detail != null ? R.ok(detail) : R.fail("获取直播详情失败");
    }

    /**
     * 刷新推流地址
     *
     * @param liveId 直播ID
     */
    @Log(title = "刷新推流地址", businessType = BusinessType.UPDATE)
    @PutMapping("/refresh/{liveId}")
    public R<Map<String, Object>> refreshStream(@PathVariable Long liveId) {
        return liveDataService.refreshLiveStream(liveId);
    }

    /**
     * 获取推流信息
     *
     * @param liveId 直播ID
     */
    @GetMapping("/stream/{liveId}")
    public R<GetStreamVo> getStreamInfo(@PathVariable Long liveId) {
        GetStreamVo streamVo = liveStreamService.queryByLiveId(liveId);
        return streamVo != null ? R.ok(streamVo) : R.fail("获取推流信息失败");
    }

    /**
     * 更新直播状态为开始直播
     *
     * @param liveId 直播ID
     */
    @Log(title = "开始直播", businessType = BusinessType.UPDATE)
    @PutMapping("/start/{liveId}")
    public R<Void> startLive(@PathVariable Long liveId) {
        return liveDataService.updateLiveStatus(liveId, LiveStateManager.LiveStatus.LIVE);
    }

    /**
     * 更新直播状态为结束直播
     *
     * @param liveId 直播ID
     */
    @Log(title = "结束直播", businessType = BusinessType.UPDATE)
    @PutMapping("/end/{liveId}")
    public R<Void> endLive(@PathVariable Long liveId) {
        return liveDataService.updateLiveStatus(liveId, LiveStateManager.LiveStatus.ENDED);
    }

    /**
     * 刷新直播状态
     *
     * @param liveId 直播ID
     * @param status 目标状态(0-未开始,1-直播中,2-已结束,3-异常)
     */
    @Log(title = "更新直播状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{liveId}/{status}")
    public R<Void> updateLiveStatus(@PathVariable Long liveId, @PathVariable Integer status) {
        LiveStateManager.LiveStatus targetStatus;
        switch (status) {
            case 0:
                targetStatus = LiveStateManager.LiveStatus.NOT_STARTED;
                break;
            case 1:
                targetStatus = LiveStateManager.LiveStatus.LIVE;
                break;
            case 2:
                targetStatus = LiveStateManager.LiveStatus.ENDED;
                break;
            default:
                targetStatus = LiveStateManager.LiveStatus.ERROR;
        }
        return liveDataService.updateLiveStatus(liveId, targetStatus);
    }
} 