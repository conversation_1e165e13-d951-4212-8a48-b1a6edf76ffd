package com.ydwl.live.callback.vo;

import cn.hutool.json.JSONObject;
import lombok.Builder;
import lombok.Data;

/**
 * 按需回调返回给阿里云的回调参数
 */
@Data
public class OnDemandRecordCallbackVo {


    /**
     * API版本。默认为1.0版本。
     */
    private String ApiVersion;


    /**
     * 是否需要录制。取值：true：需要录制；false：不需要录制。
     */
    private boolean NeedRecord;


    /**
     * 具体格式的录制周期变化。取值范围：5~21600。单位：秒。
     */
    private JSONObject Interval;


    /**
     * 录制格式。支持MP4、FLV、M3U8。
     */
    private JSONObject Format;

/*  例子
 * {
 *     "ApiVersion" : "1.0",
 *     "NeedRecord" : true,
 *     "Interval" : {
 *         "Mp4": 300,
 *         "Flv": 120,
 *         "M3U8": 180
 *     },
 *     "Format" : ["mp4","flv"]
 * }
 */



}
