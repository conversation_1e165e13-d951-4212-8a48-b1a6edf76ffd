package com.ydwl.live.controller.refactored;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.live.controller.refactored.request.LiveCreateRequest;
import com.ydwl.live.controller.refactored.request.LiveUpdateRequest;
import com.ydwl.live.controller.refactored.response.LiveDetailResponse;
import com.ydwl.live.controller.refactored.response.LiveListResponse;
import com.ydwl.live.domain.model.valueobject.StreamInfo;
import com.ydwl.live.manager.LiveManager;
import com.ydwl.live.manager.dto.LiveCreateCommand;
import com.ydwl.live.manager.dto.LiveDetailDto;
import com.ydwl.live.manager.dto.LiveListDto;
import com.ydwl.live.manager.dto.LiveUpdateCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 重构后的直播管理控制器
 * 
 * 统一的直播管理API，遵循RESTful设计原则
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v2/live")
public class LiveManagementController extends BaseController {
    
    private final LiveManager liveManager;
    
    /**
     * 创建直播
     */
    @SaCheckPermission("live:live:add")
    @Log(title = "创建直播", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<LiveDetailResponse> createLive(@Validated(AddGroup.class) @RequestBody LiveCreateRequest request) {
        try {
            LiveCreateCommand command = LiveCreateCommand.builder()
                .categoryId(request.getCategoryId())
                .title(request.getTitle())
                .coverImgUrl(request.getCoverImgUrl())
                .planStartTime(request.getPlanStartTime())
                .description(request.getDescription())
                .tagList(request.getTagList())
                .signupRequired(request.isSignupRequired())
                .replayEnabled(request.isReplayEnabled())
                .autoRecord(request.isAutoRecord())
                .chatEnabled(request.isChatEnabled())
                .chatDelay(request.getChatDelay())
                .giftEnabled(request.isGiftEnabled())
                .defaultQuality(request.getDefaultQuality())
                .accessLevel(request.getAccessLevel())
                .build();
            
            LiveDetailDto dto = liveManager.createLive(command);
            LiveDetailResponse response = convertToDetailResponse(dto);
            
            return R.ok(response);
        } catch (Exception e) {
            log.error("创建直播失败", e);
            return R.fail("创建直播失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新直播信息
     */
    @SaCheckPermission("live:live:edit")
    @Log(title = "更新直播", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/{id}")
    public R<LiveDetailResponse> updateLive(
            @PathVariable @NotNull(message = "直播ID不能为空") Long id,
            @Validated(EditGroup.class) @RequestBody LiveUpdateRequest request) {
        try {
            LiveUpdateCommand command = LiveUpdateCommand.builder()
                .liveId(id)
                .title(request.getTitle())
                .coverImgUrl(request.getCoverImgUrl())
                .planStartTime(request.getPlanStartTime())
                .description(request.getDescription())
                .tagList(request.getTagList())
                .build();
            
            LiveDetailDto dto = liveManager.updateLive(command);
            LiveDetailResponse response = convertToDetailResponse(dto);
            
            return R.ok(response);
        } catch (Exception e) {
            log.error("更新直播失败: liveId={}", id, e);
            return R.fail("更新直播失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取直播详情
     */
    @SaCheckPermission("live:live:query")
    @GetMapping("/{id}")
    public R<LiveDetailResponse> getLiveDetail(
            @PathVariable @NotNull(message = "直播ID不能为空") Long id) {
        try {
            Optional<LiveDetailDto> dtoOpt = liveManager.getLiveDetail(id);
            if (dtoOpt.isPresent()) {
                LiveDetailResponse response = convertToDetailResponse(dtoOpt.get());
                return R.ok(response);
            } else {
                return R.fail("直播不存在");
            }
        } catch (Exception e) {
            log.error("获取直播详情失败: liveId={}", id, e);
            return R.fail("获取直播详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据分类获取直播列表
     */
    @SaCheckPermission("live:live:list")
    @GetMapping("/category/{categoryId}")
    public R<List<LiveListResponse>> getLivesByCategory(
            @PathVariable @NotNull(message = "分类ID不能为空") Long categoryId) {
        try {
            List<LiveListDto> dtos = liveManager.getLivesByCategory(categoryId);
            List<LiveListResponse> responses = dtos.stream()
                .map(this::convertToListResponse)
                .collect(Collectors.toList());
            
            return R.ok(responses);
        } catch (Exception e) {
            log.error("获取分类直播列表失败: categoryId={}", categoryId, e);
            return R.fail("获取直播列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据状态获取直播列表
     */
    @SaCheckPermission("live:live:list")
    @GetMapping("/status/{status}")
    public R<List<LiveListResponse>> getLivesByStatus(
            @PathVariable @NotNull(message = "状态不能为空") Long status) {
        try {
            List<LiveListDto> dtos = liveManager.getLivesByStatus(status);
            List<LiveListResponse> responses = dtos.stream()
                .map(this::convertToListResponse)
                .collect(Collectors.toList());
            
            return R.ok(responses);
        } catch (Exception e) {
            log.error("获取状态直播列表失败: status={}", status, e);
            return R.fail("获取直播列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 开始直播
     */
    @SaCheckPermission("live:live:edit")
    @Log(title = "开始直播", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/start")
    public R<Void> startLive(@PathVariable @NotNull(message = "直播ID不能为空") Long id) {
        try {
            liveManager.startLive(id);
            return R.ok();
        } catch (Exception e) {
            log.error("开始直播失败: liveId={}", id, e);
            return R.fail("开始直播失败: " + e.getMessage());
        }
    }
    
    /**
     * 结束直播
     */
    @SaCheckPermission("live:live:edit")
    @Log(title = "结束直播", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/end")
    public R<Void> endLive(@PathVariable @NotNull(message = "直播ID不能为空") Long id) {
        try {
            liveManager.endLive(id);
            return R.ok();
        } catch (Exception e) {
            log.error("结束直播失败: liveId={}", id, e);
            return R.fail("结束直播失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新推流信息
     */
    @SaCheckPermission("live:live:edit")
    @Log(title = "刷新推流", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/stream/refresh")
    public R<StreamInfo> refreshStreamInfo(@PathVariable @NotNull(message = "直播ID不能为空") Long id) {
        try {
            StreamInfo streamInfo = liveManager.refreshStreamInfo(id);
            return R.ok(streamInfo);
        } catch (Exception e) {
            log.error("刷新推流信息失败: liveId={}", id, e);
            return R.fail("刷新推流信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除直播
     */
    @SaCheckPermission("live:live:remove")
    @Log(title = "删除直播", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> deleteLive(@PathVariable @NotNull(message = "直播ID不能为空") Long id) {
        try {
            liveManager.deleteLive(id);
            return R.ok();
        } catch (Exception e) {
            log.error("删除直播失败: liveId={}", id, e);
            return R.fail("删除直播失败: " + e.getMessage());
        }
    }
    
    /**
     * 转换为详情响应
     */
    private LiveDetailResponse convertToDetailResponse(LiveDetailDto dto) {
        return LiveDetailResponse.builder()
            .liveId(dto.getLiveId())
            .categoryId(dto.getCategoryId())
            .title(dto.getTitle())
            .coverImgUrl(dto.getCoverImgUrl())
            .planStartTime(dto.getPlanStartTime())
            .actualStartTime(dto.getActualStartTime())
            .actualEndTime(dto.getActualEndTime())
            .durationMinutes(dto.getDurationMinutes())
            .status(dto.getStatus())
            .statusText(dto.getStatusText())
            .description(dto.getDescription())
            .tagList(dto.getTagList())
            .viewCount(dto.getViewCount())
            .likeCount(dto.getLikeCount())
            .shareCount(dto.getShareCount())
            .maxOnlineCount(dto.getMaxOnlineCount())
            .currentOnlineCount(dto.getCurrentOnlineCount())
            .streamInfo(dto.getStreamInfo())
            .settings(dto.getSettings())
            .build();
    }
    
    /**
     * 转换为列表响应
     */
    private LiveListResponse convertToListResponse(LiveListDto dto) {
        return LiveListResponse.builder()
            .liveId(dto.getLiveId())
            .title(dto.getTitle())
            .coverImgUrl(dto.getCoverImgUrl())
            .planStartTime(dto.getPlanStartTime())
            .status(dto.getStatus())
            .statusText(dto.getStatusText())
            .viewCount(dto.getViewCount())
            .likeCount(dto.getLikeCount())
            .currentOnlineCount(dto.getCurrentOnlineCount())
            .build();
    }
}
