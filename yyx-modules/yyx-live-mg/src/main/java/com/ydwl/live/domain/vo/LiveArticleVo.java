package com.ydwl.live.domain.vo;

import java.util.Date;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.live.domain.LiveArticle;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 直播文章视图对象 live_article
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveArticle.class)
public class LiveArticleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文章ID
     */
    @ExcelProperty(value = "文章ID")
    private Long id;

    /**
     * 关联业务ID
     */
    @ExcelProperty(value = "关联业务ID")
    private Long relatedId;

    /**
     * 文章标题
     */
    @ExcelProperty(value = "文章标题")
    private String title;

    /**
     * 文章内容
     */
    @ExcelProperty(value = "文章内容")
    private String content;

    /**
     * 发布状态(0-草稿,1-已发布,2-已归档)
     */
    @ExcelProperty(value = "发布状态(0-草稿,1-已发布,2-已归档)")
    private Long publishStatus;

    /**
     * 关联类型(live-直播,course-课程,product-产品)
     */
    @ExcelProperty(value = "关联类型(live-直播,course-课程,product-产品)")
    private String relatedType;

    /**
     * 内容格式(html-网页,markdown-标记,plain-纯文本)
     */
    @ExcelProperty(value = "内容格式(html-网页,markdown-标记,plain-纯文本)")
    private String contentFormat;

    /**
     * 封面图片URL
     */
    @ExcelProperty(value = "封面图片URL")
    private String coverImgUrl;

    /**
     * 文章摘要
     */
    @ExcelProperty(value = "文章摘要")
    private String summary;

    /**
     * 最后编辑时间
     */
    @ExcelProperty(value = "最后编辑时间")
    private Date lastEditTime;

    /**
     * 是否精选(0-否,1-是)
     */
    @ExcelProperty(value = "是否精选(0-否,1-是)")
    private Long isFeatured;

    /**
     * 是否允许评论(0-否,1-是)
     */
    @ExcelProperty(value = "是否允许评论(0-否,1-是)")
    private Long isCommentAllowed;

    /**
     * 浏览次数
     */
    @ExcelProperty(value = "浏览次数")
    private Long viewCount;

    /**
     * 点赞次数
     */
    @ExcelProperty(value = "点赞次数")
    private Long likeCount;

    /**
     * 分享次数
     */
    @ExcelProperty(value = "分享次数")
    private Long shareCount;


}
