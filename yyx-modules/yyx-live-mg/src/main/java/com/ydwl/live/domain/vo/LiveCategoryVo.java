package com.ydwl.live.domain.vo;

import com.ydwl.common.translation.annotation.Translation;
import com.ydwl.common.translation.constant.TransConstant;
import com.ydwl.live.domain.LiveCategory;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 直播分类视图对象 live_category
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveCategory.class)
public class LiveCategoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    @ExcelProperty(value = "分类ID")
    private Long id;

    /**
     * 分类名称
     */
    @ExcelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 分类编码(唯一标识)
     */
    @ExcelProperty(value = "分类编码(唯一标识)")
    private String categoryCode;

    /**
     * 父分类ID(0表示顶级分类)
     */
    @ExcelProperty(value = "父分类ID(0表示顶级分类)")
    private Long parentId;

    /**
     * 分类路径(格式:1,2,3)
     */
    @ExcelProperty(value = "分类路径(格式:1,2,3)")
    private String path;

    /**
     * 树排序序号
     */
    @ExcelProperty(value = "树排序序号")
    private Long treeSort;

    /**
     * 树层级(从1开始)
     */
    @ExcelProperty(value = "树层级(从1开始)")
    private Long treeLevel;

    /**
     * 分类图标URL
     */
    @Translation(type = TransConstant.PRIVATE_OSS_URL_TO_TEMP_URL, mapper = "iconUrl")
    @ExcelProperty(value = "分类图标URL")
    private String iconUrl;

    /**
     * 分类封面图URL
     */
    @Translation(type = TransConstant.PRIVATE_OSS_URL_TO_TEMP_URL, mapper = "coverImgUrl")
    @ExcelProperty(value = "分类封面图URL")
    private String coverImgUrl;

    /**
     * 分类描述
     */
    @ExcelProperty(value = "分类描述")
    private String description;

    /**
     * 状态(0-禁用,1-启用)
     */
    @ExcelProperty(value = "状态(0-禁用,1-启用)")
    private Long status;

    /**
     * 该分类下直播数量
     */
    @ExcelProperty(value = "该分类下直播数量")
    private Long liveCount;

    /**
     * 分类浏览次数
     */
    @ExcelProperty(value = "分类浏览次数")
    private Long viewCount;


}
