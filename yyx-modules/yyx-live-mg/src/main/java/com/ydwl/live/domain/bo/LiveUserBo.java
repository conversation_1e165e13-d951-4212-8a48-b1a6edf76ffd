package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveUser;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户业务对象 live_user
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveUser.class, reverseConvertGenerate = false)
public class LiveUserBo extends BaseEntity {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 微信唯一标识
     */
    private String openid;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户状态(0-禁用,1-正常)
     */
    @NotNull(message = "用户状态(0-禁用,1-正常)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userStatus;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 性别(0-未知,1-男,2-女)
     */
    private Long gender;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 通知偏好设置
     */
    private String notificationSettings;

    /**
     * 用户行为统计
     */
    private String userStatistics;


}
