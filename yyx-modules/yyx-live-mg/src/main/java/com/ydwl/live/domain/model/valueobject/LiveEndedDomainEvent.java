package com.ydwl.live.domain.model.valueobject;

import lombok.Getter;

/**
 * 直播结束领域事件
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
public class LiveEndedDomainEvent extends DomainEvent {
    
    private final Long durationMinutes;
    
    public LiveEndedDomainEvent(LiveId liveId, Long durationMinutes) {
        super(liveId);
        this.durationMinutes = durationMinutes;
    }
    
    @Override
    public String getEventType() {
        return "LiveEnded";
    }
}
