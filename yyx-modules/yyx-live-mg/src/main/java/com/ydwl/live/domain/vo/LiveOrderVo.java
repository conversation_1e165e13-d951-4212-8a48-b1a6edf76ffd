package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.common.excel.annotation.ExcelDictFormat;
import com.ydwl.common.excel.convert.ExcelDictConvert;
import com.ydwl.live.domain.LiveOrder;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 订单视图对象 live_order
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveOrder.class)
public class LiveOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long id;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 订单类型（1-充值，2-购买会员）
     */
    @ExcelProperty(value = "订单类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-充值，2-购买会员")
    private Long orderType;

    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    private Long amount;

    /**
     * 虚拟币数量
     */
    @ExcelProperty(value = "虚拟币数量")
    private Long coinAmount;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式")
    private String paymentMethod;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "支付时间")
    private Date paymentTime;

    /**
     * 第三方交易号
     */
    @ExcelProperty(value = "第三方交易号")
    private String transactionId;

    /**
     * 订单状态（0-待支付，1-已支付，2-已取消，3-已退款）
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-待支付，1-已支付，2-已取消，3-已退款")
    private Long status;


}
