package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 视频上传记录对象 live_video_upload
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_video_upload")
public class LiveVideoUpload extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 上传ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 直播ID
     */
    private Long liveId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小(单位:字节)
     */
    private Long fileSizeBytes;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * OSS存储地址
     */
    private String ossUrl;

    /**
     * 文件MD5
     */
    private String fileMd5;

    /**
     * 视频时长(单位:秒)
     */
    private Double videoDurationSeconds;

    /**
     * 视频分辨率
     */
    private String videoResolution;

    /**
     * 视频码率(单位:Kbps)
     */
    private Integer videoBitrateKbps;

    /**
     * 帧率
     */
    private Double frameRate;

    /**
     * 视频编码格式
     */
    private String videoCodec;

    /**
     * 音频编码格式
     */
    private String audioCodec;

    /**
     * 宽高比
     */
    private String aspectRatio;

    /**
     * 创建日期
     */
    private String createdDate;

    /**
     * 最后修改日期
     */
    private String lastModifiedDate;

    /**
     * 状态(0-上传中,1-已完成,2-失败)
     */
    private Long uploadStatus;

    /**
     * 上传进度(单位:%)
     */
    private Long uploadProgressPercent;

    /**
     * 上传完成时间
     */
    private Date uploadCompleteTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
