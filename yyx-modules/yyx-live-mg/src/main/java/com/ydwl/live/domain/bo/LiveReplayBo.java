package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveReplay;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 直播回放业务对象 live_replay
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveReplay.class, reverseConvertGenerate = false)
public class LiveReplayBo extends BaseEntity {

    /**
     * 回放ID
     */
    @NotNull(message = "回放ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 关联直播ID
     */
    @NotNull(message = "关联直播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long liveId;

    /**
     * 业务ID (与 LiveTranscodeTask中的bizId一致，冗余用于查询)
     */
    private String bizId;

    /**
     * 关联转码任务ID
     */
    @NotNull(message = "关联转码任务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long transcodeTaskId;

    /**
     * 回放地址
     */
    @NotBlank(message = "回放地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String replayUrl;

    /**
     * 封面图片URL
     */
    private String coverImgUrl;

    /**
     * 状态(0-生成中,1-可用,2-已过期)
     */
    @NotNull(message = "状态(0-生成中,1-可用,2-已过期)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * 视频时长(毫秒)
     */
    private Long duration;

    /**
     * 回放可用时间
     */
    private Date availableTime;

    /**
     * 回放过期时间
     */
    private Date expiryTime;

    /**
     * 访问类型(0-公开,1-需登录,2-需报名,3-会员专享)
     */
    @NotNull(message = "访问类型(0-公开,1-需登录,2-需报名,3-会员专享)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long accessType;

    /**
     * 观看次数
     */
    private Long viewCount;

    /**
     * 回放来源类型 (例如: fc_transcode, mts_transcode, recording)
     */
    private String sourceType;

}
