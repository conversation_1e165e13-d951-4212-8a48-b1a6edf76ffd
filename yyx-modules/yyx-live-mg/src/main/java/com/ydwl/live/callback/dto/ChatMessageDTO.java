package com.ydwl.live.callback.dto;

import lombok.Data;
import java.util.Date;

/**
 * 聊天消息DTO
 */
@Data
public class ChatMessageDTO {
    /**
     * 直播ID
     */
    private String liveId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型（1-普通消息，2-系统消息，3-礼物消息）
     */
    private Integer type;

    /**
     * 发送时间
     */
    private Date sendTime;
}
