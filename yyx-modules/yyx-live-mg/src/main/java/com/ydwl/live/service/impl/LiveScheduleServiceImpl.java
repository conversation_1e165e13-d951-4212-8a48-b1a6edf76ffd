package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveScheduleBo;
import com.ydwl.live.domain.vo.LiveScheduleVo;
import com.ydwl.live.domain.LiveSchedule;
import com.ydwl.live.mapper.LiveScheduleMapper;
import com.ydwl.live.service.ILiveScheduleService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 直播预告Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveScheduleServiceImpl implements ILiveScheduleService {

    private final LiveScheduleMapper baseMapper;

    /**
     * 查询直播预告
     *
     * @param id 主键
     * @return 直播预告
     */
    @Override
    public LiveScheduleVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询直播预告列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播预告分页列表
     */
    @Override
    public TableDataInfo<LiveScheduleVo> queryPageList(LiveScheduleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveSchedule> lqw = buildQueryWrapper(bo);
        Page<LiveScheduleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的直播预告列表
     *
     * @param bo 查询条件
     * @return 直播预告列表
     */
    @Override
    public List<LiveScheduleVo> queryList(LiveScheduleBo bo) {
        LambdaQueryWrapper<LiveSchedule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveSchedule> buildQueryWrapper(LiveScheduleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveSchedule> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveSchedule::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), LiveSchedule::getTitle, bo.getTitle());
        lqw.eq(bo.getHostUserId() != null, LiveSchedule::getHostUserId, bo.getHostUserId());
        lqw.eq(bo.getCategoryId() != null, LiveSchedule::getCategoryId, bo.getCategoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), LiveSchedule::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverImgUrl()), LiveSchedule::getCoverImgUrl, bo.getCoverImgUrl());
        lqw.eq(bo.getScheduledTime() != null, LiveSchedule::getScheduledTime, bo.getScheduledTime());
        lqw.eq(bo.getEstimatedDurationMinutes() != null, LiveSchedule::getEstimatedDurationMinutes, bo.getEstimatedDurationMinutes());
        lqw.eq(bo.getScheduleStatus() != null, LiveSchedule::getScheduleStatus, bo.getScheduleStatus());
        return lqw;
    }

    /**
     * 新增直播预告
     *
     * @param bo 直播预告
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveScheduleBo bo) {
        LiveSchedule add = MapstructUtils.convert(bo, LiveSchedule.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改直播预告
     *
     * @param bo 直播预告
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveScheduleBo bo) {
        LiveSchedule update = MapstructUtils.convert(bo, LiveSchedule.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveSchedule entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除直播预告信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
