package com.ydwl.live.domain.model.valueobject;

import lombok.Getter;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 领域事件基类
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
public abstract class DomainEvent {
    
    /**
     * 事件ID
     */
    private final String eventId;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredOn;
    
    /**
     * 聚合根ID
     */
    private final LiveId aggregateId;
    
    protected DomainEvent(LiveId aggregateId) {
        this.eventId = UUID.randomUUID().toString();
        this.occurredOn = LocalDateTime.now();
        this.aggregateId = aggregateId;
    }
    
    /**
     * 获取事件类型
     */
    public abstract String getEventType();
}
