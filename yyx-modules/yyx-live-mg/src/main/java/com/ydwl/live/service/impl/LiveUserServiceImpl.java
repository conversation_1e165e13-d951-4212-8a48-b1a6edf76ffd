package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveUserBo;
import com.ydwl.live.domain.vo.LiveUserVo;
import com.ydwl.live.domain.LiveUser;
import com.ydwl.live.mapper.LiveUserMapper;
import com.ydwl.live.service.ILiveUserService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 用户Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveUserServiceImpl implements ILiveUserService {

    private final LiveUserMapper baseMapper;

    /**
     * 查询用户
     *
     * @param id 主键
     * @return 用户
     */
    @Override
    public LiveUserVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户分页列表
     */
    @Override
    public TableDataInfo<LiveUserVo> queryPageList(LiveUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveUser> lqw = buildQueryWrapper(bo);
        Page<LiveUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户列表
     *
     * @param bo 查询条件
     * @return 用户列表
     */
    @Override
    public List<LiveUserVo> queryList(LiveUserBo bo) {
        LambdaQueryWrapper<LiveUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveUser> buildQueryWrapper(LiveUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveUser> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveUser::getId);
        lqw.like(StringUtils.isNotBlank(bo.getUsername()), LiveUser::getUsername, bo.getUsername());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), LiveUser::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getOpenid()), LiveUser::getOpenid, bo.getOpenid());
        lqw.eq(StringUtils.isNotBlank(bo.getAvatarUrl()), LiveUser::getAvatarUrl, bo.getAvatarUrl());
        lqw.like(StringUtils.isNotBlank(bo.getNickname()), LiveUser::getNickname, bo.getNickname());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), LiveUser::getEmail, bo.getEmail());
        lqw.eq(bo.getUserStatus() != null, LiveUser::getUserStatus, bo.getUserStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getBio()), LiveUser::getBio, bo.getBio());
        lqw.eq(bo.getGender() != null, LiveUser::getGender, bo.getGender());
        lqw.eq(bo.getBirthday() != null, LiveUser::getBirthday, bo.getBirthday());
        lqw.eq(bo.getLastLoginTime() != null, LiveUser::getLastLoginTime, bo.getLastLoginTime());
        lqw.eq(StringUtils.isNotBlank(bo.getLastLoginIp()), LiveUser::getLastLoginIp, bo.getLastLoginIp());
        lqw.eq(StringUtils.isNotBlank(bo.getNotificationSettings()), LiveUser::getNotificationSettings, bo.getNotificationSettings());
        lqw.eq(StringUtils.isNotBlank(bo.getUserStatistics()), LiveUser::getUserStatistics, bo.getUserStatistics());
        return lqw;
    }

    /**
     * 新增用户
     *
     * @param bo 用户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveUserBo bo) {
        LiveUser add = MapstructUtils.convert(bo, LiveUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户
     *
     * @param bo 用户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveUserBo bo) {
        LiveUser update = MapstructUtils.convert(bo, LiveUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveUser entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
