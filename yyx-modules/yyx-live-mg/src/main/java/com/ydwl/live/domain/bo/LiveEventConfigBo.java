package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveEventConfig;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 事件配置业务对象 live_event_config
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveEventConfig.class, reverseConvertGenerate = false)
public class LiveEventConfigBo extends BaseEntity {

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 事件类型（live）
     */
    @NotBlank(message = "事件类型（live）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String eventType;

    /**
     * 事件ID
     */
    @NotNull(message = "事件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long eventId;

    /**
     * 报名开始时间
     */
    @NotNull(message = "报名开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date signupStart;

    /**
     * 报名截止时间
     */
    @NotNull(message = "报名截止时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date signupEnd;

    /**
     * 最大参与人数
     */
    private Long maxParticipants;

    /**
     * 报名需填字段配置
     */
    private String signupFields;


}
