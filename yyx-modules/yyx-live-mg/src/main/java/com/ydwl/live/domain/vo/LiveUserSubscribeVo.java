package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.live.domain.LiveUserSubscribe;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用户订阅视图对象 live_user_subscribe
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveUserSubscribe.class)
public class LiveUserSubscribeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订阅ID
     */
    @ExcelProperty(value = "订阅ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 直播ID
     */
    @ExcelProperty(value = "直播ID")
    private Long liveId;

    /**
     * 订阅时间
     */
    @ExcelProperty(value = "订阅时间")
    private Date subscribeTime;

    /**
     * 是否已通知(0-未通知,1-已通知)
     */
    @ExcelProperty(value = "是否已通知(0-未通知,1-已通知)")
    private Long isNotified;


}
