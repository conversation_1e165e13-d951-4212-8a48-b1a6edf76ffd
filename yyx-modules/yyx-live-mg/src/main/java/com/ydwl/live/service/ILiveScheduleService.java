package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveScheduleVo;
import com.ydwl.live.domain.bo.LiveScheduleBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 直播预告Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveScheduleService {

    /**
     * 查询直播预告
     *
     * @param id 主键
     * @return 直播预告
     */
    LiveScheduleVo queryById(Long id);

    /**
     * 分页查询直播预告列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播预告分页列表
     */
    TableDataInfo<LiveScheduleVo> queryPageList(LiveScheduleBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的直播预告列表
     *
     * @param bo 查询条件
     * @return 直播预告列表
     */
    List<LiveScheduleVo> queryList(LiveScheduleBo bo);

    /**
     * 新增直播预告
     *
     * @param bo 直播预告
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveScheduleBo bo);

    /**
     * 修改直播预告
     *
     * @param bo 直播预告
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveScheduleBo bo);

    /**
     * 校验并批量删除直播预告信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
