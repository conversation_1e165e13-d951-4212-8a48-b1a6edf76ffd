package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveVideoPlayRecordVo;
import com.ydwl.live.domain.bo.LiveVideoPlayRecordBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 视频播放记录Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-05-21
 */
public interface ILiveVideoPlayRecordService {

    /**
     * 查询视频播放记录
     *
     * @param id 主键
     * @return 视频播放记录
     */
    LiveVideoPlayRecordVo queryById(Long id);

    /**
     * 分页查询视频播放记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频播放记录分页列表
     */
    TableDataInfo<LiveVideoPlayRecordVo> queryPageList(LiveVideoPlayRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的视频播放记录列表
     *
     * @param bo 查询条件
     * @return 视频播放记录列表
     */
    List<LiveVideoPlayRecordVo> queryList(LiveVideoPlayRecordBo bo);

    /**
     * 新增视频播放记录
     *
     * @param bo 视频播放记录
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveVideoPlayRecordBo bo);

    /**
     * 修改视频播放记录
     *
     * @param bo 视频播放记录
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveVideoPlayRecordBo bo);

    /**
     * 校验并批量删除视频播放记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
