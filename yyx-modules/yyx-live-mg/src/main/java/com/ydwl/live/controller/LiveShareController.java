package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveShareVo;
import com.ydwl.live.domain.bo.LiveShareBo;
import com.ydwl.live.service.ILiveShareService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 直播分享记录
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/share")
public class LiveShareController extends BaseController {

    private final ILiveShareService liveShareService;

    /**
     * 查询直播分享记录列表
     */
    @SaCheckPermission("live:share:list")
    @GetMapping("/list")
    public TableDataInfo<LiveShareVo> list(LiveShareBo bo, PageQuery pageQuery) {
        return liveShareService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出直播分享记录列表
     */
    @SaCheckPermission("live:share:export")
    @Log(title = "直播分享记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveShareBo bo, HttpServletResponse response) {
        List<LiveShareVo> list = liveShareService.queryList(bo);
        ExcelUtil.exportExcel(list, "直播分享记录", LiveShareVo.class, response);
    }

    /**
     * 获取直播分享记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:share:query")
    @GetMapping("/{id}")
    public R<LiveShareVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveShareService.queryById(id));
    }

    /**
     * 新增直播分享记录
     */
    @SaCheckPermission("live:share:add")
    @Log(title = "直播分享记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveShareBo bo) {
        return toAjax(liveShareService.insertByBo(bo));
    }

    /**
     * 修改直播分享记录
     */
    @SaCheckPermission("live:share:edit")
    @Log(title = "直播分享记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveShareBo bo) {
        return toAjax(liveShareService.updateByBo(bo));
    }

    /**
     * 删除直播分享记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:share:remove")
    @Log(title = "直播分享记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveShareService.deleteWithValidByIds(List.of(ids), true));
    }
}
