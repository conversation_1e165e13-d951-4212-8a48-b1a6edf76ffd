package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveTranscodeTaskBo;
import com.ydwl.live.domain.vo.LiveTranscodeTaskVo;
import com.ydwl.live.domain.LiveTranscodeTask;
import com.ydwl.live.mapper.LiveTranscodeTaskMapper;
import com.ydwl.live.service.ILiveTranscodeTaskService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 转码任务Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveTranscodeTaskServiceImpl implements ILiveTranscodeTaskService {

    private final LiveTranscodeTaskMapper baseMapper;

    /**
     * 查询转码任务
     *
     * @param id 主键
     * @return 转码任务
     */
    @Override
    public LiveTranscodeTaskVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询转码任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转码任务分页列表
     */
    @Override
    public TableDataInfo<LiveTranscodeTaskVo> queryPageList(LiveTranscodeTaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveTranscodeTask> lqw = buildQueryWrapper(bo);
        Page<LiveTranscodeTaskVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的转码任务列表
     *
     * @param bo 查询条件
     * @return 转码任务列表
     */
    @Override
    public List<LiveTranscodeTaskVo> queryList(LiveTranscodeTaskBo bo) {
        LambdaQueryWrapper<LiveTranscodeTask> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveTranscodeTask> buildQueryWrapper(LiveTranscodeTaskBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveTranscodeTask> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveTranscodeTask::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTaskNo()), LiveTranscodeTask::getTaskNo, bo.getTaskNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskType()), LiveTranscodeTask::getTaskType, bo.getTaskType());
        lqw.eq(bo.getLiveId() != null, LiveTranscodeTask::getLiveId, bo.getLiveId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceUrl()), LiveTranscodeTask::getSourceUrl, bo.getSourceUrl());
        lqw.eq(bo.getStatus() != null, LiveTranscodeTask::getStatus, bo.getStatus());
        lqw.eq(bo.getTemplateId() != null, LiveTranscodeTask::getTemplateId, bo.getTemplateId());
        lqw.eq(StringUtils.isNotBlank(bo.getBucket()), LiveTranscodeTask::getBucket, bo.getBucket());
        lqw.eq(StringUtils.isNotBlank(bo.getObjectKey()), LiveTranscodeTask::getObjectKey, bo.getObjectKey());
        lqw.eq(StringUtils.isNotBlank(bo.getOutputUrl()), LiveTranscodeTask::getOutputUrl, bo.getOutputUrl());
        lqw.eq(bo.getDuration() != null, LiveTranscodeTask::getDuration, bo.getDuration());
        lqw.eq(bo.getFileSize() != null, LiveTranscodeTask::getFileSize, bo.getFileSize());
        lqw.eq(bo.getProgress() != null, LiveTranscodeTask::getProgress, bo.getProgress());
        lqw.eq(StringUtils.isNotBlank(bo.getBizId()), LiveTranscodeTask::getBizId, bo.getBizId());
        lqw.eq(StringUtils.isNotBlank(bo.getCallbackUrl()), LiveTranscodeTask::getCallbackUrl, bo.getCallbackUrl());
        lqw.eq(bo.getCallbackStatus() != null, LiveTranscodeTask::getCallbackStatus, bo.getCallbackStatus());
        lqw.eq(bo.getStartTime() != null, LiveTranscodeTask::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, LiveTranscodeTask::getEndTime, bo.getEndTime());
        lqw.eq(StringUtils.isNotBlank(bo.getErrorMsg()), LiveTranscodeTask::getErrorMsg, bo.getErrorMsg());
        lqw.eq(bo.getRetryCount() != null, LiveTranscodeTask::getRetryCount, bo.getRetryCount());
        lqw.eq(bo.getPriority() != null, LiveTranscodeTask::getPriority, bo.getPriority());
        return lqw;
    }

    /**
     * 新增转码任务
     *
     * @param bo 转码任务
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveTranscodeTaskBo bo) {
        LiveTranscodeTask add = MapstructUtils.convert(bo, LiveTranscodeTask.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改转码任务
     *
     * @param bo 转码任务
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveTranscodeTaskBo bo) {
        LiveTranscodeTask update = MapstructUtils.convert(bo, LiveTranscodeTask.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveTranscodeTask entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除转码任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据业务ID查询转码任务
     *
     * @param bizId 业务ID
     * @return 转码任务
     */
    @Override
    public LiveTranscodeTaskVo queryByBizId(String bizId) {
        if (StringUtils.isBlank(bizId)) {
            return null;
        }
        LambdaQueryWrapper<LiveTranscodeTask> lqw = Wrappers.lambdaQuery();
        lqw.eq(LiveTranscodeTask::getBizId, bizId);
        lqw.last("LIMIT 1");
        return baseMapper.selectVoOne(lqw);
    }
}
