package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveVideoPlayRecordVo;
import com.ydwl.live.domain.bo.LiveVideoPlayRecordBo;
import com.ydwl.live.service.ILiveVideoPlayRecordService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 视频播放记录
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/videoPlayRecord")
public class LiveVideoPlayRecordController extends BaseController {

    private final ILiveVideoPlayRecordService liveVideoPlayRecordService;

    /**
     * 查询视频播放记录列表
     */
    @SaCheckPermission("live:videoPlayRecord:list")
    @GetMapping("/list")
    public TableDataInfo<LiveVideoPlayRecordVo> list(LiveVideoPlayRecordBo bo, PageQuery pageQuery) {
        return liveVideoPlayRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出视频播放记录列表
     */
    @SaCheckPermission("live:videoPlayRecord:export")
    @Log(title = "视频播放记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveVideoPlayRecordBo bo, HttpServletResponse response) {
        List<LiveVideoPlayRecordVo> list = liveVideoPlayRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "视频播放记录", LiveVideoPlayRecordVo.class, response);
    }

    /**
     * 获取视频播放记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:videoPlayRecord:query")
    @GetMapping("/{id}")
    public R<LiveVideoPlayRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveVideoPlayRecordService.queryById(id));
    }

    /**
     * 新增视频播放记录
     */
    @SaCheckPermission("live:videoPlayRecord:add")
    @Log(title = "视频播放记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveVideoPlayRecordBo bo) {
        return toAjax(liveVideoPlayRecordService.insertByBo(bo));
    }

    /**
     * 修改视频播放记录
     */
    @SaCheckPermission("live:videoPlayRecord:edit")
    @Log(title = "视频播放记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveVideoPlayRecordBo bo) {
        return toAjax(liveVideoPlayRecordService.updateByBo(bo));
    }

    /**
     * 删除视频播放记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:videoPlayRecord:remove")
    @Log(title = "视频播放记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveVideoPlayRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
