package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveShareVo;
import com.ydwl.live.domain.bo.LiveShareBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 直播分享记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveShareService {

    /**
     * 查询直播分享记录
     *
     * @param id 主键
     * @return 直播分享记录
     */
    LiveShareVo queryById(Long id);

    /**
     * 分页查询直播分享记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播分享记录分页列表
     */
    TableDataInfo<LiveShareVo> queryPageList(LiveShareBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的直播分享记录列表
     *
     * @param bo 查询条件
     * @return 直播分享记录列表
     */
    List<LiveShareVo> queryList(LiveShareBo bo);

    /**
     * 新增直播分享记录
     *
     * @param bo 直播分享记录
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveShareBo bo);

    /**
     * 修改直播分享记录
     *
     * @param bo 直播分享记录
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveShareBo bo);

    /**
     * 校验并批量删除直播分享记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
