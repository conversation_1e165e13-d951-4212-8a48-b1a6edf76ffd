package com.ydwl.live.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 直播ID值对象
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
@EqualsAndHashCode
public class LiveId {
    
    private final Long value;
    
    private LiveId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("直播ID不能为空或小于等于0");
        }
        this.value = value;
    }
    
    /**
     * 创建直播ID
     */
    public static LiveId of(Long value) {
        return new LiveId(value);
    }
    
    /**
     * 生成新的直播ID（实际项目中可能使用雪花算法等）
     */
    public static LiveId generate() {
        // 这里简化处理，实际应该使用分布式ID生成器
        return new LiveId(System.currentTimeMillis());
    }
    
    @Override
    public String toString() {
        return "LiveId{" + value + "}";
    }
}
