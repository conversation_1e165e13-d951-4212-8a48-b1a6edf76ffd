package com.ydwl.live.callback.dto;


import lombok.Data;

/**
 * 推流按需录制回调 阿里云回调给服务器的数据
 */
@Data
public class OnDemandRecordCallbackDto {
    /**
     * 主播流域名。
     */
    private String domain;
    /**
     * 播流所属应用名称。
     */
    private String app;
    /**
     * 直播流名称。
     */
    private String stream;
    /**
     * 编码方式。取值：
     * h264
     * h265
     */
    private String codec;
    /**
     * 视频码率。单位：kbps。
     */
    private String vbitrate;
    
    /**
     * 录制事件类型
     * record_started: 录制开始
     * record_paused: 录制暂停
     * record_completed: 录制完成
     */
    private String event;
    
    /**
     * 录制文件URI
     */
    private String uri;
    
    /**
     * 录制ID
     */
    private String recordId;
    
    /**
     * 录制时长（秒）
     */
    private Double duration;
    
    /**
     * 录制开始时间（Unix时间戳）
     */
    private Long startTime;
    
    /**
     * 录制结束时间（Unix时间戳）
     */
    private Long stopTime;

    /* 例子
     * GET /?app=seq_all&domain=demo.aliyundoc.com&stream=ondemand8&vbitrate=2000&codec=h264 HTTP/1.1
     * Host: example.aliyundoc.com
     * User-Agent: Go-http-client/1.1
     * Accept-Encoding: gzip
     */
}
