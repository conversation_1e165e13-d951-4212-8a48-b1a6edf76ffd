package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveOrder;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单业务对象 live_order
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveOrder.class, reverseConvertGenerate = false)
public class LiveOrderBo extends BaseEntity {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 订单类型（1-充值，2-购买会员）
     */
    @NotNull(message = "订单类型（1-充值，2-购买会员）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderType;

    /**
     * 订单金额
     */
    @NotNull(message = "订单金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 虚拟币数量
     */
    private Long coinAmount;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 第三方交易号
     */
    private String transactionId;

    /**
     * 订单状态（0-待支付，1-已支付，2-已取消，3-已退款）
     */
    @NotNull(message = "订单状态（0-待支付，1-已支付，2-已取消，3-已退款）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;


}
