package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.live.domain.LiveBannedUser;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 直播禁言用户视图对象 live_banned_user
 *
 * <AUTHOR> <PERSON>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveBannedUser.class)
public class LiveBannedUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 禁言记录ID
     */
    @ExcelProperty(value = "禁言记录ID")
    private Long id;

    /**
     * 直播ID
     */
    @ExcelProperty(value = "直播ID")
    private Long liveId;

    /**
     * 被禁言用户ID
     */
    @ExcelProperty(value = "被禁言用户ID")
    private Long userId;

    /**
     * 禁言原因
     */
    @ExcelProperty(value = "禁言原因")
    private String banReason;

    /**
     * 操作人ID
     */
    @ExcelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 禁言时间
     */
    @ExcelProperty(value = "禁言时间")
    private Date banTime;

    /**
     * 解除禁言时间
     */
    @ExcelProperty(value = "解除禁言时间")
    private Date unbanTime;

    /**
     * 禁言状态(0-已解除,1-生效中)
     */
    @ExcelProperty(value = "禁言状态(0-已解除,1-生效中)")
    private Long banStatus;


}
