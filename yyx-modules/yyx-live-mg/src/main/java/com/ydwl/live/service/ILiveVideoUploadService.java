package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveVideoUploadVo;
import com.ydwl.live.domain.bo.LiveVideoUploadBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 视频上传记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveVideoUploadService {

    /**
     * 查询视频上传记录
     *
     * @param id 主键
     * @return 视频上传记录
     */
    LiveVideoUploadVo queryById(Long id);

    /**
     * 分页查询视频上传记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频上传记录分页列表
     */
    TableDataInfo<LiveVideoUploadVo> queryPageList(LiveVideoUploadBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的视频上传记录列表
     *
     * @param bo 查询条件
     * @return 视频上传记录列表
     */
    List<LiveVideoUploadVo> queryList(LiveVideoUploadBo bo);

    /**
     * 新增视频上传记录
     *
     * @param bo 视频上传记录
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveVideoUploadBo bo);

    /**
     * 修改视频上传记录
     *
     * @param bo 视频上传记录
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveVideoUploadBo bo);

    /**
     * 校验并批量删除视频上传记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
