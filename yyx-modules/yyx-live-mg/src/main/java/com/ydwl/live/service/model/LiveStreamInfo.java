package com.ydwl.live.service.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 推流信息数据模型
 * <p>用于不同服务之间传递推流相关信息，避免服务之间直接依赖</p>
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiveStreamInfo {
    
    /**
     * 推流ID
     */
    private Long id;
    
    /**
     * 直播ID
     */
    private Long liveId;
    
    /**
     * 推流地址
     */
    private String pushUrl;
    
    /**
     * 推流密钥
     */
    private String pushKey;
    
    /**
     * 推流状态：1-正常 2-异常
     */
    private Long streamStatus;
    
    /**
     * 直播标题
     */
    private String title;
    
    /**
     * 播放地址映射，key为格式，value为URL
     */
    private Map<String, String> playUrls = new HashMap<>();
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 