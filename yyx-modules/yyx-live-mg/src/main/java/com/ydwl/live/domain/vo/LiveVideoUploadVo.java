package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.live.domain.LiveVideoUpload;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 视频上传记录视图对象 live_video_upload
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveVideoUpload.class)
public class LiveVideoUploadVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 上传ID
     */
    @ExcelProperty(value = "上传ID")
    private Long id;

    /**
     * 直播ID
     */
    @ExcelProperty(value = "直播ID")
    private Long liveId;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    private String fileName;

    /**
     * 文件大小(单位:字节)
     */
    @ExcelProperty(value = "文件大小(单位:字节)")
    private Long fileSizeBytes;

    /**
     * 文件类型
     */
    @ExcelProperty(value = "文件类型")
    private String fileType;

    /**
     * OSS存储地址
     */
    @ExcelProperty(value = "OSS存储地址")
    private String ossUrl;

    /**
     * 文件MD5
     */
    @ExcelProperty(value = "文件MD5")
    private String fileMd5;

    /**
     * 视频时长(单位:秒)
     */
    @ExcelProperty(value = "视频时长(单位:秒)")
    private Double videoDurationSeconds;

    /**
     * 视频分辨率
     */
    @ExcelProperty(value = "视频分辨率")
    private String videoResolution;

    /**
     * 视频码率(单位:Kbps)
     */
    @ExcelProperty(value = "视频码率(单位:Kbps)")
    private Integer videoBitrateKbps;

    /**
     * 帧率
     */
    @ExcelProperty(value = "帧率")
    private Double frameRate;

    /**
     * 视频编码格式
     */
    @ExcelProperty(value = "视频编码格式")
    private String videoCodec;

    /**
     * 音频编码格式
     */
    @ExcelProperty(value = "音频编码格式")
    private String audioCodec;

    /**
     * 宽高比
     */
    @ExcelProperty(value = "宽高比")
    private String aspectRatio;

    /**
     * 创建日期
     */
    @ExcelProperty(value = "创建日期")
    private String createdDate;

    /**
     * 最后修改日期
     */
    @ExcelProperty(value = "最后修改日期")
    private String lastModifiedDate;

    /**
     * 状态(0-上传中,1-已完成,2-失败)
     */
    @ExcelProperty(value = "状态(0-上传中,1-已完成,2-失败)")
    private Long uploadStatus;

    /**
     * 上传进度(单位:%)
     */
    @ExcelProperty(value = "上传进度(单位:%)")
    private Long uploadProgressPercent;

    /**
     * 上传完成时间
     */
    @ExcelProperty(value = "上传完成时间")
    private Date uploadCompleteTime;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errorMessage;


}
