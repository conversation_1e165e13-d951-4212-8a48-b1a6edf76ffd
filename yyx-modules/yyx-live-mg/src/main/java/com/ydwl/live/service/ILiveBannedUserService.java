package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveBannedUserVo;
import com.ydwl.live.domain.bo.LiveBannedUserBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 直播禁言用户Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveBannedUserService {

    /**
     * 查询直播禁言用户
     *
     * @param id 主键
     * @return 直播禁言用户
     */
    LiveBannedUserVo queryById(Long id);

    /**
     * 分页查询直播禁言用户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播禁言用户分页列表
     */
    TableDataInfo<LiveBannedUserVo> queryPageList(LiveBannedUserBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的直播禁言用户列表
     *
     * @param bo 查询条件
     * @return 直播禁言用户列表
     */
    List<LiveBannedUserVo> queryList(LiveBannedUserBo bo);

    /**
     * 新增直播禁言用户
     *
     * @param bo 直播禁言用户
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveBannedUserBo bo);

    /**
     * 修改直播禁言用户
     *
     * @param bo 直播禁言用户
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveBannedUserBo bo);

    /**
     * 校验并批量删除直播禁言用户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
