package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveVo;
import com.ydwl.live.domain.bo.LiveBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 直播信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveService {

    /**
     * 查询直播信息
     *
     * @param id 主键
     * @return 直播信息
     */
    LiveVo queryById(Long id);

    /**
     * 分页查询直播信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播信息分页列表
     */
    TableDataInfo<LiveVo> queryPageList(LiveBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的直播信息列表
     * @param bo 查询条件
     * @return 直播信息列表
     */
    List<LiveVo> queryList(LiveBo bo);

    /**
     * 新增直播信息
     *
     * @param bo 直播信息
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveBo bo);

    /**
     * 修改直播信息
     * @param bo 直播信息
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveBo bo);

    /**
     * 校验并批量删除直播信息信息
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
    
    /**
     * 按分类ID查询直播信息列表（分页）
     *
     * @param categoryId 分类ID
     * @param status     直播状态(可选，null表示查询所有状态)
     * @param pageQuery  分页参数
     * @return 直播信息分页列表
     */
    TableDataInfo<LiveVo> queryPageListByCategory(Long categoryId, Long status, PageQuery pageQuery);
    
    /**
     * 按分类ID查询直播信息列表（不分页）
     *
     * @param categoryId 分类ID
     * @param status     直播状态(可选，null表示查询所有状态)
     * @param limit      返回数量限制(可选)
     * @return 直播信息列表
     */
    List<LiveVo> queryListByCategory(Long categoryId, Long status, Integer limit);
    
    /**
     * 按分类路径查询直播信息列表（包含子分类，分页）
     *
     * @param categoryPath 分类路径
     * @param status       直播状态(可选，null表示查询所有状态)
     * @param pageQuery    分页参数
     * @return 直播信息分页列表
     */
    TableDataInfo<LiveVo> queryPageListByCategoryPath(String categoryPath, Long status, PageQuery pageQuery);
    
    /**
     * 分页查询直播信息列表（包含推流、回放等详细信息）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 包含详细信息的直播分页列表
     */
    TableDataInfo<Map<String, Object>> queryPageListWithDetails(LiveBo bo, PageQuery pageQuery);
}
