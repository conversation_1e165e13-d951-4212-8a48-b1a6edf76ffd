package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveStream;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 推流信息业务对象 live_stream
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveStream.class, reverseConvertGenerate = false)
public class LiveStreamBo extends BaseEntity {

    /**
     * 推流信息ID
     */
    @NotNull(message = "推流信息ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long liveId;

    /**
     * 推流地址
     */
    @NotBlank(message = "推流地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pushUrl;

    /**
     * 推流密钥
     */
    @NotBlank(message = "推流密钥不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pushKey;

    /**
     * 状态(0-创建中,1-正常,2-异常)
     */
    @NotNull(message = "状态(0-创建中,1-正常,2-异常)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long streamStatus;


}
