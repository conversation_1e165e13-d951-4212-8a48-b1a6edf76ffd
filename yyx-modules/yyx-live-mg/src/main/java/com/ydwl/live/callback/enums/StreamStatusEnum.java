package com.ydwl.live.callback.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 直播流状态枚举
 */
@Getter
@AllArgsConstructor
public enum StreamStatusEnum {

    CREATING(0L, "创建中"),
    NORMAL(1L, "正常"),
    ABNORMAL(2L, "异常");

    private final Long code;
    private final String desc;

    public static StreamStatusEnum getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (StreamStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
