package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeOutput;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 转码输出文件视图对象 live_transcode_output
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveTranscodeOutput.class)
public class LiveTranscodeOutputVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 输出ID
     */
    @ExcelProperty(value = "输出ID")
    private Long id;

    /**
     * 转码任务ID
     */
    @ExcelProperty(value = "转码任务ID")
    private Long taskId;

    /**
     * 清晰度标识(SD-标清,HD-高清,FHD-超清)
     */
    @ExcelProperty(value = "清晰度标识(SD-标清,HD-高清,FHD-超清)")
    private String definition;

    /**
     * 分辨率(如:1920x1080)
     */
    @ExcelProperty(value = "分辨率(如:1920x1080)")
    private String resolution;

    /**
     * 输出格式(mp4,hls等)
     */
    @ExcelProperty(value = "输出格式(mp4,hls等)")
    private String format;

    /**
     * 输出文件地址
     */
    @ExcelProperty(value = "输出文件地址")
    private String url;

    /**
     * 分片时长(秒)
     */
    @ExcelProperty(value = "分片时长(秒)")
    private Long segmentTime;

    /**
     * 文件大小(byte)
     */
    @ExcelProperty(value = "文件大小(byte)")
    private Long fileSize;

    /**
     * 实际码率(kbps)
     */
    @ExcelProperty(value = "实际码率(kbps)")
    private Long bitRate;

    /**
     * 时长(秒)
     */
    @ExcelProperty(value = "时长(秒)")
    private Long duration;


}
