package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveBannedUserBo;
import com.ydwl.live.domain.vo.LiveBannedUserVo;
import com.ydwl.live.domain.LiveBannedUser;
import com.ydwl.live.mapper.LiveBannedUserMapper;
import com.ydwl.live.service.ILiveBannedUserService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 直播禁言用户Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveBannedUserServiceImpl implements ILiveBannedUserService {

    private final LiveBannedUserMapper baseMapper;

    /**
     * 查询直播禁言用户
     *
     * @param id 主键
     * @return 直播禁言用户
     */
    @Override
    public LiveBannedUserVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询直播禁言用户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播禁言用户分页列表
     */
    @Override
    public TableDataInfo<LiveBannedUserVo> queryPageList(LiveBannedUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveBannedUser> lqw = buildQueryWrapper(bo);
        Page<LiveBannedUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的直播禁言用户列表
     *
     * @param bo 查询条件
     * @return 直播禁言用户列表
     */
    @Override
    public List<LiveBannedUserVo> queryList(LiveBannedUserBo bo) {
        LambdaQueryWrapper<LiveBannedUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveBannedUser> buildQueryWrapper(LiveBannedUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveBannedUser> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveBannedUser::getId);
        lqw.eq(bo.getLiveId() != null, LiveBannedUser::getLiveId, bo.getLiveId());
        lqw.eq(bo.getUserId() != null, LiveBannedUser::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getBanReason()), LiveBannedUser::getBanReason, bo.getBanReason());
        lqw.eq(bo.getOperatorId() != null, LiveBannedUser::getOperatorId, bo.getOperatorId());
        lqw.eq(bo.getBanTime() != null, LiveBannedUser::getBanTime, bo.getBanTime());
        lqw.eq(bo.getUnbanTime() != null, LiveBannedUser::getUnbanTime, bo.getUnbanTime());
        lqw.eq(bo.getBanStatus() != null, LiveBannedUser::getBanStatus, bo.getBanStatus());
        return lqw;
    }

    /**
     * 新增直播禁言用户
     *
     * @param bo 直播禁言用户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveBannedUserBo bo) {
        LiveBannedUser add = MapstructUtils.convert(bo, LiveBannedUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改直播禁言用户
     *
     * @param bo 直播禁言用户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveBannedUserBo bo) {
        LiveBannedUser update = MapstructUtils.convert(bo, LiveBannedUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveBannedUser entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除直播禁言用户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
