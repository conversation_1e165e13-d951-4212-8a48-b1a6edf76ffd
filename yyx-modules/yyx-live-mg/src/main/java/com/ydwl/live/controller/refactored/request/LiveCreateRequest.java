package com.ydwl.live.controller.refactored.request;

import com.ydwl.common.core.validate.AddGroup;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 创建直播请求
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Data
public class LiveCreateRequest {
    
    /**
     * 直播分类ID
     */
    @NotNull(message = "直播分类ID不能为空", groups = {AddGroup.class})
    private Long categoryId;
    
    /**
     * 直播标题
     */
    @NotBlank(message = "直播标题不能为空", groups = {AddGroup.class})
    private String title;
    
    /**
     * 直播封面图片URL
     */
    private String coverImgUrl;
    
    /**
     * 计划开始时间
     */
    private LocalDateTime planStartTime;
    
    /**
     * 直播描述
     */
    private String description;
    
    /**
     * 标签列表
     */
    private String tagList;
    
    /**
     * 是否需要报名
     */
    private boolean signupRequired = false;
    
    /**
     * 是否启用回放
     */
    private boolean replayEnabled = true;
    
    /**
     * 是否自动录制
     */
    private boolean autoRecord = true;
    
    /**
     * 是否启用聊天
     */
    private boolean chatEnabled = true;
    
    /**
     * 聊天延迟时间(秒)
     */
    private Long chatDelay = 0L;
    
    /**
     * 是否启用礼物
     */
    private boolean giftEnabled = true;
    
    /**
     * 默认画质
     */
    private String defaultQuality = "HD";
    
    /**
     * 访问权限级别
     */
    private Long accessLevel = 0L;
}
