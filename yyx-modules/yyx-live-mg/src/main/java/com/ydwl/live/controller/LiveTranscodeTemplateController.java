package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveTranscodeTemplateVo;
import com.ydwl.live.domain.bo.LiveTranscodeTemplateBo;
import com.ydwl.live.service.ILiveTranscodeTemplateService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 视频转码模板
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/transcodeTemplate")
public class LiveTranscodeTemplateController extends BaseController {

    private final ILiveTranscodeTemplateService liveTranscodeTemplateService;

    /**
     * 查询视频转码模板列表
     */
    @SaCheckPermission("live:transcodeTemplate:list")
    @GetMapping("/list")
    public TableDataInfo<LiveTranscodeTemplateVo> list(LiveTranscodeTemplateBo bo, PageQuery pageQuery) {
        return liveTranscodeTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出视频转码模板列表
     */
    @SaCheckPermission("live:transcodeTemplate:export")
    @Log(title = "视频转码模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveTranscodeTemplateBo bo, HttpServletResponse response) {
        List<LiveTranscodeTemplateVo> list = liveTranscodeTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "视频转码模板", LiveTranscodeTemplateVo.class, response);
    }

    /**
     * 获取视频转码模板详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:transcodeTemplate:query")
    @GetMapping("/{id}")
    public R<LiveTranscodeTemplateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveTranscodeTemplateService.queryById(id));
    }

    /**
     * 新增视频转码模板
     */
    @SaCheckPermission("live:transcodeTemplate:add")
    @Log(title = "视频转码模板", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveTranscodeTemplateBo bo) {
        return toAjax(liveTranscodeTemplateService.insertByBo(bo));
    }

    /**
     * 修改视频转码模板
     */
    @SaCheckPermission("live:transcodeTemplate:edit")
    @Log(title = "视频转码模板", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveTranscodeTemplateBo bo) {
        return toAjax(liveTranscodeTemplateService.updateByBo(bo));
    }

    /**
     * 删除视频转码模板
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:transcodeTemplate:remove")
    @Log(title = "视频转码模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveTranscodeTemplateService.deleteWithValidByIds(List.of(ids), true));
    }
}
