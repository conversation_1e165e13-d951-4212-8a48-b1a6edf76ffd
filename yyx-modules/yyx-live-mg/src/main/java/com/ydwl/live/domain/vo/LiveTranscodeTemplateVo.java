package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeTemplate;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 视频转码模板视图对象 live_transcode_template
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveTranscodeTemplate.class)
public class LiveTranscodeTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @ExcelProperty(value = "模板ID")
    private Long id;

    /**
     * 模板名称
     */
    @ExcelProperty(value = "模板名称")
    private String templateName;

    /**
     * 转码类型(fc-函数计算,mts-媒体转码服务)
     */
    @ExcelProperty(value = "转码类型(fc-函数计算,mts-媒体转码服务)")
    private String templateType;

    /**
     * 清晰度标识(SD-标清,HD-高清,FHD-超清)
     */
    @ExcelProperty(value = "清晰度标识(SD-标清,HD-高清,FHD-超清)")
    private String definition;

    /**
     * 分辨率(如:1920x1080)
     */
    @ExcelProperty(value = "分辨率(如:1920x1080)")
    private String resolution;

    /**
     * 视频码率(kbps)
     */
    @ExcelProperty(value = "视频码率(kbps)")
    private Long videoBitrate;

    /**
     * 音频码率(kbps)
     */
    @ExcelProperty(value = "音频码率(kbps)")
    private Long audioBitrate;

    /**
     * 帧率
     */
    @ExcelProperty(value = "帧率")
    private Long fps;

    /**
     * 输出格式
     */
    @ExcelProperty(value = "输出格式")
    private String format;

    /**
     * 编码方式
     */
    @ExcelProperty(value = "编码方式")
    private String codec;

    /**
     * 外部系统模板ID(阿里云模板ID)
     */
    @ExcelProperty(value = "外部系统模板ID(阿里云模板ID)")
    private String externalId;

    /**
     * 状态(0-禁用,1-启用)
     */
    @ExcelProperty(value = "状态(0-禁用,1-启用)")
    private Long status;

    /**
     * 是否默认(0-否,1-是)
     */
    @ExcelProperty(value = "是否默认(0-否,1-是)")
    private Long isDefault;


}
