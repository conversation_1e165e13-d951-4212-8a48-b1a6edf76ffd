package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.live.domain.LiveVideoPlayRecord;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 视频播放记录视图对象 live_video_play_record
 *
 * <AUTHOR> <PERSON>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveVideoPlayRecord.class)
public class LiveVideoPlayRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @ExcelProperty(value = "记录ID")
    private Long id;

    /**
     * 直播ID
     */
    @ExcelProperty(value = "直播ID")
    private Long liveId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 播放时间
     */
    @ExcelProperty(value = "播放时间")
    private Date playTime;

    /**
     * 播放时长(单位:秒)
     */
    @ExcelProperty(value = "播放时长(单位:秒)")
    private Long playDurationSeconds;

    /**
     * 最后播放位置(单位:秒)
     */
    @ExcelProperty(value = "最后播放位置(单位:秒)")
    private Long lastPositionSeconds;

    /**
     * 是否看完(0-否,1-是)
     */
    @ExcelProperty(value = "是否看完(0-否,1-是)")
    private Long isFinished;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型")
    private String deviceType;

    /**
     * IP地址
     */
    @ExcelProperty(value = "IP地址")
    private String ipAddress;

    /**
     * 播放清晰度
     */
    @ExcelProperty(value = "播放清晰度")
    private String videoQuality;


}
