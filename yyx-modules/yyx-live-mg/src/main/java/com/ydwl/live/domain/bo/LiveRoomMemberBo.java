package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveRoomMember;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 直播间在线成员业务对象 live_room_member
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveRoomMember.class, reverseConvertGenerate = false)
public class LiveRoomMemberBo extends BaseEntity {

    /**
     * 成员ID
     */
    @NotNull(message = "成员ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long liveId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 加入时间
     */
    @NotNull(message = "加入时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date joinTime;

    /**
     * 离开时间
     */
    private Date leaveTime;

    /**
     * 观看时长(单位:秒)
     */
    private Long watchDurationSeconds;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 设备信息
     */
    private String clientDevice;


}
