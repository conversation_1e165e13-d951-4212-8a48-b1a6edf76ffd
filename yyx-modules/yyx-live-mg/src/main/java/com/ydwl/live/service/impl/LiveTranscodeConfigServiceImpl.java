package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveTranscodeConfigBo;
import com.ydwl.live.domain.vo.LiveTranscodeConfigVo;
import com.ydwl.live.domain.LiveTranscodeConfig;
import com.ydwl.live.mapper.LiveTranscodeConfigMapper;
import com.ydwl.live.service.ILiveTranscodeConfigService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 转码配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveTranscodeConfigServiceImpl implements ILiveTranscodeConfigService {

    private final LiveTranscodeConfigMapper baseMapper;

    /**
     * 查询转码配置
     *
     * @param id 主键
     * @return 转码配置
     */
    @Override
    public LiveTranscodeConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询转码配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转码配置分页列表
     */
    @Override
    public TableDataInfo<LiveTranscodeConfigVo> queryPageList(LiveTranscodeConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveTranscodeConfig> lqw = buildQueryWrapper(bo);
        Page<LiveTranscodeConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的转码配置列表
     *
     * @param bo 查询条件
     * @return 转码配置列表
     */
    @Override
    public List<LiveTranscodeConfigVo> queryList(LiveTranscodeConfigBo bo) {
        LambdaQueryWrapper<LiveTranscodeConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveTranscodeConfig> buildQueryWrapper(LiveTranscodeConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveTranscodeConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveTranscodeConfig::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getKey()), LiveTranscodeConfig::getKey, bo.getKey());
        lqw.eq(StringUtils.isNotBlank(bo.getValue()), LiveTranscodeConfig::getValue, bo.getValue());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), LiveTranscodeConfig::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), LiveTranscodeConfig::getDescription, bo.getDescription());
        return lqw;
    }

    /**
     * 新增转码配置
     *
     * @param bo 转码配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveTranscodeConfigBo bo) {
        LiveTranscodeConfig add = MapstructUtils.convert(bo, LiveTranscodeConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改转码配置
     *
     * @param bo 转码配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveTranscodeConfigBo bo) {
        LiveTranscodeConfig update = MapstructUtils.convert(bo, LiveTranscodeConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveTranscodeConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除转码配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
