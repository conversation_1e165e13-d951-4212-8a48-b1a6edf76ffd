package com.ydwl.live.enums;

import lombok.Getter;

/**
 * 枚举类，表示直播状态  0-未开始，1-直播中，2-已结束，3-回放中，4-异常
 */
@Getter
public enum LiveStatusEnum {
    /**
     * 未开始
     */
    NOT_STARTED(0L, "未开始"),
    /**
     * 直播正在进行中
     */
    LIVE(1L, "直播中"),
    /**
     * 直播已结束
     */
    ENDED(2L, "已结束"),
    /**
     * 回放中
     */
    REPLAY(3L, "回放中"),
    /**
     * 异常状态
     */
    ERROR(4L, "异常");

    private final Long value;
    private final String description;

    LiveStatusEnum(Long value, String description) {
        this.value = value;
        this.description = description;
    }

    public Long getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据值获取枚举
     */
    public static LiveStatusEnum fromValue(Long value) {
        for (LiveStatusEnum status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的直播状态值: " + value);
    }
}
