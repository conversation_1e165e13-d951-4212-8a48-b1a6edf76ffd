package com.ydwl.live.enums;

import lombok.Getter;

/**
 * 枚举类，表示直播状态  0-计划中，1-直播中，2-已结束，3-已下线
 */
@Getter
public enum LiveStatusEnum {
    /**
     * 直播已计划
     */
    SCHEDULED(0L),
    /**
     * 直播正在进行中
     */
    LIVE(1L),
    /**
     * 直播已结束
     */
    ENDED(2L),
    /**
     * 直播下线
     */
    OFFLINE(3L),
    /**
     * 回放准备
     */
    REPLAY_PREPARED(4L);

    private final Long value;

    LiveStatusEnum(Long value) {
        this.value = value;
    }

    public Long getValue() {
        return value;
    }
}
