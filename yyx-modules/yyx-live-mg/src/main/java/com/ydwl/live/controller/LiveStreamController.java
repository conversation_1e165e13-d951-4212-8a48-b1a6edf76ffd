package com.ydwl.live.controller;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.live.domain.vo.GetStreamVo;
import com.ydwl.live.domain.vo.LiveStreamVo;
import com.ydwl.live.callback.dto.InsertOrUpdateLiveStreamDTO;
import com.ydwl.live.service.ILiveStreamService;

/**
 * 推流信息
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/stream")
public class LiveStreamController extends BaseController {

    private final ILiveStreamService liveStreamService;



    /**
     * 获取推流信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:stream:query")
    @GetMapping("/{id}")
    public R<GetStreamVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveStreamService.queryByLiveId(id));
    }

    /**
     * 修改推流信息
     */
    @Log(title = "推流信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InsertOrUpdateLiveStreamDTO bo) {
        Boolean insertOrUpdateLiveStreamDTO = liveStreamService.InsertOrUpdateLiveStreamDTO(bo);
        if (insertOrUpdateLiveStreamDTO) {
            return R.ok();
        } else {
            return R.fail("修改失败");
        }
    }

}
