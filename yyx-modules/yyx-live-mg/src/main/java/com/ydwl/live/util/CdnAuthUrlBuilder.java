package com.ydwl.live.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class CdnAuthUrlBuilder {

    /**
     * 生成 CDN 鉴权方式 A 的 auth_key
     * 格式：path + "-" + timestamp + "-" + rand + "-" + uid + "-" + private_key
     *
     * @param filePath 请求路径（不含域名）
     * @param expireTime 过期时间（秒）
     * @param secretKey CDN 鉴权密钥
     * @return 生成的 auth_key
     */
    public static String generateAuthKey(String filePath, long expireTime, String secretKey) throws Exception {
        // 构建待签名字符串，格式：path-timestamp-0-0-secretKey
        String stringToSign = String.format("%s-%d-0-0-%s", filePath, expireTime, secretKey);
        
        // 使用 MD5 算法计算哈希
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hash = md.digest(stringToSign.getBytes(StandardCharsets.UTF_8));
        
        // 将 hash 转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        // 返回最终的 auth_key，格式：timestamp-0-0-md5hash
        return String.format("%d-0-0-%s", expireTime, hexString.toString());
    }
}
