package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ydwl.common.core.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveCategoryBo;
import com.ydwl.live.domain.vo.LiveCategoryVo;
import com.ydwl.live.domain.LiveCategory;
import com.ydwl.live.mapper.LiveCategoryMapper;
import com.ydwl.live.service.ILiveCategoryService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.Objects;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

/**
 * 直播分类Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveCategoryServiceImpl implements ILiveCategoryService {

    private final LiveCategoryMapper baseMapper;

    /**
     * 查询直播分类
     *
     * @param id 主键
     * @return 直播分类
     */
    @Override
    public LiveCategoryVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询符合条件的直播分类列表
     *
     * @param bo 查询条件
     * @return 直播分类列表
     */
    @Override
    public List<LiveCategoryVo> queryList(LiveCategoryBo bo) {
        LambdaQueryWrapper<LiveCategory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveCategory> buildQueryWrapper(LiveCategoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveCategory> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveCategory::getId);
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), LiveCategory::getCategoryName, bo.getCategoryName());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryCode()), LiveCategory::getCategoryCode, bo.getCategoryCode());
        lqw.eq(bo.getParentId() != null, LiveCategory::getParentId, bo.getParentId());
        lqw.eq(StringUtils.isNotBlank(bo.getPath()), LiveCategory::getPath, bo.getPath());
        lqw.eq(bo.getTreeSort() != null, LiveCategory::getTreeSort, bo.getTreeSort());
        lqw.eq(bo.getTreeLevel() != null, LiveCategory::getTreeLevel, bo.getTreeLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getIconUrl()), LiveCategory::getIconUrl, bo.getIconUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverImgUrl()), LiveCategory::getCoverImgUrl, bo.getCoverImgUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), LiveCategory::getDescription, bo.getDescription());
        lqw.eq(bo.getStatus() != null, LiveCategory::getStatus, bo.getStatus());
        lqw.eq(bo.getLiveCount() != null, LiveCategory::getLiveCount, bo.getLiveCount());
        lqw.eq(bo.getViewCount() != null, LiveCategory::getViewCount, bo.getViewCount());
        return lqw;
    }

    /**
     * 新增直播分类
     *
     * @param bo 直播分类
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(LiveCategoryBo bo) {
        LiveCategory add = MapstructUtils.convert(bo, LiveCategory.class);
        validEntityBeforeSave(add);
        
        // 自动设置树形结构相关字段
        setTreeInfo(add);
        
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改直播分类
     *
     * @param bo 直播分类
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(LiveCategoryBo bo) {
        LiveCategory update = MapstructUtils.convert(bo, LiveCategory.class);
        validEntityBeforeSave(update);
        
        // 自动设置树形结构相关字段
        setTreeInfo(update);
        
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveCategory entity){
        // 1. 分类名称在同一父分类下唯一性校验
        LambdaQueryWrapper<LiveCategory> nameQueryWrapper = Wrappers.lambdaQuery();
        nameQueryWrapper.eq(LiveCategory::getCategoryName, entity.getCategoryName())
                 .eq(LiveCategory::getParentId, entity.getParentId() != null ? entity.getParentId() : 0L);
        
        // 如果是修改操作，排除自身ID
        if (entity.getId() != null) {
            nameQueryWrapper.ne(LiveCategory::getId, entity.getId());
        }
        
        long nameCount = baseMapper.selectCount(nameQueryWrapper);
        if (nameCount > 0) {
            throw new RuntimeException("同一父分类下已存在相同名称的分类");
        }
        
        // 2. 分类编码唯一性校验（如果有值）
        if (StringUtils.isNotBlank(entity.getCategoryCode())) {
            LambdaQueryWrapper<LiveCategory> codeQueryWrapper = Wrappers.lambdaQuery();
            codeQueryWrapper.eq(LiveCategory::getCategoryCode, entity.getCategoryCode());
            
            // 如果是修改操作，排除自身ID
            if (entity.getId() != null) {
                codeQueryWrapper.ne(LiveCategory::getId, entity.getId());
            }
            
            long codeCount = baseMapper.selectCount(codeQueryWrapper);
            if (codeCount > 0) {
                throw new RuntimeException("分类编码已存在，请使用其他编码");
            }
        }
        
        // 3. 父分类存在性校验（如果不是顶级分类）
        if (entity.getParentId() != null && entity.getParentId() > 0) {
            LiveCategory parentCategory = baseMapper.selectById(entity.getParentId());
            if (parentCategory == null) {
                throw new RuntimeException("父分类不存在");
            }
            
            // 4. 检查状态一致性 - 如果父分类已禁用，子分类不能启用
            if (parentCategory.getStatus() != null && parentCategory.getStatus() == 0 
                && entity.getStatus() != null && entity.getStatus() == 1) {
                throw new RuntimeException("父分类已禁用，子分类不能启用");
            }
        }
    }

    /**
     * 设置树形结构相关信息
     */
    private void setTreeInfo(LiveCategory entity) {
        // 如果是顶级分类
        if (entity.getParentId() == null || entity.getParentId() == 0) {
            entity.setParentId(0L);
            entity.setTreeLevel(1L);
            
            // 如果是新增操作，设置path
            if (entity.getId() == null) {
                // path会在插入后更新
                entity.setPath("");
            } else {
                entity.setPath(entity.getId().toString());
            }
        } else {
            // 获取父分类信息
            LiveCategory parentCategory = baseMapper.selectById(entity.getParentId());
            if (parentCategory != null) {
                entity.setTreeLevel(parentCategory.getTreeLevel() + 1);
                
                // 如果是已有分类，设置完整path
                if (entity.getId() != null) {
                    entity.setPath(parentCategory.getPath() + "," + entity.getId());
                }
            }
        }
        
        // 如果未设置排序值，设置默认值
        if (entity.getTreeSort() == null) {
            entity.setTreeSort(0L);
        }
    }

    /**
     * 校验并批量删除直播分类信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 1. 检查是否有子分类
            LambdaQueryWrapper<LiveCategory> childQueryWrapper = Wrappers.lambdaQuery();
            childQueryWrapper.in(LiveCategory::getParentId, ids);
            long childCount = baseMapper.selectCount(childQueryWrapper);
            if (childCount > 0) {
                throw new RuntimeException("无法删除，存在子分类");
            }
            
            // 2. 检查是否有直播使用该分类
            // TODO: 此处需要查询直播表，检查是否有直播使用该分类
            // 示例代码：
            // LambdaQueryWrapper<Live> liveQueryWrapper = Wrappers.lambdaQuery();
            // liveQueryWrapper.in(Live::getCategoryId, ids);
            // long liveCount = liveMapper.selectCount(liveQueryWrapper);
            // if (liveCount > 0) {
            //     throw new RuntimeException("无法删除，该分类下存在直播内容");
            // }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取分类树形结构
     * 
     * @param status 分类状态(可选，null表示查询所有状态)
     * @return 分类树形结构
     */
    @Override
    public List<Map<String, Object>> getCategoryTree(Long status) {
        // 1. 查询所有满足条件的分类
        LambdaQueryWrapper<LiveCategory> queryWrapper = Wrappers.lambdaQuery();
        if (status != null) {
            queryWrapper.eq(LiveCategory::getStatus, status);
        }
        queryWrapper.orderByAsc(LiveCategory::getTreeSort);
        
        List<LiveCategoryVo> allCategories = baseMapper.selectVoList(queryWrapper);
        
        // 2. 构建树形结构
        List<Map<String, Object>> result = new ArrayList<>();
        Map<Long, Map<String, Object>> nodeMap = new HashMap<>();
        
        // 先将所有节点转换为Map格式并存入nodeMap
        for (LiveCategoryVo category : allCategories) {
            Map<String, Object> node = new HashMap<>();
            node.put("id", category.getId());
            node.put("label", category.getCategoryName());
            node.put("parentId", category.getParentId());
            node.put("sort", category.getTreeSort());
            node.put("level", category.getTreeLevel());
            node.put("path", category.getPath());
            node.put("status", category.getStatus());
            node.put("iconUrl", category.getIconUrl());
            node.put("coverImgUrl", category.getCoverImgUrl());
            node.put("description", category.getDescription());
            node.put("liveCount", category.getLiveCount());
            node.put("viewCount", category.getViewCount());
            node.put("children", new ArrayList<>());
            
            nodeMap.put(category.getId(), node);
        }
        
        // 构建树形结构
        for (Map<String, Object> node : nodeMap.values()) {
            Long parentId = (Long) node.get("parentId");
            
            // 顶级节点直接添加到结果集
            if (parentId == null || parentId == 0) {
                result.add(node);
            } else {
                // 子节点添加到父节点的children集合
                Map<String, Object> parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    List<Map<String, Object>> children = (List<Map<String, Object>>) parentNode.get("children");
                    children.add(node);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 按层级查询分类
     * 
     * @param level 层级(从1开始)
     * @param status 分类状态(可选，null表示查询所有状态)
     * @return 指定层级的分类列表
     */
    @Override
    public List<LiveCategoryVo> getByLevel(Long level, Long status) {
        if (level == null || level < 1) {
            throw new IllegalArgumentException("分类层级必须大于等于1");
        }
        
        LambdaQueryWrapper<LiveCategory> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LiveCategory::getTreeLevel, level);
        
        if (status != null) {
            queryWrapper.eq(LiveCategory::getStatus, status);
        }
        
        queryWrapper.orderByAsc(LiveCategory::getTreeSort);
        return baseMapper.selectVoList(queryWrapper);
    }
    
    /**
     * 获取子分类列表
     * 
     * @param parentId 父分类ID
     * @param status 分类状态(可选，null表示查询所有状态)
     * @return 子分类列表
     */
    @Override
    public List<LiveCategoryVo> getChildren(Long parentId, Long status) {
        if (parentId == null) {
            parentId = 0L;
        }
        
        LambdaQueryWrapper<LiveCategory> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LiveCategory::getParentId, parentId);
        
        if (status != null) {
            queryWrapper.eq(LiveCategory::getStatus, status);
        }
        
        queryWrapper.orderByAsc(LiveCategory::getTreeSort);
        return baseMapper.selectVoList(queryWrapper);
    }
    
    /**
     * 增加分类浏览次数
     * 
     * @param categoryId 分类ID
     * @return 是否增加成功
     */
    @Override
    public Boolean incrementViewCount(Long categoryId) {
        if (categoryId == null) {
            return false;
        }
        
        LambdaUpdateWrapper<LiveCategory> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(LiveCategory::getId, categoryId);
        updateWrapper.setSql("view_count = view_count + 1");
        
        return baseMapper.update(null, updateWrapper) > 0;
    }
    
    /**
     * 获取热门分类列表
     * 
     * @param limit 限制数量
     * @return 热门分类列表
     */
    @Override
    public List<LiveCategoryVo> getHotCategories(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10; // 默认返回10条
        }
        
        // 使用Mapper中的专用方法获取热门分类
        return baseMapper.selectHotCategories(limit);
    }
    
    /**
     * 更新分类的path和增加直播数量
     * 
     * @param categoryId 分类ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePathAfterInsert(Long categoryId) {
        if (categoryId == null) {
            return;
        }
        
        LiveCategory category = baseMapper.selectById(categoryId);
        if (category == null) {
            return;
        }
        
        // 处理顶级分类
        if (category.getParentId() == null || category.getParentId() == 0) {
            category.setPath(categoryId.toString());
            baseMapper.updateById(category);
            return;
        }
        
        // 处理子分类
        LiveCategory parentCategory = baseMapper.selectById(category.getParentId());
        if (parentCategory != null) {
            category.setPath(parentCategory.getPath() + "," + categoryId);
            baseMapper.updateById(category);
        }
    }
    
    /**
     * 递归增加父分类的直播计数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementLiveCountForPath(String path) {
        if (StringUtils.isBlank(path)) {
            return;
        }
        
        String[] ids = path.split(",");
        for (String idStr : ids) {
            Long id = Long.parseLong(idStr);
            
            LambdaUpdateWrapper<LiveCategory> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(LiveCategory::getId, id);
            updateWrapper.setSql("live_count = live_count + 1");
            
            baseMapper.update(null, updateWrapper);
        }
    }
    
    /**
     * 递减父分类的直播计数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decrementLiveCountForPath(String path) {
        if (StringUtils.isBlank(path)) {
            return;
        }
        
        String[] ids = path.split(",");
        for (String idStr : ids) {
            Long id = Long.parseLong(idStr);
            
            LambdaUpdateWrapper<LiveCategory> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(LiveCategory::getId, id);
            updateWrapper.setSql("live_count = CASE WHEN live_count > 0 THEN live_count - 1 ELSE 0 END");
            
            baseMapper.update(null, updateWrapper);
        }
    }
}
