package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveTranscodeTemplate;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 视频转码模板业务对象 live_transcode_template
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveTranscodeTemplate.class, reverseConvertGenerate = false)
public class LiveTranscodeTemplateBo extends BaseEntity {

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String templateName;

    /**
     * 转码类型(fc-函数计算,mts-媒体转码服务)
     */
    @NotBlank(message = "转码类型(fc-函数计算,mts-媒体转码服务)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String templateType;

    /**
     * 清晰度标识(SD-标清,HD-高清,FHD-超清)
     */
    @NotBlank(message = "清晰度标识(SD-标清,HD-高清,FHD-超清)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String definition;

    /**
     * 分辨率(如:1920x1080)
     */
    @NotBlank(message = "分辨率(如:1920x1080)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String resolution;

    /**
     * 视频码率(kbps)
     */
    @NotNull(message = "视频码率(kbps)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long videoBitrate;

    /**
     * 音频码率(kbps)
     */
    private Long audioBitrate;

    /**
     * 帧率
     */
    private Long fps;

    /**
     * 输出格式
     */
    private String format;

    /**
     * 编码方式
     */
    private String codec;

    /**
     * 外部系统模板ID(阿里云模板ID)
     */
    private String externalId;

    /**
     * 状态(0-禁用,1-启用)
     */
    private Long status;

    /**
     * 是否默认(0-否,1-是)
     */
    private Long isDefault;


}
