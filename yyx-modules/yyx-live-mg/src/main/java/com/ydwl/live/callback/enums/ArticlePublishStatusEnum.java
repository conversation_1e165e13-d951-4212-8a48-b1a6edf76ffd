package com.ydwl.live.callback.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章发布状态枚举
 */
@Getter
@AllArgsConstructor
public enum ArticlePublishStatusEnum {

    DRAFT(0L, "草稿"),
    PUBLISHED(1L, "已发布"),
    ARCHIVED(2L, "已归档");

    private final Long code;
    private final String desc;

    public static ArticlePublishStatusEnum getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (ArticlePublishStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
