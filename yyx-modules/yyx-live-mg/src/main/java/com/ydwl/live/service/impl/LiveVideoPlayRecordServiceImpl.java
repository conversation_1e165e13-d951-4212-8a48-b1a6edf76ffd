package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveVideoPlayRecordBo;
import com.ydwl.live.domain.vo.LiveVideoPlayRecordVo;
import com.ydwl.live.domain.LiveVideoPlayRecord;
import com.ydwl.live.mapper.LiveVideoPlayRecordMapper;
import com.ydwl.live.service.ILiveVideoPlayRecordService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 视频播放记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveVideoPlayRecordServiceImpl implements ILiveVideoPlayRecordService {

    private final LiveVideoPlayRecordMapper baseMapper;

    /**
     * 查询视频播放记录
     *
     * @param id 主键
     * @return 视频播放记录
     */
    @Override
    public LiveVideoPlayRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询视频播放记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频播放记录分页列表
     */
    @Override
    public TableDataInfo<LiveVideoPlayRecordVo> queryPageList(LiveVideoPlayRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveVideoPlayRecord> lqw = buildQueryWrapper(bo);
        Page<LiveVideoPlayRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的视频播放记录列表
     *
     * @param bo 查询条件
     * @return 视频播放记录列表
     */
    @Override
    public List<LiveVideoPlayRecordVo> queryList(LiveVideoPlayRecordBo bo) {
        LambdaQueryWrapper<LiveVideoPlayRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveVideoPlayRecord> buildQueryWrapper(LiveVideoPlayRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveVideoPlayRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveVideoPlayRecord::getId);
        lqw.eq(bo.getLiveId() != null, LiveVideoPlayRecord::getLiveId, bo.getLiveId());
        lqw.eq(bo.getUserId() != null, LiveVideoPlayRecord::getUserId, bo.getUserId());
        lqw.eq(bo.getPlayTime() != null, LiveVideoPlayRecord::getPlayTime, bo.getPlayTime());
        lqw.eq(bo.getPlayDurationSeconds() != null, LiveVideoPlayRecord::getPlayDurationSeconds, bo.getPlayDurationSeconds());
        lqw.eq(bo.getLastPositionSeconds() != null, LiveVideoPlayRecord::getLastPositionSeconds, bo.getLastPositionSeconds());
        lqw.eq(bo.getIsFinished() != null, LiveVideoPlayRecord::getIsFinished, bo.getIsFinished());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceType()), LiveVideoPlayRecord::getDeviceType, bo.getDeviceType());
        lqw.eq(StringUtils.isNotBlank(bo.getIpAddress()), LiveVideoPlayRecord::getIpAddress, bo.getIpAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getVideoQuality()), LiveVideoPlayRecord::getVideoQuality, bo.getVideoQuality());
        return lqw;
    }

    /**
     * 新增视频播放记录
     *
     * @param bo 视频播放记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveVideoPlayRecordBo bo) {
        LiveVideoPlayRecord add = MapstructUtils.convert(bo, LiveVideoPlayRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改视频播放记录
     *
     * @param bo 视频播放记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveVideoPlayRecordBo bo) {
        LiveVideoPlayRecord update = MapstructUtils.convert(bo, LiveVideoPlayRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveVideoPlayRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除视频播放记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
