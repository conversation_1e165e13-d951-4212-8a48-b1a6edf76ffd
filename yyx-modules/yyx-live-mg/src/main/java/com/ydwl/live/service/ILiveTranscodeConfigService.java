package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveTranscodeConfigVo;
import com.ydwl.live.domain.bo.LiveTranscodeConfigBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 转码配置Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveTranscodeConfigService {

    /**
     * 查询转码配置
     *
     * @param id 主键
     * @return 转码配置
     */
    LiveTranscodeConfigVo queryById(Long id);

    /**
     * 分页查询转码配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转码配置分页列表
     */
    TableDataInfo<LiveTranscodeConfigVo> queryPageList(LiveTranscodeConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的转码配置列表
     *
     * @param bo 查询条件
     * @return 转码配置列表
     */
    List<LiveTranscodeConfigVo> queryList(LiveTranscodeConfigBo bo);

    /**
     * 新增转码配置
     *
     * @param bo 转码配置
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveTranscodeConfigBo bo);

    /**
     * 修改转码配置
     *
     * @param bo 转码配置
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveTranscodeConfigBo bo);

    /**
     * 校验并批量删除转码配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
