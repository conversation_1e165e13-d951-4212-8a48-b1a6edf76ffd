package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveUserWalletBo;
import com.ydwl.live.domain.vo.LiveUserWalletVo;
import com.ydwl.live.domain.LiveUserWallet;
import com.ydwl.live.mapper.LiveUserWalletMapper;
import com.ydwl.live.service.ILiveUserWalletService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 用户钱包Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveUserWalletServiceImpl implements ILiveUserWalletService {

    private final LiveUserWalletMapper baseMapper;

    /**
     * 查询用户钱包
     *
     * @param id 主键
     * @return 用户钱包
     */
    @Override
    public LiveUserWalletVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户钱包列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户钱包分页列表
     */
    @Override
    public TableDataInfo<LiveUserWalletVo> queryPageList(LiveUserWalletBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveUserWallet> lqw = buildQueryWrapper(bo);
        Page<LiveUserWalletVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户钱包列表
     *
     * @param bo 查询条件
     * @return 用户钱包列表
     */
    @Override
    public List<LiveUserWalletVo> queryList(LiveUserWalletBo bo) {
        LambdaQueryWrapper<LiveUserWallet> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveUserWallet> buildQueryWrapper(LiveUserWalletBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveUserWallet> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveUserWallet::getId);
        lqw.eq(bo.getUserId() != null, LiveUserWallet::getUserId, bo.getUserId());
        lqw.eq(bo.getBalance() != null, LiveUserWallet::getBalance, bo.getBalance());
        lqw.eq(bo.getTotalRecharge() != null, LiveUserWallet::getTotalRecharge, bo.getTotalRecharge());
        lqw.eq(bo.getTotalConsume() != null, LiveUserWallet::getTotalConsume, bo.getTotalConsume());
        return lqw;
    }

    /**
     * 新增用户钱包
     *
     * @param bo 用户钱包
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveUserWalletBo bo) {
        LiveUserWallet add = MapstructUtils.convert(bo, LiveUserWallet.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户钱包
     *
     * @param bo 用户钱包
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveUserWalletBo bo) {
        LiveUserWallet update = MapstructUtils.convert(bo, LiveUserWallet.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveUserWallet entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户钱包信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
