package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveUserSubscribeVo;
import com.ydwl.live.domain.bo.LiveUserSubscribeBo;
import com.ydwl.live.service.ILiveUserSubscribeService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 用户订阅
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/userSubscribe")
public class LiveUserSubscribeController extends BaseController {

    private final ILiveUserSubscribeService liveUserSubscribeService;

    /**
     * 查询用户订阅列表
     */
    @SaCheckPermission("live:userSubscribe:list")
    @GetMapping("/list")
    public TableDataInfo<LiveUserSubscribeVo> list(LiveUserSubscribeBo bo, PageQuery pageQuery) {
        return liveUserSubscribeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户订阅列表
     */
    @SaCheckPermission("live:userSubscribe:export")
    @Log(title = "用户订阅", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveUserSubscribeBo bo, HttpServletResponse response) {
        List<LiveUserSubscribeVo> list = liveUserSubscribeService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户订阅", LiveUserSubscribeVo.class, response);
    }

    /**
     * 获取用户订阅详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:userSubscribe:query")
    @GetMapping("/{id}")
    public R<LiveUserSubscribeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveUserSubscribeService.queryById(id));
    }

    /**
     * 新增用户订阅
     */
    @SaCheckPermission("live:userSubscribe:add")
    @Log(title = "用户订阅", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveUserSubscribeBo bo) {
        return toAjax(liveUserSubscribeService.insertByBo(bo));
    }

    /**
     * 修改用户订阅
     */
    @SaCheckPermission("live:userSubscribe:edit")
    @Log(title = "用户订阅", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveUserSubscribeBo bo) {
        return toAjax(liveUserSubscribeService.updateByBo(bo));
    }

    /**
     * 删除用户订阅
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:userSubscribe:remove")
    @Log(title = "用户订阅", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveUserSubscribeService.deleteWithValidByIds(List.of(ids), true));
    }
}
