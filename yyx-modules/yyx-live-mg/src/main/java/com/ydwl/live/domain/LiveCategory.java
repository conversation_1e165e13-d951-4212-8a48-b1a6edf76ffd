package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 直播分类对象 live_category
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_category")
public class LiveCategory extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类编码(唯一标识)
     */
    private String categoryCode;

    /**
     * 父分类ID(0表示顶级分类)
     */
    private Long parentId;

    /**
     * 分类路径(格式:1,2,3)
     */
    private String path;

    /**
     * 树排序序号
     */
    private Long treeSort;

    /**
     * 树层级(从1开始)
     */
    private Long treeLevel;

    /**
     * 分类图标URL
     */
    private String iconUrl;

    /**
     * 分类封面图URL
     */
    private String coverImgUrl;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 状态(0-禁用,1-启用)
     */
    private Long status;

    /**
     * 该分类下直播数量
     */
    private Long liveCount;

    /**
     * 分类浏览次数
     */
    private Long viewCount;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
