package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveArticleBo;
import com.ydwl.live.domain.vo.LiveArticleVo;
import com.ydwl.live.domain.LiveArticle;
import com.ydwl.live.mapper.LiveArticleMapper;
import com.ydwl.live.service.ILiveArticleService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 直播文章Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveArticleServiceImpl implements ILiveArticleService {

    private final LiveArticleMapper baseMapper;

    /**
     * 查询直播文章
     *
     * @param id 主键
     * @return 直播文章
     */
    @Override
    public LiveArticleVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询直播文章列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播文章分页列表
     */
    @Override
    public TableDataInfo<LiveArticleVo> queryPageList(LiveArticleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveArticle> lqw = buildQueryWrapper(bo);
        Page<LiveArticleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的直播文章列表
     *
     * @param bo 查询条件
     * @return 直播文章列表
     */
    @Override
    public List<LiveArticleVo> queryList(LiveArticleBo bo) {
        LambdaQueryWrapper<LiveArticle> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveArticle> buildQueryWrapper(LiveArticleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveArticle> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveArticle::getId);
        lqw.eq(bo.getRelatedId() != null, LiveArticle::getRelatedId, bo.getRelatedId());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), LiveArticle::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), LiveArticle::getContent, bo.getContent());
        lqw.eq(bo.getPublishStatus() != null, LiveArticle::getPublishStatus, bo.getPublishStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getRelatedType()), LiveArticle::getRelatedType, bo.getRelatedType());
        lqw.eq(StringUtils.isNotBlank(bo.getContentFormat()), LiveArticle::getContentFormat, bo.getContentFormat());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverImgUrl()), LiveArticle::getCoverImgUrl, bo.getCoverImgUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getSummary()), LiveArticle::getSummary, bo.getSummary());
        lqw.eq(bo.getLastEditTime() != null, LiveArticle::getLastEditTime, bo.getLastEditTime());
        lqw.eq(bo.getIsFeatured() != null, LiveArticle::getIsFeatured, bo.getIsFeatured());
        lqw.eq(bo.getIsCommentAllowed() != null, LiveArticle::getIsCommentAllowed, bo.getIsCommentAllowed());
        lqw.eq(bo.getViewCount() != null, LiveArticle::getViewCount, bo.getViewCount());
        lqw.eq(bo.getLikeCount() != null, LiveArticle::getLikeCount, bo.getLikeCount());
        lqw.eq(bo.getShareCount() != null, LiveArticle::getShareCount, bo.getShareCount());
        return lqw;
    }

    /**
     * 新增直播文章
     *
     * @param bo 直播文章
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveArticleBo bo) {
        LiveArticle add = MapstructUtils.convert(bo, LiveArticle.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改直播文章
     *
     * @param bo 直播文章
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveArticleBo bo) {
        LiveArticle update = MapstructUtils.convert(bo, LiveArticle.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveArticle entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除直播文章信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
