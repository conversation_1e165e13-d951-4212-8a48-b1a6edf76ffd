package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveTranscodeOutputVo;
import com.ydwl.live.domain.bo.LiveTranscodeOutputBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 转码输出文件Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveTranscodeOutputService {

    /**
     * 查询转码输出文件
     *
     * @param id 主键
     * @return 转码输出文件
     */
    LiveTranscodeOutputVo queryById(Long id);

    /**
     * 分页查询转码输出文件列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转码输出文件分页列表
     */
    TableDataInfo<LiveTranscodeOutputVo> queryPageList(LiveTranscodeOutputBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的转码输出文件列表
     *
     * @param bo 查询条件
     * @return 转码输出文件列表
     */
    List<LiveTranscodeOutputVo> queryList(LiveTranscodeOutputBo bo);

    /**
     * 根据任务ID和分辨率以及模板ID查询转码输出记录
     * 
     * @param taskId 任务ID
     * @param resolution 分辨率
     * @param templateId 模板ID
     * @return 转码输出记录
     */
    LiveTranscodeOutputVo queryByTaskIdAndDefinition(Long taskId, String resolution, String templateId);

    /**
     * 新增转码输出文件
     *
     * @param bo 转码输出文件
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveTranscodeOutputBo bo);

    /**
     * 修改转码输出文件
     *
     * @param bo 转码输出文件
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveTranscodeOutputBo bo);

    /**
     * 校验并批量删除转码输出文件信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
