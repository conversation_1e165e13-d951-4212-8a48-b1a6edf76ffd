package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveBo;
import com.ydwl.live.domain.vo.LiveVo;
import com.ydwl.live.domain.vo.GetStreamVo;
import com.ydwl.live.domain.Live;
import com.ydwl.live.mapper.LiveMapper;
import com.ydwl.live.service.ILiveService;
import com.ydwl.live.mapper.LiveCategoryMapper;
import org.springframework.transaction.annotation.Transactional;
import com.ydwl.live.domain.LiveCategory;
import com.ydwl.live.service.ILiveCategoryService;
import com.ydwl.live.service.ILiveReplayService;
import com.ydwl.live.service.ILiveStreamService;
import com.ydwl.live.domain.bo.LiveReplayBo;
import com.ydwl.live.domain.vo.LiveReplayVo;
import com.ydwl.live.service.model.LiveStreamInfo;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 直播信息Service业务层处理
 * 
 * <p>提供直播信息的完整业务逻辑处理，包括：</p>
 * <ul>
 *   <li>直播信息的增删改查操作</li>
 *   <li>复杂查询条件的构建和处理</li>
 *   <li>分页查询支持</li>
 *   <li>数据校验和业务规则检查</li>
 * </ul>
 * 
 * <p><strong>设计特点：</strong></p>
 * <ul>
 *   <li>使用Lambda表达式构建类型安全的查询条件</li>
 *   <li>支持灵活的分页和排序</li>
 *   <li>通过MapStruct实现BO与Entity的高效转换</li>
 *   <li>提供数据校验和业务规则检查扩展点</li>
 * </ul>
 * 
 * <AUTHOR> Yi
 * @version 2.1.4
 * @since 2025-05-21
 * @see ILiveService 直播服务接口
 * @see LiveMapper 直播数据访问层
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LiveServiceImpl implements ILiveService {

    /**
     * 直播信息数据访问对象
     * <p>提供对直播信息表的基础CRUD操作和自定义查询</p>
     */
    private final LiveMapper baseMapper;

    /**
     * 直播分类数据访问对象
     * <p>提供对直播分类表的基础CRUD操作和自定义查询</p>
     */
    private final LiveCategoryMapper liveCategoryMapper;

    /**
     * 直播分类服务对象
     * <p>提供对直播分类的业务逻辑处理</p>
     */
    private final ILiveCategoryService liveCategoryService;
    
    /**
     * 直播推流服务
     * <p>提供直播推流信息的业务逻辑处理</p>
     */
    private final ILiveStreamService liveStreamService;
    
    /**
     * 直播回放服务
     * <p>提供直播回放信息的业务逻辑处理</p>
     */
    private final ILiveReplayService liveReplayService;

    /**
     * 根据主键查询直播信息
     * 
     * <p>通过直播ID获取完整的直播信息，包含所有业务字段</p>
     *
     * @param id 直播主键ID，不能为null
     * @return 直播信息视图对象，如果不存在则返回null
     * @throws IllegalArgumentException 当id为null时抛出
     * 
     * @apiNote 
     * <ul>
     *   <li>返回的是视图对象(VO)，适合前端展示</li>
     *   <li>自动处理逻辑删除的数据过滤</li>
     *   <li>支持多租户数据隔离</li>
     * </ul>
     */
    @Override
    public LiveVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询直播信息列表
     * 
     * <p>根据查询条件进行分页查询，支持多种过滤条件和排序方式</p>
     *
     * @param bo 查询条件业务对象，包含各种过滤条件
     * @param pageQuery 分页参数对象，包含页码、页大小、排序等信息
     * @return 分页结果对象，包含数据列表和分页信息
     * @throws IllegalArgumentException 当pageQuery为null时抛出
     * 
     * @apiNote
     * <ul>
     *   <li>支持动态查询条件，null值会被自动忽略</li>
     *   <li>默认按创建时间倒序排列</li>
     *   <li>返回结果包含总数、当前页数据等完整分页信息</li>
     *   <li>自动处理多租户和逻辑删除过滤</li>
     * </ul>
     * 
     * @since 2.1.0
     */
    @Override
    public TableDataInfo<LiveVo> queryPageList(LiveBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Live> lqw = buildQueryWrapper(bo);
        Page<LiveVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的直播信息列表
     * 
     * <p>根据查询条件获取所有匹配的直播信息，不进行分页</p>
     *
     * @param bo 查询条件业务对象，可以为null表示查询所有
     * @return 直播信息列表，可能为空列表但不会为null
     * 
     * @apiNote
     * <ul>
     *   <li>不进行分页，一次性返回所有匹配数据</li>
     *   <li>适用于数据量较小的场景或导出功能</li>
     *   <li>大数据量场景建议使用分页查询</li>
     * </ul>
     * 
     * @see #queryPageList(LiveBo, PageQuery) 分页查询方法
     */
    @Override
    public List<LiveVo> queryList(LiveBo bo) {
        LambdaQueryWrapper<Live> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构建动态查询条件
     * 
     * <p>根据业务对象中的非空字段动态构建Lambda查询条件</p>
     *
     * @param bo 查询条件业务对象，可以为null
     * @return Lambda查询包装器对象
     * 
     * @implNote
     * <ul>
     *   <li>使用Lambda表达式确保类型安全</li>
     *   <li>自动忽略null值，避免无效查询条件</li>
     *   <li>支持字符串模糊匹配和精确匹配</li>
     *   <li>默认按ID升序排列</li>
     * </ul>
     */
    private LambdaQueryWrapper<Live> buildQueryWrapper(LiveBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Live> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCategoryId() != null, Live::getCategoryId, bo.getCategoryId());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), Live::getTitle, bo.getTitle());
        lqw.eq(bo.getStatus() != null, Live::getStatus, bo.getStatus());
        lqw.eq(bo.getStreamId() != null, Live::getStreamId, bo.getStreamId());
        return lqw;
    }

    /**
     * 新增直播信息
     * 
     * <p>创建新的直播信息记录，包含数据校验和业务规则检查</p>
     *
     * @param bo 要新增的直播信息业务对象，不能为null
     * @return 是否新增成功，true表示成功，false表示失败
     * @throws IllegalArgumentException 当bo为null或必填字段为空时抛出
     * 
     * @apiNote
     * <ul>
     *   <li>新增成功后，bo对象的ID字段会被设置为生成的主键值</li>
     *   <li>会自动设置创建时间、创建人等审计字段</li>
     *   <li>执行数据校验，确保业务规则正确性</li>
     *   <li>支持事务回滚，失败时不会产生脏数据</li>
     * </ul>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(LiveBo bo) {
        Live add = MapstructUtils.convert(bo, Live.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改直播信息
     * 
     * <p>根据ID更新直播信息，包含数据校验和业务规则检查</p>
     *
     * @param bo 要修改的直播信息业务对象，必须包含有效的ID
     * @return 是否修改成功，true表示成功，false表示失败或记录不存在
     * @throws IllegalArgumentException 当bo为null或ID为空时抛出
     * 
     * @apiNote
     * <ul>
     *   <li>只更新非空字段，空值字段保持原值</li>
     *   <li>会自动设置更新时间、更新人等审计字段</li>
     *   <li>执行数据校验，确保修改后数据的正确性</li>
     *   <li>支持乐观锁，避免并发修改冲突</li>
     * </ul>
     */
    @Override
    public Boolean updateByBo(LiveBo bo) {
        Live update = MapstructUtils.convert(bo, Live.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     * 
     * <p>在新增或修改数据前进行业务规则校验和数据完整性检查</p>
     *
     * @param entity 要校验的实体对象
     * @throws IllegalArgumentException 当数据不符合业务规则时抛出
     * 
     * @implNote
     * <p>当前实现为空，具体的校验规则包括：</p>
     * <ul>
     *   <li>唯一性约束检查</li>
     *   <li>业务规则验证</li>
     *   <li>数据格式校验</li>
     *   <li>关联数据完整性检查</li>
     * </ul>
     * 
     * @future 后续版本将实现具体的校验逻辑
     */
    private void validEntityBeforeSave(Live entity){
        // 默认开播状态为未开始
        if (entity.getStatus() == null) {
            entity.setStatus(0L);
        }
    }

    /**
     * 校验并批量删除直播信息
     * 
     * <p>批量删除指定ID的直播信息，支持业务校验</p>
     *
     * @param ids 待删除的主键集合，不能为空或包含空元素
     * @param isValid 是否进行业务校验，true表示执行校验
     * @return 是否删除成功，true表示成功，false表示失败
     * @throws IllegalArgumentException 当ids为空时抛出
     * 
     * @apiNote
     * <ul>
     *   <li>采用逻辑删除，不会物理删除数据</li>
     *   <li>支持批量操作，提高性能</li>
     *   <li>可选择是否进行业务校验</li>
     *   <li>删除失败时支持事务回滚</li>
     * </ul>
     * 
     * @see #validBeforeDelete(Collection) 删除前校验方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(Boolean.TRUE.equals(isValid)){
            // 校验是否存在正在直播的记录
            LambdaQueryWrapper<Live> lqw = Wrappers.lambdaQuery();
            lqw.in(Live::getId, ids);
            lqw.eq(Live::getStatus, 1); // 1表示正在直播
            Long count = baseMapper.selectCount(lqw);
            if (count > 0) {
                throw new RuntimeException("存在正在直播的记录，无法删除");
            }
        }
        
        // 删除关联的推流信息
        ids.forEach(liveId -> {
            GetStreamVo streamInfo = liveStreamService.queryByLiveId(liveId);
            if (streamInfo != null && streamInfo.getId() != null) {
                liveStreamService.deleteWithValidByIds(List.of(streamInfo.getId()), false);
            }
        });
        
        // 删除关联的回放信息
        ids.forEach(liveId -> {
            LiveReplayBo queryBo = new LiveReplayBo();
            queryBo.setLiveId(liveId);
            List<LiveReplayVo> replayList = liveReplayService.queryList(queryBo);
            if (!replayList.isEmpty()) {
                List<Long> replayIds = replayList.stream()
                    .map(LiveReplayVo::getId)
                    .collect(Collectors.toList());
                liveReplayService.deleteWithValidByIds(replayIds, false);
            }
        });
        
        // 删除直播信息
        return baseMapper.deleteBatchIds(ids) > 0;
    }
    
    /**
     * 删除前的业务校验
     * 
     * <p>在删除直播信息前检查业务约束和依赖关系</p>
     *
     * @param ids 待删除的ID集合
     * @throws IllegalStateException 当存在业务约束不允许删除时抛出
     * 
     * @implNote
     * <p>具体的校验规则包括：</p>
     * <ul>
     *   <li>检查直播是否正在进行中</li>
     *   <li>验证是否存在相关的预约记录</li>
     *   <li>检查回放文件是否需要保留</li>
     *   <li>确认用户权限是否允许删除</li>
     * </ul>
     */
    private void validBeforeDelete(Collection<Long> ids) {
        // TODO: 可以添加删除前的业务校验逻辑
    }

    /**
     * 按分类ID查询直播信息列表（分页）
     *
     * @param categoryId 分类ID
     * @param status     直播状态(可选，null表示查询所有状态)
     * @param pageQuery  分页参数
     * @return 直播信息分页列表
     */
    @Override
    public TableDataInfo<LiveVo> queryPageListByCategory(Long categoryId, Long status, PageQuery pageQuery) {
        LambdaQueryWrapper<Live> lqw = Wrappers.lambdaQuery();
        lqw.eq(Live::getCategoryId, categoryId);
        lqw.eq(status != null, Live::getStatus, status);
        lqw.orderByDesc(Live::getCreateTime);
        
        Page<LiveVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }
    
    /**
     * 按分类ID查询直播信息列表（不分页）
     *
     * @param categoryId 分类ID
     * @param status     直播状态(可选，null表示查询所有状态)
     * @param limit      返回数量限制(可选)
     * @return 直播信息列表
     */
    @Override
    public List<LiveVo> queryListByCategory(Long categoryId, Long status, Integer limit) {
        LambdaQueryWrapper<Live> lqw = Wrappers.lambdaQuery();
        lqw.eq(Live::getCategoryId, categoryId);
        lqw.eq(status != null, Live::getStatus, status);
        lqw.orderByDesc(Live::getCreateTime);
        
        if (limit != null && limit > 0) {
            lqw.last("LIMIT " + limit);
        }
        
        return baseMapper.selectVoList(lqw);
    }
    
    /**
     * 按分类路径查询直播信息列表（包含子分类，分页）
     *
     * @param path      分类路径
     * @param status    直播状态（可选）
     * @param pageQuery 分页参数
     * @return 直播信息分页列表
     */
    @Override
    public TableDataInfo<LiveVo> queryPageListByCategoryPath(String path, Long status, PageQuery pageQuery) {
        // 1. 查询以给定路径开头的所有分类
        LambdaQueryWrapper<LiveCategory> categoryLqw = Wrappers.lambdaQuery();
        categoryLqw.likeRight(LiveCategory::getPath, path);
        categoryLqw.or().eq(LiveCategory::getPath, path);
        
        List<LiveCategory> categories = liveCategoryMapper.selectList(categoryLqw);
        if (categories.isEmpty()) {
            return new TableDataInfo<>();
        }
        
        // 2. 提取分类ID列表
        List<Long> categoryIds = categories.stream()
            .map(LiveCategory::getId)
            .collect(Collectors.toList());
        
        // 3. 查询这些分类下的直播
        LambdaQueryWrapper<Live> lqw = Wrappers.lambdaQuery();
        lqw.in(Live::getCategoryId, categoryIds);
        lqw.eq(status != null, Live::getStatus, status);
        lqw.orderByDesc(Live::getCreateTime);
        
        Page<LiveVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 分页查询直播信息列表（包含推流、回放等详细信息）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 包含详细信息的直播分页列表
     */
    @Override
    public TableDataInfo<Map<String, Object>> queryPageListWithDetails(LiveBo bo, PageQuery pageQuery) {
        TableDataInfo<LiveVo> baseData = queryPageList(bo, pageQuery);
        List<Map<String, Object>> detailsList = new ArrayList<>();
        
        for (LiveVo liveVo : baseData.getRows()) {
            Map<String, Object> details = new HashMap<>();
            details.put("liveInfo", liveVo);
            
            // 获取直播状态
            Long status = liveVo.getStatus();
            
            // 状态为直播中(1)时，添加推流信息和播放信息，但不添加回放信息
            if (status != null && status == 1L) {
                LiveStreamInfo streamInfo = liveStreamService.getStreamInfoByLiveId(liveVo.getId());
                if (streamInfo != null) {
                    details.put("streamInfo", streamInfo);
                    details.put("playUrls", streamInfo.getPlayUrls());
                }
            } 
            // 状态为回放中(3)时，仅添加回放信息，不添加推流和播放信息
            else if (status != null && status == 3L) {
                LiveReplayBo replayBo = new LiveReplayBo();
                replayBo.setLiveId(liveVo.getId());
                List<LiveReplayVo> replayList = liveReplayService.queryList(replayBo);
                if (replayList != null && !replayList.isEmpty()) {
                    details.put("replayList", replayList);
                }
            }
            // 其他状态(未开始、已结束等)，根据实际情况添加信息
            else {
                // 获取推流信息
                LiveStreamInfo streamInfo = liveStreamService.getStreamInfoByLiveId(liveVo.getId());
                if (streamInfo != null) {
                    details.put("streamInfo", streamInfo);
                    details.put("playUrls", streamInfo.getPlayUrls());
                }
                
                // 获取回放信息
                LiveReplayBo replayBo = new LiveReplayBo();
                replayBo.setLiveId(liveVo.getId());
                List<LiveReplayVo> replayList = liveReplayService.queryList(replayBo);
                if (replayList != null && !replayList.isEmpty()) {
                    details.put("replayList", replayList);
                }
            }
            
            detailsList.add(details);
        }
        
        return new TableDataInfo<>(detailsList, baseData.getTotal());
    }
}
