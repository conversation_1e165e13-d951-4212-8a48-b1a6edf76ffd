package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveSchedule;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 直播预告业务对象 live_schedule
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveSchedule.class, reverseConvertGenerate = false)
public class LiveScheduleBo extends BaseEntity {

    /**
     * 预告ID
     */
    @NotNull(message = "预告ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 预告标题
     */
    @NotBlank(message = "预告标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 主播ID
     */
    @NotNull(message = "主播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long hostUserId;

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long categoryId;

    /**
     * 预告描述
     */
    private String description;

    /**
     * 预告封面URL
     */
    private String coverImgUrl;

    /**
     * 预定时间
     */
    @NotNull(message = "预定时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date scheduledTime;

    /**
     * 预计时长(单位:分钟)
     */
    private Long estimatedDurationMinutes;

    /**
     * 状态(0-已取消,1-待开播)
     */
    @NotNull(message = "状态(0-已取消,1-待开播)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long scheduleStatus;


}
