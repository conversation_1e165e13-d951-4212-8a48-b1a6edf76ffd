package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveShare;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 直播分享记录业务对象 live_share
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveShare.class, reverseConvertGenerate = false)
public class LiveShareBo extends BaseEntity {

    /**
     * 分享记录ID
     */
    @NotNull(message = "分享记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long liveId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 分享平台
     */
    private String sharePlatform;


}
