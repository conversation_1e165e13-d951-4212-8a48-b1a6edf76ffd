package com.ydwl.live.domain.vo;

import java.util.Date;
import com.ydwl.live.domain.LiveTranscodeTask;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;



/**
 * 转码任务视图对象 live_transcode_task
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveTranscodeTask.class)
public class LiveTranscodeTaskVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 转码任务ID
     */
    @ExcelProperty(value = "转码任务ID")
    private Long id;

    /**
     * 任务编号
     */
    @ExcelProperty(value = "任务编号")
    private String taskNo;

    /**
     * 任务类型(fc-函数计算,mts-媒体转码服务)
     */
    @ExcelProperty(value = "任务类型(fc-函数计算,mts-媒体转码服务)")
    private String taskType;

    /**
     * 关联直播ID
     */
    @ExcelProperty(value = "关联直播ID")
    private Long liveId;

    /**
     * 源文件地址
     */
    @ExcelProperty(value = "源文件地址")
    private String sourceUrl;

    /**
     * 状态(0-待处理,1-处理中,2-已完成,3-已失败,4-已取消)
     */
    @ExcelProperty(value = "状态(0-待处理,1-处理中,2-已完成,3-已失败,4-已取消)")
    private Long status;

    /**
     * 转码模板ID
     */
    @ExcelProperty(value = "转码模板ID")
    private Long templateId;

    /**
     * OSS存储桶名称
     */
    @ExcelProperty(value = "OSS存储桶名称")
    private String bucket;

    /**
     * OSS对象键名
     */
    @ExcelProperty(value = "OSS对象键名")
    private String objectKey;

    /**
     * 输出文件地址
     */
    @ExcelProperty(value = "输出文件地址")
    private String outputUrl;

    /**
     * 服务商任务ID (例如MTS/FC的JobID)
     */
    private String providerJobId;

    /**
     * 视频时长(秒)
     */
    @ExcelProperty(value = "视频时长(秒)")
    private Long duration;

    /**
     * 文件大小(byte)
     */
    @ExcelProperty(value = "文件大小(byte)")
    private Long fileSize;

    /**
     * 转码进度(%)
     */
    @ExcelProperty(value = "转码进度(%)")
    private Long progress;

    /**
     * 业务ID(回调标识)
     */
    @ExcelProperty(value = "业务ID(回调标识)")
    private String bizId;

    /**
     * 回调通知URL
     */
    @ExcelProperty(value = "回调通知URL")
    private String callbackUrl;

    /**
     * 回调状态(0-未回调,1-已回调)
     */
    @ExcelProperty(value = "回调状态(0-未回调,1-已回调)")
    private Long callbackStatus;

    /**
     * 回调接收时间
     */
    private Date callbackTime;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errorMsg;

    /**
     * 重试次数
     */
    @ExcelProperty(value = "重试次数")
    private Long retryCount;

    /**
     * 优先级(1-10)
     */
    @ExcelProperty(value = "优先级(1-10)")
    private Long priority;

    /**
     * 转码任务对应的输出列表
     */
    private List<LiveTranscodeOutputVo> outputs;

}
