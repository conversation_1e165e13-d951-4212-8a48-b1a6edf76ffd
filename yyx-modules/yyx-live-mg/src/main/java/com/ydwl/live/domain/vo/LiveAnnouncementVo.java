package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.live.domain.LiveAnnouncement;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 直播公告视图对象 live_announcement
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveAnnouncement.class)
public class LiveAnnouncementVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 公告ID
     */
    @ExcelProperty(value = "公告ID")
    private Long id;

    /**
     * 直播ID
     */
    @ExcelProperty(value = "直播ID")
    private Long liveId;

    /**
     * 公告内容
     */
    @ExcelProperty(value = "公告内容")
    private String content;

    /**
     * 生效时间
     */
    @ExcelProperty(value = "生效时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 状态(0-下线,1-上线)
     */
    @ExcelProperty(value = "状态(0-下线,1-上线)")
    private Long announcementStatus;


}
