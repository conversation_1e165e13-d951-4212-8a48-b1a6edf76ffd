package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.live.domain.LiveUser;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用户视图对象 live_user
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveUser.class)
public class LiveUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long id;

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名")
    private String username;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String phone;

    /**
     * 微信唯一标识
     */
    @ExcelProperty(value = "微信唯一标识")
    private String openid;

    /**
     * 头像URL
     */
    @ExcelProperty(value = "头像URL")
    private String avatarUrl;

    /**
     * 用户昵称
     */
    @ExcelProperty(value = "用户昵称")
    private String nickname;

    /**
     * 用户邮箱
     */
    @ExcelProperty(value = "用户邮箱")
    private String email;

    /**
     * 用户状态(0-禁用,1-正常)
     */
    @ExcelProperty(value = "用户状态(0-禁用,1-正常)")
    private Long userStatus;

    /**
     * 个人简介
     */
    @ExcelProperty(value = "个人简介")
    private String bio;

    /**
     * 性别(0-未知,1-男,2-女)
     */
    @ExcelProperty(value = "性别(0-未知,1-男,2-女)")
    private Long gender;

    /**
     * 生日
     */
    @ExcelProperty(value = "生日")
    private Date birthday;

    /**
     * 最后登录时间
     */
    @ExcelProperty(value = "最后登录时间")
    private Date lastLoginTime;

    /**
     * 最后登录IP
     */
    @ExcelProperty(value = "最后登录IP")
    private String lastLoginIp;

    /**
     * 通知偏好设置
     */
    @ExcelProperty(value = "通知偏好设置")
    private String notificationSettings;

    /**
     * 用户行为统计
     */
    @ExcelProperty(value = "用户行为统计")
    private String userStatistics;


}
