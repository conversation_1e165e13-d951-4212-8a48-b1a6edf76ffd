package com.ydwl.live.task.optimized;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ydwl.live.domain.Live;
import com.ydwl.live.domain.LiveStream;
import com.ydwl.live.mapper.LiveMapper;
import com.ydwl.live.mapper.LiveStreamMapper;
import com.ydwl.live.service.impl.LiveStateManager;
import com.ydwl.live.service.optimized.OptimizedLiveStateManager;
import com.ydwl.live.util.DistributedLockUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 优化后的直播状态检查定时任务
 * 
 * 解决并发执行、N+1查询等问题
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OptimizedLiveStatusCheckTask {
    
    private final LiveMapper liveMapper;
    private final LiveStreamMapper liveStreamMapper;
    private final OptimizedLiveStateManager stateManager;
    private final DistributedLockUtil distributedLockUtil;
    
    private static final String TASK_LOCK_KEY = "live:status:check:task";
    private static final long TASK_LOCK_TIMEOUT = 300; // 5分钟任务锁
    private static final long LIVE_TIMEOUT_HOURS = 12; // 直播超时时间12小时
    
    /**
     * 定时检查直播状态
     * 使用分布式锁防止多实例重复执行
     */
    @Scheduled(fixedRateString = "${ydwl.live.task.status-check-interval:60}", timeUnit = TimeUnit.SECONDS)
    public void checkLiveStatus() {
        String lockValue = UUID.randomUUID().toString();
        
        // 获取分布式锁，防止多实例重复执行
        if (!distributedLockUtil.lock(TASK_LOCK_KEY, lockValue, TASK_LOCK_TIMEOUT, TimeUnit.SECONDS)) {
            log.debug("直播状态检查任务已在其他实例执行，跳过本次执行");
            return;
        }
        
        try {
            log.info("开始执行直播状态检查任务");
            executeStatusCheck();
        } catch (Exception e) {
            log.error("直播状态检查任务异常", e);
        } finally {
            distributedLockUtil.unlock(TASK_LOCK_KEY, lockValue);
        }
    }
    
    /**
     * 执行状态检查逻辑
     */
    private void executeStatusCheck() {
        // 1. 批量查询正在直播的记录
        List<Live> livesInProgress = liveMapper.selectList(
            new LambdaQueryWrapper<Live>()
                .eq(Live::getStatus, LiveStateManager.LiveStatus.LIVE.getValue())
                .select(Live::getId, Live::getActualStartTime, Live::getUpdateTime)
        );
        
        if (livesInProgress.isEmpty()) {
            log.info("当前没有正在进行的直播");
            return;
        }
        
        log.info("发现{}个正在进行的直播，开始检查", livesInProgress.size());
        
        // 2. 批量查询推流信息，避免N+1问题
        List<Long> liveIds = livesInProgress.stream()
            .map(Live::getId)
            .collect(Collectors.toList());
        
        Map<Long, LiveStream> streamMap = liveStreamMapper.selectList(
            new LambdaQueryWrapper<LiveStream>()
                .in(LiveStream::getLiveId, liveIds)
                .select(LiveStream::getLiveId, LiveStream::getStreamStatus, LiveStream::getUpdateTime)
        ).stream().collect(Collectors.toMap(LiveStream::getLiveId, stream -> stream));
        
        // 3. 检查每个直播并收集需要更新的ID
        List<Long> timeoutLiveIds = new ArrayList<>();
        List<Long> noStreamLiveIds = new ArrayList<>();
        
        for (Live live : livesInProgress) {
            Long liveId = live.getId();
            LiveStream stream = streamMap.get(liveId);
            
            if (stream == null) {
                log.warn("直播({})无推流信息，标记为需要结束", liveId);
                noStreamLiveIds.add(liveId);
                continue;
            }
            
            if (isLiveTimeout(live, stream)) {
                log.info("直播({})超时无推流，标记为需要结束", liveId);
                timeoutLiveIds.add(liveId);
            }
        }
        
        // 4. 批量更新状态
        int totalUpdated = 0;
        
        if (!timeoutLiveIds.isEmpty()) {
            int updated = stateManager.batchUpdateLiveStatus(timeoutLiveIds, LiveStateManager.LiveStatus.ENDED);
            totalUpdated += updated;
            log.info("批量结束超时直播: 处理{}个，成功{}个", timeoutLiveIds.size(), updated);
        }
        
        if (!noStreamLiveIds.isEmpty()) {
            int updated = stateManager.batchUpdateLiveStatus(noStreamLiveIds, LiveStateManager.LiveStatus.ERROR);
            totalUpdated += updated;
            log.info("批量标记无推流直播为异常: 处理{}个，成功{}个", noStreamLiveIds.size(), updated);
        }
        
        log.info("直播状态检查任务完成: 检查{}个直播，更新{}个状态", livesInProgress.size(), totalUpdated);
    }
    
    /**
     * 检查直播是否超时
     */
    private boolean isLiveTimeout(Live live, LiveStream stream) {
        if (live.getActualStartTime() == null) {
            return false;
        }
        
        // 检查直播是否超过最大时长
        LocalDateTime startTime = LocalDateTime.ofInstant(
            live.getActualStartTime().toInstant(), ZoneId.systemDefault());
        LocalDateTime timeoutTime = startTime.plusHours(LIVE_TIMEOUT_HOURS);
        
        if (LocalDateTime.now().isAfter(timeoutTime)) {
            return true;
        }
        
        // 检查推流状态是否异常
        if (stream.getStreamStatus() != null && stream.getStreamStatus() == 2L) {
            // 推流状态为异常，且超过一定时间没有恢复
            LocalDateTime streamUpdateTime = LocalDateTime.ofInstant(
                stream.getUpdateTime().toInstant(), ZoneId.systemDefault());
            return LocalDateTime.now().isAfter(streamUpdateTime.plusMinutes(10));
        }
        
        return false;
    }
    
    /**
     * 清理过期的直播记录（可选的清理任务）
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupExpiredLives() {
        String lockValue = UUID.randomUUID().toString();
        String cleanupLockKey = "live:cleanup:task";
        
        if (!distributedLockUtil.lock(cleanupLockKey, lockValue, 3600, TimeUnit.SECONDS)) {
            log.debug("直播清理任务已在其他实例执行，跳过本次执行");
            return;
        }
        
        try {
            log.info("开始执行直播记录清理任务");
            
            // 查找30天前结束的直播
            Date thirtyDaysAgo = Date.from(
                LocalDateTime.now().minusDays(30)
                    .atZone(ZoneId.systemDefault()).toInstant());
            
            List<Live> expiredLives = liveMapper.selectList(
                new LambdaQueryWrapper<Live>()
                    .eq(Live::getStatus, LiveStateManager.LiveStatus.ENDED.getValue())
                    .lt(Live::getActualEndTime, thirtyDaysAgo)
                    .select(Live::getId)
            );
            
            if (!expiredLives.isEmpty()) {
                // 这里可以实现软删除或归档逻辑
                log.info("发现{}个过期直播记录，可考虑归档处理", expiredLives.size());
            }
            
        } catch (Exception e) {
            log.error("直播记录清理任务异常", e);
        } finally {
            distributedLockUtil.unlock(cleanupLockKey, lockValue);
        }
    }
}
