package com.ydwl.live.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ydwl.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 视频播放记录对象 live_video_play_record
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_video_play_record")
public class LiveVideoPlayRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 直播ID
     */
    private Long liveId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 播放时间
     */
    private Date playTime;

    /**
     * 播放时长(单位:秒)
     */
    private Long playDurationSeconds;

    /**
     * 最后播放位置(单位:秒)
     */
    private Long lastPositionSeconds;

    /**
     * 是否看完(0-否,1-是)
     */
    private Long isFinished;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 播放清晰度
     */
    private String videoQuality;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
