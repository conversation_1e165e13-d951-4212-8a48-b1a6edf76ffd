package com.ydwl.live.service;

import com.ydwl.live.event.TranscodeEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 转码服务使用示例
 * 演示如何发布转码事件并调用转码服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TranscodeExampleService {

    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 发布转码事件示例 - 使用默认配置
     *
     * @param bucket   存储桶名称
     * @param object   文件路径
     * @param bizId    业务ID
     * @param callbackUrl 回调地址
     */
    public void publishTranscodeEventDefault(String bucket, String object, String bizId, String callbackUrl) {
        // 构建转码参数
        Map<String, Object> transcodeParams = new HashMap<>();
        transcodeParams.put("bucket", bucket);
        transcodeParams.put("object", object);
        transcodeParams.put("bizId", bizId);
        transcodeParams.put("callbackUrl", callbackUrl);
        
        // 设置默认分辨率配置
        Map<String, String> resolutions = new HashMap<>();
        resolutions.put("p480", "true");
        resolutions.put("p720", "true");
        resolutions.put("p1080", "false");
        transcodeParams.put("resolutions", resolutions);
        
        transcodeParams.put("segmentTime", 4); // 默认4秒切片
        // 不指定method，使用配置文件中的默认方式
        
        // 发布转码事件
        TranscodeEvent event = new TranscodeEvent(transcodeParams);
        applicationEventPublisher.publishEvent(event);
        
        log.info("已发布转码事件 - 使用默认配置，BizId: {}", bizId);
    }

    /**
     * 发布转码事件示例 - 使用函数计算方式
     *
     * @param bucket   存储桶名称
     * @param object   文件路径
     * @param bizId    业务ID
     * @param callbackUrl 回调地址
     */
    public void publishTranscodeEventWithFc(String bucket, String object, String bizId, String callbackUrl) {
        // 构建转码参数
        Map<String, Object> transcodeParams = new HashMap<>();
        transcodeParams.put("bucket", bucket);
        transcodeParams.put("object", object);
        transcodeParams.put("bizId", bizId);
        transcodeParams.put("callbackUrl", callbackUrl);
        transcodeParams.put("method", "fc"); // 指定使用函数计算
        
        // 设置分辨率配置
        Map<String, String> resolutions = new HashMap<>();
        resolutions.put("p480", "true");
        resolutions.put("p720", "true");
        resolutions.put("p1080", "true");
        transcodeParams.put("resolutions", resolutions);
        
        transcodeParams.put("segmentTime", 6); // 6秒切片
        
        // 发布转码事件
        TranscodeEvent event = new TranscodeEvent(transcodeParams);
        applicationEventPublisher.publishEvent(event);
        
        log.info("已发布转码事件 - 使用函数计算方式，BizId: {}", bizId);
    }

    /**
     * 发布转码事件示例 - 使用MTS媒体转码方式
     *
     * @param bucket   存储桶名称
     * @param object   文件路径
     * @param bizId    业务ID
     * @param callbackUrl 回调地址
     */
    public void publishTranscodeEventWithMts(String bucket, String object, String bizId, String callbackUrl) {
        // 构建转码参数
        Map<String, Object> transcodeParams = new HashMap<>();
        transcodeParams.put("bucket", bucket);
        transcodeParams.put("object", object);
        transcodeParams.put("bizId", bizId);
        transcodeParams.put("callbackUrl", callbackUrl);
        transcodeParams.put("method", "mts"); // 指定使用MTS媒体转码
        
        // 设置分辨率配置
        Map<String, String> resolutions = new HashMap<>();
        resolutions.put("p480", "false");
        resolutions.put("p720", "true");
        resolutions.put("p1080", "true");
        transcodeParams.put("resolutions", resolutions);
        
        transcodeParams.put("segmentTime", 8); // 8秒切片
        
        // 发布转码事件
        TranscodeEvent event = new TranscodeEvent(transcodeParams);
        applicationEventPublisher.publishEvent(event);
        
        log.info("已发布转码事件 - 使用MTS媒体转码方式，BizId: {}", bizId);
    }

    /**
     * 发布转码事件示例 - 使用HTTP调用方式
     *
     * @param bucket   存储桶名称
     * @param object   文件路径
     * @param bizId    业务ID
     * @param callbackUrl 回调地址
     */
    public void publishTranscodeEventWithHttp(String bucket, String object, String bizId, String callbackUrl) {
        // 构建转码参数
        Map<String, Object> transcodeParams = new HashMap<>();
        transcodeParams.put("bucket", bucket);
        transcodeParams.put("object", object);
        transcodeParams.put("bizId", bizId);
        transcodeParams.put("callbackUrl", callbackUrl);
        transcodeParams.put("method", "http"); // 指定使用HTTP调用
        
        // 设置分辨率配置
        Map<String, String> resolutions = new HashMap<>();
        resolutions.put("p480", "true");
        resolutions.put("p720", "true");
        resolutions.put("p1080", "false");
        transcodeParams.put("resolutions", resolutions);
        
        transcodeParams.put("segmentTime", 5); // 5秒切片
        
        // 发布转码事件
        TranscodeEvent event = new TranscodeEvent(transcodeParams);
        applicationEventPublisher.publishEvent(event);
        
        log.info("已发布转码事件 - 使用HTTP调用方式，BizId: {}", bizId);
    }

    /**
     * 发布转码事件示例 - 自定义全部参数
     *
     * @param bucket      存储桶名称
     * @param object      文件路径
     * @param bizId       业务ID
     * @param callbackUrl 回调地址
     * @param method      转码方式：fc/mts/http
     * @param resolutions 分辨率配置
     * @param segmentTime 切片时长
     */
    public void publishTranscodeEventCustom(String bucket, String object, String bizId, String callbackUrl,
                                           String method, Map<String, String> resolutions, Integer segmentTime) {
        // 构建转码参数
        Map<String, Object> transcodeParams = new HashMap<>();
        transcodeParams.put("bucket", bucket);
        transcodeParams.put("object", object);
        transcodeParams.put("bizId", bizId);
        transcodeParams.put("callbackUrl", callbackUrl);
        transcodeParams.put("method", method);
        transcodeParams.put("resolutions", resolutions);
        transcodeParams.put("segmentTime", segmentTime);
        
        // 发布转码事件
        TranscodeEvent event = new TranscodeEvent(transcodeParams);
        applicationEventPublisher.publishEvent(event);
        
        log.info("已发布转码事件 - 自定义配置，BizId: {}, 方式: {}", bizId, method);
    }

    /**
     * 批量发布转码事件示例
     *
     * @param transcodeRequests 批量转码请求
     */
    public void publishBatchTranscodeEvents(java.util.List<Map<String, Object>> transcodeRequests) {
        for (Map<String, Object> request : transcodeRequests) {
            TranscodeEvent event = new TranscodeEvent(request);
            applicationEventPublisher.publishEvent(event);
            
            String bizId = (String) request.get("bizId");
            String method = (String) request.getOrDefault("method", "default");
            log.info("已发布批量转码事件，BizId: {}, 方式: {}", bizId, method);
            
            // 添加延迟避免同时发送过多事件
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("批量转码事件发布完成，共 {} 个任务", transcodeRequests.size());
    }
} 