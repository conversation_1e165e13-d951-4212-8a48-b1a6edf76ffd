package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveCategoryVo;
import com.ydwl.live.domain.bo.LiveCategoryBo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 直播分类Service接口
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
public interface ILiveCategoryService {

    /**
     * 查询直播分类
     *
     * @param id 主键
     * @return 直播分类
     */
    LiveCategoryVo queryById(Long id);


    /**
     * 查询符合条件的直播分类列表
     *
     * @param bo 查询条件
     * @return 直播分类列表
     */
    List<LiveCategoryVo> queryList(LiveCategoryBo bo);

    /**
     * 新增直播分类
     *
     * @param bo 直播分类
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveCategoryBo bo);

    /**
     * 修改直播分类
     *
     * @param bo 直播分类
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveCategoryBo bo);

    /**
     * 校验并批量删除直播分类信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
    
    /**
     * 获取分类树形结构
     * 
     * @param status 分类状态(可选，null表示查询所有状态)
     * @return 分类树形结构
     */
    List<Map<String, Object>> getCategoryTree(Long status);
    
    /**
     * 按层级查询分类
     * 
     * @param level 层级(从1开始)
     * @param status 分类状态(可选，null表示查询所有状态)
     * @return 指定层级的分类列表
     */
    List<LiveCategoryVo> getByLevel(Long level, Long status);
    
    /**
     * 获取子分类列表
     * 
     * @param parentId 父分类ID
     * @param status 分类状态(可选，null表示查询所有状态)
     * @return 子分类列表
     */
    List<LiveCategoryVo> getChildren(Long parentId, Long status);
    
    /**
     * 增加分类浏览次数
     * 
     * @param categoryId 分类ID
     * @return 是否增加成功
     */
    Boolean incrementViewCount(Long categoryId);
    
    /**
     * 获取热门分类列表
     * 
     * @param limit 限制数量
     * @return 热门分类列表
     */
    List<LiveCategoryVo> getHotCategories(Integer limit);
    
    /**
     * 递归增加父分类的直播计数
     * 
     * @param path 分类路径
     */
    void incrementLiveCountForPath(String path);
    
    /**
     * 递减父分类的直播计数
     * 
     * @param path 分类路径
     */
    void decrementLiveCountForPath(String path);
}
