package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveUserVo;
import com.ydwl.live.domain.bo.LiveUserBo;
import com.ydwl.live.service.ILiveUserService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 用户
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/user")
public class LiveUserController extends BaseController {

    private final ILiveUserService liveUserService;

    /**
     * 查询用户列表
     */
    @SaCheckPermission("live:user:list")
    @GetMapping("/list")
    public TableDataInfo<LiveUserVo> list(LiveUserBo bo, PageQuery pageQuery) {
        return liveUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户列表
     */
    @SaCheckPermission("live:user:export")
    @Log(title = "用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveUserBo bo, HttpServletResponse response) {
        List<LiveUserVo> list = liveUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户", LiveUserVo.class, response);
    }

    /**
     * 获取用户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:user:query")
    @GetMapping("/{id}")
    public R<LiveUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveUserService.queryById(id));
    }

    /**
     * 新增用户
     */
    @SaCheckPermission("live:user:add")
    @Log(title = "用户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveUserBo bo) {
        return toAjax(liveUserService.insertByBo(bo));
    }

    /**
     * 修改用户
     */
    @SaCheckPermission("live:user:edit")
    @Log(title = "用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveUserBo bo) {
        return toAjax(liveUserService.updateByBo(bo));
    }

    /**
     * 删除用户
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:user:remove")
    @Log(title = "用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveUserService.deleteWithValidByIds(List.of(ids), true));
    }
}
