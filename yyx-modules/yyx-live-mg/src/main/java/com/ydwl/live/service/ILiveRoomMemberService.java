package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveRoomMemberVo;
import com.ydwl.live.domain.bo.LiveRoomMemberBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 直播间在线成员Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-05-21
 */
public interface ILiveRoomMemberService {

    /**
     * 查询直播间在线成员
     *
     * @param id 主键
     * @return 直播间在线成员
     */
    LiveRoomMemberVo queryById(Long id);

    /**
     * 分页查询直播间在线成员列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播间在线成员分页列表
     */
    TableDataInfo<LiveRoomMemberVo> queryPageList(LiveRoomMemberBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的直播间在线成员列表
     *
     * @param bo 查询条件
     * @return 直播间在线成员列表
     */
    List<LiveRoomMemberVo> queryList(LiveRoomMemberBo bo);

    /**
     * 新增直播间在线成员
     *
     * @param bo 直播间在线成员
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveRoomMemberBo bo);

    /**
     * 修改直播间在线成员
     *
     * @param bo 直播间在线成员
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveRoomMemberBo bo);

    /**
     * 校验并批量删除直播间在线成员信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
