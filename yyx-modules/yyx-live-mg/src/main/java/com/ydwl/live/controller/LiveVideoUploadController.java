package com.ydwl.live.controller;

import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.ydwl.live.domain.bo.ConfirmUploadBo;
import com.ydwl.live.domain.bo.PreSignedUrlRequestBo;
import com.ydwl.live.domain.vo.OssPreSignedUrlVo;
import com.ydwl.live.service.ILiveOssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.live.domain.vo.LiveVideoUploadVo;
import com.ydwl.live.domain.bo.LiveVideoUploadBo;
import com.ydwl.live.service.ILiveVideoUploadService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;
import com.alibaba.fastjson.JSON;

/**
 * 视频上传记录
 *
 * 支持两种上传方案：
 * 1. 方案一：经过后台上传到OSS - upload() 方法
 * 2. 方案二：前端直接上传到OSS - getPreSignedUploadUrl() + confirmUpload() 方法
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/videoUpload")
@Slf4j
public class LiveVideoUploadController extends BaseController {

    private final ILiveVideoUploadService liveVideoUploadService;
    private final ILiveOssService ossService;


    /**
     * 上传回放视频到OSS（方案一：经过后台上传）
     * 流程：前端 -> 后端 -> OSS
     *
     * @param file 文件
     * @param liveid 直播ID
     */
    @Log(title = "OSS对象存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<LiveVideoUploadVo> upload(@RequestPart("file") MultipartFile file, @RequestPart("id") Long liveid) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }
        LiveVideoUploadVo upload = ossService.upload(file, liveid);
        return R.ok(upload);
    }

    /**
     * 获取OSS预签名上传URL（前端直接上传 - 第一步）
     * 流程：前端请求预签名URL -> 后端生成预签名URL -> 前端直接上传到OSS
     *
     * @param request 预签名URL请求对象
     */
    @SaCheckPermission("system:oss:upload")
    @Log(title = "获取预签名上传URL", businessType = BusinessType.OTHER)
    @PostMapping("/getPreSignedUrl")
    public R<OssPreSignedUrlVo> getPreSignedUploadUrl(@Validated @RequestBody PreSignedUrlRequestBo request) {

        // 验证文件类型
        if (!isValidVideoType(request.getContentType())) {
            return R.fail("不支持的文件类型：" + request.getContentType() + "。支持的类型：mp4, avi, mov, wmv, flv, mkv");
        }

        // 验证文件大小限制
        if (request.getMaxFileSize() != null && request.getMaxFileSize() > 2L * 1024 * 1024 * 1024) {
            return R.fail("文件大小不能超过2GB");
        }

        try {
            OssPreSignedUrlVo preSignedUrl = ossService.getPreSignedUploadUrl(request);
            return R.ok(preSignedUrl);
        } catch (Exception e) {
            return R.fail("获取预签名上传URL失败：" + e.getMessage());
        }
    }

    /** 这个弃用了，除非前端主动查询这个上传的信息，现在直接回调处理了，
     * 确认文件上传完成（方案二：前端直接上传 - 第二步）
     *
     * 前端使用预签名URL上传完成后，调用此接口确认上传并保存记录到数据库
     * 此方法作为OSS回调失败时的备选方案
     *
     * @param confirmUploadBo 确认上传请求对象
     */
    @SaCheckPermission("system:oss:upload")
    @Log(title = "确认文件上传完成", businessType = BusinessType.INSERT)
    @PostMapping("/confirmUpload")
    public R<LiveVideoUploadVo> confirmUpload(@Validated @RequestBody ConfirmUploadBo confirmUploadBo) {
        try {
            log.info("接收到前端确认上传请求：{}", JSON.toJSONString(confirmUploadBo));
            LiveVideoUploadVo result = ossService.confirmUpload(confirmUploadBo);
            log.info("前端确认上传成功，文件：{}，结果：{}",
                confirmUploadBo.getFileName(), JSON.toJSONString(result));
            return R.ok(result);
        } catch (Exception e) {
            log.error("前端确认上传失败", e);
            return R.fail("确认上传失败：" + e.getMessage());
        }
    }

    /**
     * 处理OSS上传回调
     * 当文件上传到OSS成功后，OSS会自动调用此接口
     */
    @Log(title = "处理OSS回调", businessType = BusinessType.INSERT)
    @PostMapping("/ossCallback")
    public R<Object> handleOssCallback(@RequestBody Map<String, Object> callbackData, HttpServletRequest request) {
        try {

            // 验证回调请求签名
            // String pubKeyUrl = request.getHeader("x-oss-pub-key-url");
            // String authorization = request.getHeader("authorization");
            // 检查必要的回调数据是否存在
            if (!callbackData.containsKey("objectKey") || !callbackData.containsKey("size") ||
                !callbackData.containsKey("liveId") || !callbackData.containsKey("fileName") ||
                !callbackData.containsKey("contentType")) {
                String errorMsg = "回调数据缺少必要参数：" + JSON.toJSONString(callbackData);
                log.error(errorMsg);
                // 按照OSS回调要求返回响应
                return R.ok(Map.of("Status", "Failed", "Message", errorMsg));
            }

            try {
                // 提取回调数据
                String objectKey = (String) callbackData.get("objectKey");
                Long size = Long.valueOf(callbackData.get("size").toString());
                String mimeType = callbackData.containsKey("mimeType") ?
                    (String) callbackData.get("mimeType") : "application/octet-stream";
                Long liveId = Long.valueOf(callbackData.get("liveId").toString());
                String fileName = (String) callbackData.get("fileName");
                String contentType = (String) callbackData.get("contentType");

                log.info("OSS回调数据解析：objectKey={}, size={}, mimeType={}, liveId={}, fileName={}, contentType={}",
                    objectKey, size, mimeType, liveId, fileName, contentType);

                // 构建确认上传BO对象
                ConfirmUploadBo confirmUploadBo = new ConfirmUploadBo();
                confirmUploadBo.setObjectKey(objectKey);
                confirmUploadBo.setFileSize(size);
                confirmUploadBo.setLiveId(liveId);
                confirmUploadBo.setFileName(fileName);
                confirmUploadBo.setContentType(contentType);
                LiveVideoUploadVo result = ossService.confirmUpload(confirmUploadBo);
                // 返回响应给阿里云
                return R.ok(Map.of("Status", "OK", "Message", "回调处理成功", "FileId", result.getId()));
            } catch (NumberFormatException e) {
                String errorMsg = "回调数据格式转换异常：" + e.getMessage();
                log.error(errorMsg, e);
                return R.ok(Map.of("Status", "Failed", "Message", errorMsg));
            } catch (Exception e) {
                String errorMsg = "回调数据处理异常：" + e.getMessage();
                log.error(errorMsg, e);
                return R.ok(Map.of("Status", "Failed", "Message", errorMsg));
            }
        } catch (Exception e) {
            log.error("处理OSS回调失败", e);
            return R.ok(Map.of("Status", "Failed", "Message", "处理OSS回调失败：" + e.getMessage()));
        }
    }

    /**
     * 验证是否为有效的视频文件类型
     */
    private boolean isValidVideoType(String contentType) {
        return contentType != null && (
            contentType.equals("video/mp4") ||
            contentType.equals("video/avi") ||
            contentType.equals("video/mov") ||
            contentType.equals("video/wmv") ||
            contentType.equals("video/flv") ||
            contentType.equals("video/mkv")
        );
    }

    /**
     * 查询视频上传记录列表
     */
    @SaCheckPermission("live:videoUpload:list")
    @GetMapping("/list")
    public TableDataInfo<LiveVideoUploadVo> list(LiveVideoUploadBo bo, PageQuery pageQuery) {
        return liveVideoUploadService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取视频上传记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:videoUpload:query")
    @GetMapping("/{id}")
    public R<LiveVideoUploadVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveVideoUploadService.queryById(id));
    }

    /**
     * 新增视频上传记录
     */
    @SaCheckPermission("live:videoUpload:add")
    @Log(title = "视频上传记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveVideoUploadBo bo) {
        return toAjax(liveVideoUploadService.insertByBo(bo));
    }

    /**
     * 修改视频上传记录
     */
    @SaCheckPermission("live:videoUpload:edit")
    @Log(title = "视频上传记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveVideoUploadBo bo) {
        return toAjax(liveVideoUploadService.updateByBo(bo));
    }

    /**
     * 删除视频上传记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:videoUpload:remove")
    @Log(title = "视频上传记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveVideoUploadService.deleteWithValidByIds(List.of(ids), true));
    }
}

/*
任务进度记录：
[2025-01-27 当前时间]
- 已修改：LiveVideoUploadController.java - 修复getPreSignedUploadUrl方法参数绑定问题
- 已创建：PreSignedUrlRequestBo.java - 新增预签名URL请求对象
- 更改：将getPreSignedUploadUrl方法的参数接收方式从@RequestParam改为@RequestBody，
  以支持JSON格式的请求体，解决了"Required request parameter 'liveId' for method parameter type Long is not present"错误
- 原因：前端发送的是JSON格式请求体，但后端使用@RequestParam接收导致参数绑定失败
- 阻碍因素：无
*/
