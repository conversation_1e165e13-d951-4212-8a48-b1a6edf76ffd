package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveEventConfigVo;
import com.ydwl.live.domain.bo.LiveEventConfigBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 事件配置Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveEventConfigService {

    /**
     * 查询事件配置
     *
     * @param id 主键
     * @return 事件配置
     */
    LiveEventConfigVo queryById(Long id);

    /**
     * 分页查询事件配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 事件配置分页列表
     */
    TableDataInfo<LiveEventConfigVo> queryPageList(LiveEventConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的事件配置列表
     *
     * @param bo 查询条件
     * @return 事件配置列表
     */
    List<LiveEventConfigVo> queryList(LiveEventConfigBo bo);

    /**
     * 新增事件配置
     *
     * @param bo 事件配置
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveEventConfigBo bo);

    /**
     * 修改事件配置
     *
     * @param bo 事件配置
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveEventConfigBo bo);

    /**
     * 校验并批量删除事件配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
