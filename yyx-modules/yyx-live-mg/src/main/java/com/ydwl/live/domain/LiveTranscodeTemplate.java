package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 视频转码模板对象 live_transcode_template
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_transcode_template")
public class LiveTranscodeTemplate extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 转码类型(fc-函数计算,mts-媒体转码服务)
     */
    private String templateType;

    /**
     * 清晰度标识(SD-标清,HD-高清,FHD-超清)
     */
    private String definition;

    /**
     * 分辨率(如:1920x1080)
     */
    private String resolution;

    /**
     * 视频码率(kbps)
     */
    private Long videoBitrate;

    /**
     * 音频码率(kbps)
     */
    private Long audioBitrate;

    /**
     * 帧率
     */
    private Long fps;

    /**
     * 输出格式
     */
    private String format;

    /**
     * 编码方式
     */
    private String codec;

    /**
     * 外部系统模板ID(阿里云模板ID)
     */
    private String externalId;

    /**
     * 状态(0-禁用,1-启用)
     */
    private Long status;

    /**
     * 是否默认(0-否,1-是)
     */
    private Long isDefault;


}
