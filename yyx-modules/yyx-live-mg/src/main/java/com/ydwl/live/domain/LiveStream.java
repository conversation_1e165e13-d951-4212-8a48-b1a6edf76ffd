package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 推流信息对象 live_stream
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_stream")
public class LiveStream extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 推流信息ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 直播ID
     */
    private Long liveId;

    /**
     * 推流地址
     */
    private String pushUrl;

    /**
     * 推流密钥
     */
    private String pushKey;

    /**
     * 状态(0-创建中,1-正常,2-异常)
     */
    private Long streamStatus;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
