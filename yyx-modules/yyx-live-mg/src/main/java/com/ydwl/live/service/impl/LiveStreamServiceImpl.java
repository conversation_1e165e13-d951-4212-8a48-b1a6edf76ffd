package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveStreamBo;
import com.ydwl.live.domain.vo.GetStreamVo;
import com.ydwl.live.domain.vo.LiveStreamVo;
import com.ydwl.live.domain.LiveStream;
import com.ydwl.live.mapper.LiveStreamMapper;
import com.ydwl.live.service.ILiveStreamService;
import com.ydwl.live.config.LiveStreamConfig;
import com.ydwl.live.service.model.LiveStreamInfo;
import com.ydwl.live.util.AliLiveUtil;
import com.ydwl.live.mapper.LiveMapper;
import com.ydwl.live.domain.Live;

import java.util.List;
import java.util.Map;
import java.util.Date;
import java.util.Collection;
import java.util.HashMap;

/**
 * 推流信息Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LiveStreamServiceImpl implements ILiveStreamService {

    private final LiveStreamMapper baseMapper;
    private final LiveMapper liveMapper;
    private final LiveStreamConfig streamConfig;

    /**
     * 查询推流信息
     *
     * @param id 主键
     * @return 推流信息
     */
    @Override
    public LiveStreamVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询推流信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 推流信息分页列表
     */
    @Override
    public TableDataInfo<LiveStreamVo> queryPageList(LiveStreamBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveStream> lqw = buildQueryWrapper(bo);
        Page<LiveStreamVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的推流信息列表
     *
     * @param bo 查询条件
     * @return 推流信息列表
     */
    @Override
    public List<LiveStreamVo> queryList(LiveStreamBo bo) {
        LambdaQueryWrapper<LiveStream> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveStream> buildQueryWrapper(LiveStreamBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveStream> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveStream::getId);
        lqw.eq(bo.getLiveId() != null, LiveStream::getLiveId, bo.getLiveId());
        lqw.eq(StringUtils.isNotBlank(bo.getPushUrl()), LiveStream::getPushUrl, bo.getPushUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getPushKey()), LiveStream::getPushKey, bo.getPushKey());
        lqw.eq(bo.getStreamStatus() != null, LiveStream::getStreamStatus, bo.getStreamStatus());
        return lqw;
    }

    /**
     * 新增推流信息
     *
     * @param bo 推流信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveStreamBo bo) {
        LiveStream add = MapstructUtils.convert(bo, LiveStream.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改推流信息
     *
     * @param bo 推流信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveStreamBo bo) {
        LiveStream update = MapstructUtils.convert(bo, LiveStream.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveStream entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除推流信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean InsertOrUpdateLiveStreamDTO(com.ydwl.live.callback.dto.InsertOrUpdateLiveStreamDTO bo) {
        // 生成推流信息
        AliLiveUtil.PushUrlRequest pushUrlRequest = new AliLiveUtil.PushUrlRequest(bo.getId().toString(), "live");
        AliLiveUtil.PushInfo pushInfo = AliLiveUtil.generatePushUrl(pushUrlRequest);
        log.debug("生成推流信息: {}", pushInfo);
        String pushUrl = pushInfo.getPushUrl();
        String pushKey = pushInfo.getAuthKey();
        // 创建bo
        LiveStreamBo liveStreamBo = new LiveStreamBo();
        if (bo.getIsUpdate()) {
            // 如果是更新 先查询出来 然后更新
            LiveStream selectById = baseMapper.selectOne(new LambdaQueryWrapper<LiveStream>().eq(LiveStream::getLiveId, bo.getId()));
            liveStreamBo.setId(selectById.getId());
            liveStreamBo.setLiveId(bo.getId());
            liveStreamBo.setPushUrl(pushUrl);
            liveStreamBo.setPushKey(pushKey);
            liveStreamBo.setStreamStatus(1L);
            liveStreamBo.setUpdateTime(new Date(System.currentTimeMillis()));
            return updateByBo(liveStreamBo);
        } else {
            liveStreamBo.setLiveId(bo.getId());
            liveStreamBo.setPushUrl(pushUrl);
            liveStreamBo.setPushKey(pushKey);
            liveStreamBo.setStreamStatus(1L);
            liveStreamBo.setCreateTime(new Date(System.currentTimeMillis()));
            liveStreamBo.setUpdateTime(new Date(System.currentTimeMillis()));
            return insertByBo(liveStreamBo);
        }
    }

    @Override
    public GetStreamVo queryByLiveId(Long liveId) {
        GetStreamVo getStreamVo = new GetStreamVo();

        // 查询直播信息
        Live live = liveMapper.selectById(liveId);
        
        // 查询推流信息
        LiveStream selectOne = baseMapper.selectOne(new LambdaQueryWrapper<LiveStream>().eq(LiveStream::getLiveId, liveId));
        if (selectOne == null) {
            return getStreamVo;
        }
        
        // 组装数据
        getStreamVo.setId(selectOne.getId());
        getStreamVo.setLiveId(selectOne.getLiveId());
        getStreamVo.setPushUrl(selectOne.getPushUrl());
        getStreamVo.setPushKey(selectOne.getPushKey());
        getStreamVo.setStreamStatus(selectOne.getStreamStatus());
        if (live != null) {
            getStreamVo.setTitle(live.getTitle());
        }
        getStreamVo.setUpdateTime(selectOne.getCreateTime());
        return getStreamVo;
    }
    
    @Override
    public LiveStreamInfo getStreamInfoByLiveId(Long liveId) {
        // 查询直播信息
        Live live = liveMapper.selectById(liveId);
        if (live == null) {
            return null;
        }
        
        // 查询推流信息
        LiveStream stream = baseMapper.selectOne(new LambdaQueryWrapper<LiveStream>().eq(LiveStream::getLiveId, liveId));
        if (stream == null) {
            return null;
        }
        
        // 生成播放地址
        AliLiveUtil.PlayUrlRequest playRequest = new AliLiveUtil.PlayUrlRequest(
            liveId.toString(),
            streamConfig.getPlayUrlExpireSeconds(),
            streamConfig.getAppName()
        );
        AliLiveUtil.PlayInfo playInfo = AliLiveUtil.generatePlayUrls(playRequest);
        
        // 解析播放地址为Map
        Map<String, String> playUrls = parsePlayUrls(playInfo.getPlayUrls());
        
        // 构建数据模型
        return LiveStreamInfo.builder()
            .id(stream.getId())
            .liveId(liveId)
            .title(live.getTitle())
            .pushUrl(stream.getPushUrl())
            .pushKey(stream.getPushKey())
            .streamStatus(stream.getStreamStatus())
            .playUrls(playUrls)
            .createTime(stream.getCreateTime())
            .updateTime(stream.getUpdateTime())
            .build();
    }
    
    @Override
    public LiveStreamInfo createStreamForLive(Long liveId, String title) {
        // 生成推流信息
        AliLiveUtil.PushUrlRequest pushRequest = new AliLiveUtil.PushUrlRequest(
            liveId.toString(),
            streamConfig.getAppName()
        );
        AliLiveUtil.PushInfo pushInfo = AliLiveUtil.generatePushUrl(pushRequest);
        
        // 保存推流信息
        LiveStream liveStream = new LiveStream();
        liveStream.setLiveId(liveId);
        liveStream.setPushUrl(pushInfo.getPushUrl());
        liveStream.setPushKey(pushInfo.getAuthKey());
        liveStream.setStreamStatus(1L); // 正常状态
        liveStream.setCreateTime(new Date());
        liveStream.setUpdateTime(new Date());
        baseMapper.insert(liveStream);
        
        // 更新直播表中的streamId
        Live live = new Live();
        live.setId(liveId);
        live.setStreamId(liveStream.getId());
        liveMapper.updateById(live);
        
        // 生成播放地址
        AliLiveUtil.PlayUrlRequest playRequest = new AliLiveUtil.PlayUrlRequest(
            liveId.toString(),
            streamConfig.getPlayUrlExpireSeconds(),
            streamConfig.getAppName()
        );
        AliLiveUtil.PlayInfo playInfo = AliLiveUtil.generatePlayUrls(playRequest);
        
        // 解析播放地址为Map
        Map<String, String> playUrls = parsePlayUrls(playInfo.getPlayUrls());
        
        // 构建数据模型
        return LiveStreamInfo.builder()
            .id(liveStream.getId())
            .liveId(liveId)
            .title(title)
            .pushUrl(liveStream.getPushUrl())
            .pushKey(liveStream.getPushKey())
            .streamStatus(liveStream.getStreamStatus())
            .playUrls(playUrls)
            .createTime(liveStream.getCreateTime())
            .updateTime(liveStream.getUpdateTime())
            .build();
    }

    @Override
    public GetStreamVo refreshPushStream(Long liveId) {
        // 1. Find the LiveStream by liveId.
        LiveStream liveStream = baseMapper.selectOne(new LambdaQueryWrapper<LiveStream>().eq(LiveStream::getLiveId, liveId));
        if (liveStream == null) {
            // Or throw an exception
            return null;
        }

        // 2. Generate new push info.
        AliLiveUtil.PushUrlRequest pushUrlRequest = new AliLiveUtil.PushUrlRequest(liveId.toString(), "live");
        AliLiveUtil.PushInfo pushInfo = AliLiveUtil.generatePushUrl(pushUrlRequest);
        String pushUrl = pushInfo.getPushUrl();
        String pushKey = pushInfo.getAuthKey();

        // 3. Update the LiveStream record.
        LiveStreamBo liveStreamBo = new LiveStreamBo();
        liveStreamBo.setId(liveStream.getId());
        liveStreamBo.setPushUrl(pushUrl);
        liveStreamBo.setPushKey(pushKey);
        liveStreamBo.setUpdateTime(new Date(System.currentTimeMillis()));
        updateByBo(liveStreamBo);

        // 4. Return the updated stream information.
        return queryByLiveId(liveId);
    }
    
    @Override
    public LiveStreamInfo refreshStreamInfo(Long liveId) {
        // 1. 查找LiveStream
        LiveStream liveStream = baseMapper.selectOne(new LambdaQueryWrapper<LiveStream>().eq(LiveStream::getLiveId, liveId));
        if (liveStream == null) {
            log.warn("找不到直播ID为{}的推流信息", liveId);
            return null;
        }
        
        // 2. 查找Live信息
        Live live = liveMapper.selectById(liveId);
        if (live == null) {
            log.warn("找不到ID为{}的直播信息", liveId);
            return null;
        }

        // 3. 生成新推流信息
        AliLiveUtil.PushUrlRequest pushRequest = new AliLiveUtil.PushUrlRequest(
            liveId.toString(),
            streamConfig.getAppName()
        );
        AliLiveUtil.PushInfo pushInfo = AliLiveUtil.generatePushUrl(pushRequest);

        // 4. 更新LiveStream记录
        liveStream.setPushUrl(pushInfo.getPushUrl());
        liveStream.setPushKey(pushInfo.getAuthKey());
        liveStream.setUpdateTime(new Date());
        baseMapper.updateById(liveStream);

        // 5. 生成播放地址
        AliLiveUtil.PlayUrlRequest playRequest = new AliLiveUtil.PlayUrlRequest(
            liveId.toString(),
            streamConfig.getPlayUrlExpireSeconds(),
            streamConfig.getAppName()
        );
        AliLiveUtil.PlayInfo playInfo = AliLiveUtil.generatePlayUrls(playRequest);
        
        // 解析播放地址为Map
        Map<String, String> playUrls = parsePlayUrls(playInfo.getPlayUrls());
        
        // 6. 构建并返回更新后的信息
        return LiveStreamInfo.builder()
            .id(liveStream.getId())
            .liveId(liveId)
            .title(live.getTitle())
            .pushUrl(liveStream.getPushUrl())
            .pushKey(liveStream.getPushKey())
            .streamStatus(liveStream.getStreamStatus())
            .playUrls(playUrls)
            .createTime(liveStream.getCreateTime())
            .updateTime(liveStream.getUpdateTime())
            .build();
    }
    
    /**
     * 将播放URL字符串解析为Map
     * 格式: RTMP: rtmp://domain/app/stream?auth_key=xxx\nFLV: http://...
     *
     * @param playUrlsStr 播放URL字符串
     * @return 格式->URL的映射
     */
    private Map<String, String> parsePlayUrls(String playUrlsStr) {
        Map<String, String> result = new HashMap<>();
        if (StringUtils.isBlank(playUrlsStr)) {
            return result;
        }
        
        String[] lines = playUrlsStr.split("\n");
        for (String line : lines) {
            String[] parts = line.split(": ", 2);
            if (parts.length == 2) {
                result.put(parts[0], parts[1]);
            }
        }
        
        return result;
    }
}
