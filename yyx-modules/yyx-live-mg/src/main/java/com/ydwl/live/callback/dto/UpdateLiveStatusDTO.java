package com.ydwl.live.callback.dto;

import com.ydwl.common.core.validate.EditGroup;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UpdateLiveStatusDTO {
    @NotNull(message = "直播ID不能为空", groups = EditGroup.class)
    private Long id;

    @NotNull(message = "状态不能为空", groups = EditGroup.class)
    @Min(value = 0, message = "状态最小值为0")
    @Max(value = 4, message = "状态最大值为4")
    private Long status;

}
