package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveTranscodeOutputVo;
import com.ydwl.live.domain.bo.LiveTranscodeOutputBo;
import com.ydwl.live.service.ILiveTranscodeOutputService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 转码输出文件
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/transcodeOutput")
public class LiveTranscodeOutputController extends BaseController {

    private final ILiveTranscodeOutputService liveTranscodeOutputService;

    /**
     * 查询转码输出文件列表
     */
    @SaCheckPermission("live:transcodeOutput:list")
    @GetMapping("/list")
    public TableDataInfo<LiveTranscodeOutputVo> list(LiveTranscodeOutputBo bo, PageQuery pageQuery) {
        return liveTranscodeOutputService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出转码输出文件列表
     */
    @SaCheckPermission("live:transcodeOutput:export")
    @Log(title = "转码输出文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveTranscodeOutputBo bo, HttpServletResponse response) {
        List<LiveTranscodeOutputVo> list = liveTranscodeOutputService.queryList(bo);
        ExcelUtil.exportExcel(list, "转码输出文件", LiveTranscodeOutputVo.class, response);
    }

    /**
     * 获取转码输出文件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:transcodeOutput:query")
    @GetMapping("/{id}")
    public R<LiveTranscodeOutputVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveTranscodeOutputService.queryById(id));
    }

    /**
     * 新增转码输出文件
     */
    @SaCheckPermission("live:transcodeOutput:add")
    @Log(title = "转码输出文件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveTranscodeOutputBo bo) {
        return toAjax(liveTranscodeOutputService.insertByBo(bo));
    }

    /**
     * 修改转码输出文件
     */
    @SaCheckPermission("live:transcodeOutput:edit")
    @Log(title = "转码输出文件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveTranscodeOutputBo bo) {
        return toAjax(liveTranscodeOutputService.updateByBo(bo));
    }

    /**
     * 删除转码输出文件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:transcodeOutput:remove")
    @Log(title = "转码输出文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveTranscodeOutputService.deleteWithValidByIds(List.of(ids), true));
    }
}
