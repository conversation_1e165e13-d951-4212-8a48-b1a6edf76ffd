package com.ydwl.live.service;

import com.ydwl.LiveTranscoding.domain.dto.FcTranscodeCallbackResponseDto;
import com.ydwl.LiveTranscoding.domain.vo.UniversalTranscodeCallbackVo;

/**
 * 转码回调服务接口
 * 定义FC和MTS转码回调处理方法
 */
public interface ITranscodeCallbackService {

    /**
     * 处理FC转码成功回调
     * 
     * @param callbackData FC转码回调数据
     */
    void handleFcTranscodeSuccess(FcTranscodeCallbackResponseDto callbackData);

    /**
     * 处理FC转码失败回调
     * 
     * @param callbackData FC转码回调数据
     */
    void handleFcTranscodeError(FcTranscodeCallbackResponseDto callbackData);

    /**
     * 处理MTS转码成功回调
     * 
     * @param callbackData MTS转码回调数据 (使用新的Universal DTO)
     */
    void handleMtsTranscodeSuccess(UniversalTranscodeCallbackVo callbackData);

    /**
     * 处理MTS转码失败回调
     * 
     * @param callbackData MTS转码回调数据 (使用新的Universal DTO)
     */
    void handleMtsTranscodeError(UniversalTranscodeCallbackVo callbackData);
} 