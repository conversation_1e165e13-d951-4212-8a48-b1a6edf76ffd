package com.ydwl.live.manager;

import com.ydwl.live.domain.model.LiveAggregate;
import com.ydwl.live.domain.model.valueobject.*;
import com.ydwl.live.domain.service.LiveDomainService;
import com.ydwl.live.domain.repository.LiveRepository;
import com.ydwl.live.manager.dto.LiveCreateCommand;
import com.ydwl.live.manager.dto.LiveDetailDto;
import com.ydwl.live.manager.dto.LiveListDto;
import com.ydwl.live.manager.dto.LiveUpdateCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 直播管理器
 * 
 * 负责协调领域服务和应用服务，处理复杂的业务流程
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LiveManager {
    
    private final LiveDomainService liveDomainService;
    private final LiveRepository liveRepository;
    private final DomainEventPublisher domainEventPublisher;
    
    /**
     * 创建直播
     */
    @Transactional(rollbackFor = Exception.class)
    public LiveDetailDto createLive(LiveCreateCommand command) {
        try {
            // 构建领域对象
            LiveInfo liveInfo = LiveInfo.create(
                command.getCategoryId(),
                command.getTitle(),
                command.getCoverImgUrl(),
                command.getPlanStartTime(),
                command.getDescription(),
                command.getTagList()
            );
            
            LiveSettings settings = LiveSettings.create(
                command.isSignupRequired(),
                command.isReplayEnabled(),
                command.isAutoRecord(),
                command.isChatEnabled(),
                command.getChatDelay(),
                command.isGiftEnabled(),
                command.getDefaultQuality(),
                LiveSettings.AccessLevel.fromValue(command.getAccessLevel())
            );
            
            // 创建直播聚合
            LiveAggregate aggregate = liveDomainService.createLive(liveInfo, settings);
            
            // 发布领域事件
            publishDomainEvents(aggregate);
            
            // 转换为DTO返回
            return convertToDetailDto(aggregate);
            
        } catch (Exception e) {
            log.error("创建直播失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建直播失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 更新直播信息
     */
    @Transactional(rollbackFor = Exception.class)
    public LiveDetailDto updateLive(LiveUpdateCommand command) {
        LiveId liveId = LiveId.of(command.getLiveId());
        
        LiveAggregate aggregate = liveRepository.findById(liveId)
            .orElseThrow(() -> new IllegalArgumentException("直播不存在: " + command.getLiveId()));
        
        // 更新直播信息
        LiveInfo updatedInfo = aggregate.getLiveInfo().update(
            command.getTitle(),
            command.getCoverImgUrl(),
            command.getPlanStartTime(),
            command.getDescription(),
            command.getTagList()
        );
        
        // 这里需要在聚合中添加更新方法
        // aggregate.updateLiveInfo(updatedInfo);
        
        // 保存聚合
        liveRepository.save(aggregate);
        
        // 发布领域事件
        publishDomainEvents(aggregate);
        
        return convertToDetailDto(aggregate);
    }
    
    /**
     * 开始直播
     */
    @Transactional(rollbackFor = Exception.class)
    public void startLive(Long liveId) {
        LiveId id = LiveId.of(liveId);
        liveDomainService.startLive(id);
        
        // 获取聚合并发布事件
        Optional<LiveAggregate> aggregateOpt = liveRepository.findById(id);
        aggregateOpt.ifPresent(this::publishDomainEvents);
    }
    
    /**
     * 结束直播
     */
    @Transactional(rollbackFor = Exception.class)
    public void endLive(Long liveId) {
        LiveId id = LiveId.of(liveId);
        liveDomainService.endLive(id);
        
        // 获取聚合并发布事件
        Optional<LiveAggregate> aggregateOpt = liveRepository.findById(id);
        aggregateOpt.ifPresent(this::publishDomainEvents);
    }
    
    /**
     * 刷新推流信息
     */
    @Transactional(rollbackFor = Exception.class)
    public StreamInfo refreshStreamInfo(Long liveId) {
        LiveId id = LiveId.of(liveId);
        StreamInfo streamInfo = liveDomainService.refreshStreamInfo(id);
        
        // 获取聚合并发布事件
        Optional<LiveAggregate> aggregateOpt = liveRepository.findById(id);
        aggregateOpt.ifPresent(this::publishDomainEvents);
        
        return streamInfo;
    }
    
    /**
     * 获取直播详情
     */
    public Optional<LiveDetailDto> getLiveDetail(Long liveId) {
        LiveId id = LiveId.of(liveId);
        return liveRepository.findById(id)
            .map(this::convertToDetailDto);
    }
    
    /**
     * 根据分类获取直播列表
     */
    public List<LiveListDto> getLivesByCategory(Long categoryId) {
        List<LiveAggregate> aggregates = liveRepository.findByCategoryId(categoryId);
        return aggregates.stream()
            .map(this::convertToListDto)
            .collect(Collectors.toList());
    }
    
    /**
     * 根据状态获取直播列表
     */
    public List<LiveListDto> getLivesByStatus(Long statusValue) {
        LiveStatus status = LiveStatus.of(statusValue);
        List<LiveAggregate> aggregates = liveRepository.findByStatus(status);
        return aggregates.stream()
            .map(this::convertToListDto)
            .collect(Collectors.toList());
    }
    
    /**
     * 删除直播
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteLive(Long liveId) {
        LiveId id = LiveId.of(liveId);
        
        // 检查是否可以删除
        LiveAggregate aggregate = liveRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("直播不存在: " + liveId));
        
        if (aggregate.getStatus().isLive()) {
            throw new IllegalStateException("正在直播中的内容不能删除");
        }
        
        liveRepository.delete(id);
        log.info("删除直播: liveId={}", liveId);
    }
    
    /**
     * 发布领域事件
     */
    private void publishDomainEvents(LiveAggregate aggregate) {
        List<DomainEvent> events = aggregate.getDomainEvents();
        for (DomainEvent event : events) {
            domainEventPublisher.publish(event);
        }
        aggregate.clearDomainEvents();
    }
    
    /**
     * 转换为详情DTO
     */
    private LiveDetailDto convertToDetailDto(LiveAggregate aggregate) {
        return LiveDetailDto.builder()
            .liveId(aggregate.getLiveId().getValue())
            .categoryId(aggregate.getLiveInfo().getCategoryId())
            .title(aggregate.getLiveInfo().getTitle())
            .coverImgUrl(aggregate.getLiveInfo().getCoverImgUrl())
            .planStartTime(aggregate.getLiveInfo().getPlanStartTime())
            .actualStartTime(aggregate.getLiveInfo().getActualStartTime())
            .actualEndTime(aggregate.getLiveInfo().getActualEndTime())
            .durationMinutes(aggregate.getLiveInfo().getDurationMinutes())
            .status(aggregate.getStatus().getNumericValue())
            .statusText(aggregate.getStatus().getValue().name())
            .description(aggregate.getLiveInfo().getDescription())
            .tagList(aggregate.getLiveInfo().getTagList())
            .viewCount(aggregate.getStatistics().getViewCount())
            .likeCount(aggregate.getStatistics().getLikeCount())
            .shareCount(aggregate.getStatistics().getShareCount())
            .maxOnlineCount(aggregate.getStatistics().getMaxOnlineCount())
            .currentOnlineCount(aggregate.getStatistics().getCurrentOnlineCount())
            .streamInfo(aggregate.getStreamInfo())
            .settings(aggregate.getSettings())
            .build();
    }
    
    /**
     * 转换为列表DTO
     */
    private LiveListDto convertToListDto(LiveAggregate aggregate) {
        return LiveListDto.builder()
            .liveId(aggregate.getLiveId().getValue())
            .title(aggregate.getLiveInfo().getTitle())
            .coverImgUrl(aggregate.getLiveInfo().getCoverImgUrl())
            .planStartTime(aggregate.getLiveInfo().getPlanStartTime())
            .status(aggregate.getStatus().getNumericValue())
            .statusText(aggregate.getStatus().getValue().name())
            .viewCount(aggregate.getStatistics().getViewCount())
            .likeCount(aggregate.getStatistics().getLikeCount())
            .currentOnlineCount(aggregate.getStatistics().getCurrentOnlineCount())
            .build();
    }
}
