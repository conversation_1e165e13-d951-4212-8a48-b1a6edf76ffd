package com.ydwl.live.exception;

import cn.hutool.core.text.CharSequenceUtil;

/**
 * 直播业务异常类
 * 
 * <p>直播模块的专用业务异常，继承自框架的RuntimeException</p>
 * <p>提供预定义的错误码和错误信息，便于统一处理和国际化</p>
 * 
 * <AUTHOR>
 * @version 2.1.4
 * @since 2025-01-XX
 */
public class LiveException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误码枚举
     */
    public enum ErrorCode {
        // 直播状态相关错误
        LIVE_NOT_FOUND("LIVE_001", "直播不存在"),
        LIVE_ALREADY_STARTED("LIVE_002", "直播已经开始"),
        LIVE_ALREADY_ENDED("LIVE_003", "直播已经结束"),
        LIVE_NOT_STARTED("LIVE_004", "直播尚未开始"),
        LIVE_IN_PROGRESS("LIVE_005", "直播正在进行中"),
        
        // 预约相关错误
        SCHEDULE_CONFLICT("SCHEDULE_001", "预约时间冲突"),
        SCHEDULE_EXPIRED("SCHEDULE_002", "预约已过期"),
        SCHEDULE_FULL("SCHEDULE_003", "预约人数已满"),
        SCHEDULE_NOT_OPEN("SCHEDULE_004", "预约尚未开放"),
        SCHEDULE_ALREADY_EXISTS("SCHEDULE_005", "已存在预约记录"),
        
        // 权限相关错误
        ACCESS_DENIED("ACCESS_001", "访问权限不足"),
        LOGIN_REQUIRED("ACCESS_002", "需要登录才能访问"),
        MEMBER_ONLY("ACCESS_003", "仅限会员访问"),
        SIGNUP_REQUIRED("ACCESS_004", "需要报名才能观看"),
        
        // 直播间功能相关错误
        CHAT_DISABLED("ROOM_001", "聊天功能已关闭"),
        GIFT_DISABLED("ROOM_002", "礼物功能已关闭"),
        USER_BANNED("ROOM_003", "用户已被禁言"),
        ANNOUNCEMENT_NOT_FOUND("ROOM_004", "公告不存在"),
        
        // 回放相关错误
        REPLAY_NOT_AVAILABLE("REPLAY_001", "回放不可用"),
        REPLAY_PROCESSING("REPLAY_002", "回放正在处理中"),
        REPLAY_EXPIRED("REPLAY_003", "回放已过期"),
        
        // 转码相关错误
        TRANSCODE_FAILED("TRANSCODE_001", "转码失败"),
        TRANSCODE_IN_PROGRESS("TRANSCODE_002", "转码正在进行中"),
        TRANSCODE_CONFIG_ERROR("TRANSCODE_003", "转码配置错误"),
        
        // 数据校验错误
        INVALID_TIME_RANGE("VALIDATION_001", "时间范围无效"),
        INVALID_CATEGORY("VALIDATION_002", "直播分类无效"),
        TITLE_ALREADY_EXISTS("VALIDATION_003", "直播标题已存在"),
        INVALID_STREAM_CONFIG("VALIDATION_004", "推流配置无效");

        private final String code;
        private final String message;

        ErrorCode(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public String getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * 构造函数 - 使用预定义错误码
     *
     * @param errorCode 错误码枚举
     */
    public LiveException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
    }

    /**
     * 构造函数 - 使用预定义错误码和自定义消息
     *
     * @param errorCode 错误码枚举
     * @param message 自定义错误消息
     */
    public LiveException(ErrorCode errorCode, String message) {
        super(message);
        this.code = errorCode.getCode();
    }

    /**
     * 构造函数 - 使用预定义错误码和参数格式化消息
     *
     * @param errorCode 错误码枚举
     * @param args 消息格式化参数
     */
    public LiveException(ErrorCode errorCode, Object... args) {
        super(CharSequenceUtil.format(errorCode.getMessage(), args));
        this.code = errorCode.getCode();
    }

    /**
     * 构造函数 - 使用自定义错误码和消息
     *
     * @param code 错误码
     * @param message 错误消息
     */
    public LiveException(String code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 构造函数 - 包含原始异常
     *
     * @param errorCode 错误码枚举
     * @param cause 原始异常
     */
    public LiveException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.code = errorCode.getCode();
    }

    /**
     * 获取错误码
     */
    public String getCode() {
        return code;
    }

    // 便捷的静态工厂方法

    /**
     * 创建直播不存在异常
     */
    public static LiveException liveNotFound(Long liveId) {
        return new LiveException(ErrorCode.LIVE_NOT_FOUND, "直播不存在，ID: {}", liveId);
    }

    /**
     * 创建直播已开始异常
     */
    public static LiveException liveAlreadyStarted(Long liveId) {
        return new LiveException(ErrorCode.LIVE_ALREADY_STARTED, "直播已经开始，ID: {}", liveId);
    }

    /**
     * 创建预约冲突异常
     */
    public static LiveException scheduleConflict(String timeRange) {
        return new LiveException(ErrorCode.SCHEDULE_CONFLICT, "预约时间冲突: {}", timeRange);
    }

    /**
     * 创建访问权限不足异常
     */
    public static LiveException accessDenied(String reason) {
        return new LiveException(ErrorCode.ACCESS_DENIED, "访问权限不足: {}", reason);
    }

    /**
     * 创建用户被禁言异常
     */
    public static LiveException userBanned(Long userId, String reason) {
        return new LiveException(ErrorCode.USER_BANNED, "用户已被禁言，用户ID: {}, 原因: {}", userId, reason);
    }

    /**
     * 创建回放不可用异常
     */
    public static LiveException replayNotAvailable(Long liveId, String reason) {
        return new LiveException(ErrorCode.REPLAY_NOT_AVAILABLE, "回放不可用，直播ID: {}, 原因: {}", liveId, reason);
    }

    /**
     * 创建转码失败异常
     */
    public static LiveException transcodeFailed(Long taskId, String details) {
        return new LiveException(ErrorCode.TRANSCODE_FAILED, "转码失败，任务ID: {}, 详情: {}", taskId, details);
    }

    /**
     * 创建数据校验异常
     */
    public static LiveException validationError(String field, String value, String rule) {
        return new LiveException(ErrorCode.INVALID_TIME_RANGE, "数据校验失败，字段: {}, 值: {}, 规则: {}", field, value, rule);
    }
} 