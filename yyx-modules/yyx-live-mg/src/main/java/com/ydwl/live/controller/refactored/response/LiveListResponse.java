package com.ydwl.live.controller.refactored.response;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 直播列表响应
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Data
@Builder
public class LiveListResponse {
    
    /**
     * 直播ID
     */
    private Long liveId;
    
    /**
     * 直播标题
     */
    private String title;
    
    /**
     * 直播封面图片URL
     */
    private String coverImgUrl;
    
    /**
     * 计划开始时间
     */
    private LocalDateTime planStartTime;
    
    /**
     * 直播状态
     */
    private Long status;
    
    /**
     * 状态文本
     */
    private String statusText;
    
    /**
     * 观看人数
     */
    private Long viewCount;
    
    /**
     * 点赞数
     */
    private Long likeCount;
    
    /**
     * 当前在线人数
     */
    private Long currentOnlineCount;
}
