package com.ydwl.live.listener;

import com.ydwl.LiveTranscoding.domain.vo.ResolutionConfig;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeRequestVo;
import com.ydwl.LiveTranscoding.domain.vo.TranscodeResponseVo;
import com.ydwl.live.event.TranscodeEvent;
import com.ydwl.live.config.TranscodeConfig;
import com.ydwl.LiveTranscoding.service.IUniversalTranscodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 转码事件监听器
 * 监听转码事件并调用统一转码服务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TranscodeEventListener {

    private final TranscodeConfig transcodeConfig;
    private final RestTemplate restTemplate;
    private final IUniversalTranscodeService universalTranscodeService;

    /**
     * 处理转码事件
     *
     * @param event 转码事件
     */
    @Async
    @EventListener
    public void handleTranscodeEvent(TranscodeEvent event) {
        try {
            Map<String, Object> params = event.getTranscodeParams();

            // 提取通用参数
            String bucket = (String) params.get("bucket");
            String object = (String) params.get("object");
            String bizId = (String) params.get("bizId");
            String callbackUrl = (String) params.get("callbackUrl");
            String method = (String) params.getOrDefault("method", "auto");

            @SuppressWarnings("unchecked")
            Map<String, String> resolutions = (Map<String, String>) params.get("resolutions");
            Integer segmentTime = (Integer) params.getOrDefault("segmentTime", 4);
            Long fileSize = (Long) params.get("fileSize");

            log.info("处理转码事件，方式: {}, BizId: {}, 文件: {}/{}", method, bizId, bucket, object);

            // 使用统一转码服务处理所有转码请求
            callUniversalTranscodeService(bucket, object, bizId, callbackUrl, resolutions, segmentTime, fileSize, method);

        } catch (Exception e) {
            log.error("处理转码事件失败", e);
        }
    }

    /**
     * 调用统一转码服务
     */
    private void callUniversalTranscodeService(String bucket, String object, String bizId,
                                             String callbackUrl, Map<String, String> resolutions,
                                             Integer segmentTime, Long fileSize, String method) {
        try {
            log.info("使用统一转码服务进行转码，方式: {}", method);

            // 构建统一转码请求
            TranscodeRequestVo request = new TranscodeRequestVo();
            request.setBucket(bucket);
            request.setObject(object);
            request.setBizId(bizId);
            request.setCallbackUrl(callbackUrl);
            request.setSegmentTime(segmentTime);
            request.setFileSize(fileSize);

            // 设置转码方式
            if (!"auto".equals(method)) {
                request.setForceMethod(method);
            }

            // 设置分辨率配置
            if (resolutions != null && !resolutions.isEmpty()) {
                ResolutionConfig resolutionConfig = new ResolutionConfig();
                resolutionConfig.setEnable480p("true".equals(resolutions.get("p480")));
                resolutionConfig.setEnable720p("true".equals(resolutions.get("p720")));
                resolutionConfig.setEnable1080p("true".equals(resolutions.get("p1080")));
                resolutionConfig.setEnable2k("true".equals(resolutions.get("p2k")));
                resolutionConfig.setEnable4k("true".equals(resolutions.get("p4k")));
                request.setResolutions(resolutionConfig);
            } else {
                // 使用默认分辨率配置
                ResolutionConfig resolutionConfig = new ResolutionConfig();
                resolutionConfig.setEnable720p(true);
                resolutionConfig.setEnable1080p(true);
                request.setResolutions(resolutionConfig);
            }

            // 调用统一转码服务
            TranscodeResponseVo response = universalTranscodeService.transcode(request);

            if (response.getStatus() == 1L) {
                log.info("统一转码任务提交成功，BizId: {}, 方式: {}, 消息: {}",
                        response.getBizId(), response.getMethod(), response.getMessage());
            } else {
                log.error("统一转码任务提交失败，BizId: {}, 消息: {}",
                        response.getBizId(), response.getMessage());
            }

        } catch (Exception e) {
            log.error("统一转码服务调用异常，BizId: {}", bizId, e);
        }
    }

    /**
     * HTTP调用转码服务（保留用于HTTP方式）
     */
    private void callTranscodeServiceByHttp(String bucket, String object, String bizId,
                                          String callbackUrl, Map<String, String> resolutions,
                                          Integer segmentTime) {
        try {
            log.info("使用HTTP方式调用转码服务");

            // 构建HTTP请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("bucket", bucket);
            requestBody.put("object", object);
            requestBody.put("bizId", bizId);
            requestBody.put("callbackUrl", callbackUrl);
            requestBody.put("resolutions", resolutions);
            requestBody.put("segmentTime", segmentTime);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送HTTP请求
            ResponseEntity<String> response = restTemplate.postForEntity(
                transcodeConfig.getServiceUrl() + "/video", entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("HTTP转码服务调用成功，BizId: {}, 响应: {}", bizId, response.getBody());
            } else {
                log.error("HTTP转码服务调用失败，状态码: {}, 响应: {}",
                    response.getStatusCode(), response.getBody());
            }

        } catch (Exception e) {
            log.error("HTTP转码服务调用异常，BizId: {}", bizId, e);
        }
    }
}
