package com.ydwl.live.domain.model.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 直播设置值对象
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
@EqualsAndHashCode
public class LiveSettings {
    
    /**
     * 是否需要报名
     */
    private final boolean signupRequired;
    
    /**
     * 是否启用回放
     */
    private final boolean replayEnabled;
    
    /**
     * 是否自动录制
     */
    private final boolean autoRecord;
    
    /**
     * 是否启用聊天
     */
    private final boolean chatEnabled;
    
    /**
     * 聊天延迟时间(秒)
     */
    private final Long chatDelay;
    
    /**
     * 是否启用礼物
     */
    private final boolean giftEnabled;
    
    /**
     * 默认画质
     */
    private final String defaultQuality;
    
    /**
     * 访问权限级别
     */
    private final AccessLevel accessLevel;
    
    private LiveSettings(boolean signupRequired, boolean replayEnabled, boolean autoRecord,
                        boolean chatEnabled, Long chatDelay, boolean giftEnabled,
                        String defaultQuality, AccessLevel accessLevel) {
        this.signupRequired = signupRequired;
        this.replayEnabled = replayEnabled;
        this.autoRecord = autoRecord;
        this.chatEnabled = chatEnabled;
        this.chatDelay = chatDelay;
        this.giftEnabled = giftEnabled;
        this.defaultQuality = defaultQuality;
        this.accessLevel = accessLevel;
    }
    
    /**
     * 创建默认设置
     */
    public static LiveSettings defaultSettings() {
        return new LiveSettings(
            false,              // 不需要报名
            true,               // 启用回放
            true,               // 自动录制
            true,               // 启用聊天
            0L,                 // 无聊天延迟
            true,               // 启用礼物
            "HD",               // 高清画质
            AccessLevel.PUBLIC  // 公开访问
        );
    }
    
    /**
     * 创建自定义设置
     */
    public static LiveSettings create(boolean signupRequired, boolean replayEnabled, 
                                     boolean autoRecord, boolean chatEnabled, Long chatDelay,
                                     boolean giftEnabled, String defaultQuality, AccessLevel accessLevel) {
        return new LiveSettings(signupRequired, replayEnabled, autoRecord, chatEnabled,
                               chatDelay, giftEnabled, defaultQuality, accessLevel);
    }
    
    /**
     * 更新聊天设置
     */
    public LiveSettings withChatSettings(boolean chatEnabled, Long chatDelay) {
        return new LiveSettings(signupRequired, replayEnabled, autoRecord, chatEnabled,
                               chatDelay, giftEnabled, defaultQuality, accessLevel);
    }
    
    /**
     * 更新录制设置
     */
    public LiveSettings withRecordSettings(boolean replayEnabled, boolean autoRecord) {
        return new LiveSettings(signupRequired, replayEnabled, autoRecord, chatEnabled,
                               chatDelay, giftEnabled, defaultQuality, accessLevel);
    }
    
    /**
     * 更新访问权限
     */
    public LiveSettings withAccessLevel(AccessLevel accessLevel) {
        return new LiveSettings(signupRequired, replayEnabled, autoRecord, chatEnabled,
                               chatDelay, giftEnabled, defaultQuality, accessLevel);
    }
    
    /**
     * 访问权限级别枚举
     */
    public enum AccessLevel {
        PUBLIC(0L, "公开"),
        LOGIN_REQUIRED(1L, "需登录"),
        SIGNUP_REQUIRED(2L, "需报名"),
        MEMBER_ONLY(3L, "会员专享");
        
        private final Long value;
        private final String description;
        
        AccessLevel(Long value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public Long getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static AccessLevel fromValue(Long value) {
            for (AccessLevel level : values()) {
                if (level.value.equals(value)) {
                    return level;
                }
            }
            throw new IllegalArgumentException("未知的访问权限级别: " + value);
        }
    }
}
