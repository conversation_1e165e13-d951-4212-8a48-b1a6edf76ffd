package com.ydwl.live.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 视频处理配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "video.process")
public class VideoProcessConfig {
    
    /**
     * 最大文件大小（MB）
     */
    private long maxFileSize = 2048;
    
    /**
     * 支持的视频格式
     */
    private String[] supportedFormats = {"mp4", "flv", "m3u8", "mov"};
    
    /**
     * 转码重试次数
     */
    private int maxRetryCount = 3;
    
    /**
     * 重试间隔（秒）
     */
    private int retryInterval = 300;
    
    /**
     * 转码超时时间（秒）
     */
    private int transcodeTimeout = 7200;
    
    /**
     * 进度更新间隔（秒）
     */
    private int progressUpdateInterval = 30;
    
    /**
     * 是否异步转码
     */
    private boolean asyncTranscode = true;
    
    /**
     * 转码任务队列大小
     */
    private int transcodeQueueSize = 100;
    
    /**
     * 转码线程池大小
     */
    private int transcodeThreadPoolSize = 5;
} 