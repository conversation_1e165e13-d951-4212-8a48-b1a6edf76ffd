package com.ydwl.live.service;

import java.util.List;

/**
 * 视频处理服务接口
 * 整合了各种视频处理功能，包括封面图生成、多帧截图等
 */
public interface IVideoProcessService {

    /**
     * 从视频中截取多帧图片
     *
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @param intervalSeconds 截图间隔（秒）
     * @param maxFrames 最大截图数量（可选）
     * @return 处理任务ID
     */
    String generateMultiFrameSnapshots(String objectKey, String bucket, String outputPrefix,
                                     int intervalSeconds, Integer maxFrames);

    /**
     * 从视频中截取第一帧作为封面图
     *
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @return 处理任务ID
     */
    String generateFirstFrameSnapshot(String objectKey, String bucket, String outputPrefix);

    /**
     * 更新回放记录的封面图
     *
     * @param liveId 直播ID
     * @param coverImgUrl 封面图URL
     * @return 更新成功返回true，否则返回false
     */
    boolean updateReplayCoverImage(Long liveId, String coverImgUrl);
    
    /**
     * 从视频中提取关键帧
     * 
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @param maxFrames 最大截图数量
     * @param width 输出图片宽度（可选）
     * @param height 输出图片高度（可选）
     * @return 处理任务ID
     */
    String generateKeyFrameSnapshots(String objectKey, String bucket, String outputPrefix, 
                                   Integer maxFrames, Integer width, Integer height);
    
    /**
     * 按视频百分比位置截取帧
     * 
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @param percentages 百分比位置数组，例如[0, 25, 50, 75, 100]
     * @return 处理任务ID列表
     */
    List<String> generatePercentageSnapshots(String objectKey, String bucket, String outputPrefix, int[] percentages);
    
    /**
     * 自定义视频截帧
     * 
     * @param objectKey 源视频在OSS中的对象键
     * @param bucket 存储桶名称
     * @param outputPrefix 输出图片前缀
     * @param startTimeMs 截图开始时间（毫秒）
     * @param format 输出图片格式（jpg/png）
     * @param num 截图数量
     * @param intervalMs 截图间隔（毫秒）
     * @param width 输出图片宽度（可选）
     * @param height 输出图片高度（可选）
     * @param scaleType 缩放方式（crop/stretch/fill/fit）
     * @return 处理任务ID
     */
    String generateCustomSnapshots(String objectKey, String bucket, String outputPrefix,
                                 int startTimeMs, String format, Integer num, Integer intervalMs,
                                 Integer width, Integer height, String scaleType);
}
