package com.ydwl.live.util;
import com.aliyun.live20161101.Client;
import com.aliyun.live20161101.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.esotericsoftware.minlog.Log;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 阿里云直播录制服务工具类
 *
 * 本类封装了阿里云直播录制相关的核心API操作，提供完整的录制配置管理功能。包含以下核心功能模块：
 * 1. 录制配置管理 - 支持添加/查询/删除直播流录制配置。
 * 2. 实时录制控制 - 提供开始/停止录制的即时控制指令。
 * 3. 录制内容查询 - 支持按时间范围查询录制文件信息。
 * 4. 回调配置管理 - 配置录制事件通知回调地址。
 * 5. 资源管理 - 管理OSS存储配置等录制相关资源。
 *
 * 【设计特点】
 * - 线程安全：使用Spring单例模式管理IAcsClient实例，确保多线程环境下的安全性。
 * - 配置灵活：支持通过Spring配置动态注入参数（如accessKeyId, accessKeySecret等）。
 * - 异常处理：统一捕获ClientException并记录日志，避免程序因异常崩溃。
 * - 可选参数处理：使用setOptionalField方法处理可为空参数，简化代码逻辑。
 *
 * 【典型使用场景】
 * 1. 创建直播频道时自动配置录制规则。
 * 2. 手动触发特定直播流的即时录制。
 * 3. 查询历史录制记录生成回放列表。
 * 4. 配置录制完成后的回调通知。
 *
 * 【注意事项】
 * - 需确保阿里云账号已开通直播服务和OSS服务。
 * - 回调地址需要处理签名验证（参考阿里云回调文档）。
 * - 录制文件存储路径遵循OSS对象命名规则。
 * - 注意API调用频率限制（默认500次/天）。
 *
 * @see <a href="https://help.aliyun.com/document_detail/45208.html">阿里云直播录制文档</a>
 * <AUTHOR>
 * @date 2025-03.26
 */
@Component
public class AliyunLiveRecordUtil {

    /**
     * 阿里云访问密钥ID
     */
    private static final String ACCESS_KEY_ID = "LTAI5tPMJW2hGdqLiobHACqr";

    /**
     * 阿里云访问密钥密码
     */
    private static final String ACCESS_KEY_SECRET = "******************************";

    /**
     * 地域ID
     */
    private static final String REGION_ID = "cn-beijing";
    /**
     * DomainName
     */
    private static final String DOMAIN_NAME = "play.live.ycyyx.com";
    private static final String OSS_ENDPOINT = "oss-cn-beijing.aliyuncs.com";
    private static final String OSS_BUCKET = "ydwl-live-recording";



    // 静态客户端实例（线程安全）
    private static final com.aliyun.live20161101.Client client;

    /*
      获取阿里云ACS客户端
      @return 配置好的ACS客户端实例
     * @implNote 双重校验锁确保线程安全的单例模式
     */
    static {
        try {
            // 创建客户端
            Config config = new Config()
                .setAccessKeyId(ACCESS_KEY_ID)
                .setAccessKeySecret(ACCESS_KEY_SECRET)
                .setRegionId(REGION_ID);
            client = new Client(config);
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize Aliyun Live Stream client", e);
        }
    }


    /**
     * 添加录制配置
     *
     * @param config 包含以下必要参数的请求对象：
     *               - domainName 推流域名（示例：push.example.com）
     *               - appName 应用名称（示例：liveapp）
     *               - ossEndpoint OSS端点（示例：oss-cn-shanghai.aliyuncs.com）
     *               - ossBucket OSS存储桶（示例：record-bucket）
     *               可选参数：streamName, startTime, endTime, onDemand, delayTime
     * @return 操作是否成功（根据RequestId是否存在判断）
     * @apiNote 该方法会深度拷贝录制格式配置以防止外部修改影响
     */
    public boolean addLiveRecordConfig(AddLiveAppRecordConfigRequest config) {
        // 创建基础请求对象
        AddLiveAppRecordConfigRequest request = new AddLiveAppRecordConfigRequest();

        // 设置必要参数
        request.setDomainName(config.getDomainName()); // 推流域名
        request.setAppName(config.getAppName());       // 应用名称
        request.setOssEndpoint(config.getOssEndpoint()); // OSS端点
        request.setOssBucket(config.getOssBucket());   // OSS存储桶

        // 设置可选参数（使用工具方法避免空值）
        setOptionalField(request::setStreamName, config.getStreamName()); // 流名称
        setOptionalField(request::setStartTime, config.getStartTime());   // 开始时间
        setOptionalField(request::setEndTime, config.getEndTime());       // 结束时间
        setOptionalField(request::setOnDemand, config.getOnDemand());     // 是否按需录制

        // 设置录制格式配置（深拷贝避免外部修改影响内部状态）
        setRecordFormats(request, config.getRecordFormat());
        setTranscodeRecordFormats(request, config.getTranscodeRecordFormat());
        setTranscodeTemplates(request, config.getTranscodeTemplates());

        try {
            // 执行API调用
            AddLiveAppRecordConfigResponse acsResponse = client.addLiveAppRecordConfig(request);
            Log.info("添加录制配置成功, RequestId: {}", String.valueOf(acsResponse.getStatusCode()));
            return acsResponse.getStatusCode() == 200; // 判断操作是否成功
        } catch (TeaException e) {
            // 统一异常处理（记录错误日志）
            Log.error("添加录制配置失败", e.getMessage());
            return false;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送实时录制控制指令
     *
     * @param domainName 推流域名（必须）
     * @param appName 应用名称（必须）
     * @param streamName 流名称（必须）
     * @param command 控制命令（"start"=开始录制，"stop"=停止录制）
     * @return 指令是否发送成功
     * @throws IllegalArgumentException 当command参数不合法时抛出
     * @apiNote 该API仅对正在推流的直播流有效
     */
    public boolean realTimeRecordCommand(String domainName, String appName, String streamName, String command) {
        // 参数校验（示例代码，实际应更严谨）
        if (!"start".equals(command) && !"stop".equals(command)) {
            throw new IllegalArgumentException("Invalid command: " + command);
        }

        RealTimeRecordCommandRequest request = new RealTimeRecordCommandRequest();
        request.setCommand(command);       // 控制命令
        request.setDomainName(domainName); // 推流域名
        request.setAppName(appName);       // 应用名称
        request.setStreamName(streamName); // 流名称

        try {
            RealTimeRecordCommandResponse acsResponse = client.realTimeRecordCommand(request);
            Log.info("实时录制指令成功, RequestId: {}", String.valueOf(acsResponse.getStatusCode()));
            return acsResponse.getStatusCode() == 200; // 判断操作是否成功
        } catch (TeaException e) {
            Log.error("实时录制指令失败", e.getMessage());
            return false;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询直播流录制内容列表
     *
     * @param domainName 推流域名
     * @param appName 应用名称
     * @param streamName 流名称
     * @param startTime 查询开始时间（UTC时间，格式：yyyy-MM-ddTHH:mm:ssZ）
     * @param endTime 查询结束时间（UTC时间，格式同上）
     * @return 录制内容信息列表（可能为null）
     * @apiNote 时间范围建议不超过24小时以保证查询效率
     */
    public DescribeLiveStreamRecordContentResponseBody.DescribeLiveStreamRecordContentResponseBodyRecordContentInfoList getLiveRecordContent(
            String domainName, String appName, String streamName, String startTime, String endTime) {
        DescribeLiveStreamRecordContentRequest request = new DescribeLiveStreamRecordContentRequest();
        request.setDomainName(domainName); // 推流域名
        request.setAppName(appName);       // 应用名称
        request.setStreamName(streamName); // 流名称
        request.setStartTime(startTime);   // 查询开始时间
        request.setEndTime(endTime);       // 查询结束时间

        try {
            DescribeLiveStreamRecordContentResponse response = client.describeLiveStreamRecordContent(request);
            return response.getBody().getRecordContentInfoList();
        } catch (TeaException e) {
            Log.error("查询直播流录制内容失败", e.getMessage());
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询域名下所有App录制配置
     *
     * @param domainName 推流域名
     * @param appName    应用名称（可选）
     * @param streamName 流名称（可选）
     * @param pageNum    分页页码（可选）
     * @param pageSize   分页大小（可选）
     * @param order      排序方式（可选）
     * @return 录制配置列表（可能为null）
     * @apiNote 分页参数建议合理设置以避免性能问题
     */
    public DescribeLiveRecordConfigResponseBody.DescribeLiveRecordConfigResponseBodyLiveAppRecordList getLiveRecordConfigs(
            String domainName, String appName, String streamName, Integer pageNum, Integer pageSize, String order) {
        DescribeLiveRecordConfigRequest request = new DescribeLiveRecordConfigRequest();
        request.setDomainName(domainName); // 推流域名

        // 设置可选参数
        setOptionalField(request::setAppName, appName);       // 应用名称
        setOptionalField(request::setStreamName, streamName); // 流名称
        setOptionalField(request::setPageNum, pageNum);       // 分页页码
        setOptionalField(request::setPageSize, pageSize);     // 分页大小
        setOptionalField(request::setOrder, order);           // 排序方式

        try {
            DescribeLiveRecordConfigResponse response = client.describeLiveRecordConfig(request);
            Log.info("查询录制配置成功, RequestId: {}", response.getStatusCode().toString());
            return response.getBody().getLiveAppRecordList(); // 返回录制配置列表
        } catch (TeaException e) {
            Log.error("查询录制配置失败", e.getMessage());
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 设置直播回调
     *
     * @param domainName 推流域名
     * @param notifyUrl 回调地址（需支持HTTPS）
     * @throws TeaException 当域名未备案或URL非法时抛出
     * @apiNote 回调地址需要实现签名验证（参见阿里云回调文档）
     *          默认设置OnDemandUrl为固定测试地址
     */
    public void setLiveStreamsNotifyUrlConfig(String domainName, String notifyUrl) throws Exception {
        AddLiveRecordNotifyConfigRequest request = new AddLiveRecordNotifyConfigRequest();
        request.setDomainName(domainName); // 推流域名
        request.setNotifyUrl(notifyUrl);   // 回调地址
        request.setOnDemandUrl("https://ycyyx.com/callback"); // 按需录制回调地址（示例地址）

        AddLiveRecordNotifyConfigResponse response = client.addLiveRecordNotifyConfig(request);
        Log.info("设置直播回调成功, RequestId: {}", response.getStatusCode().toString());
    }

    /**
     * 删除直播回调
     *
     * @param domainName 推流域名
     * @throws TeaException 当API调用出现客户端错误时抛出
     * @apiNote 敏感操作！删除后会导致录制回调功能失效
     */
    public void deleteLiveStreamsNotifyUrlConfig(String domainName) throws Exception {
        DeleteLiveStreamsNotifyUrlConfigRequest request = new DeleteLiveStreamsNotifyUrlConfigRequest();
        request.setDomainName(domainName); // 推流域名

        DeleteLiveStreamsNotifyUrlConfigResponse response = client.deleteLiveStreamsNotifyUrlConfig(request);
        Log.info("删除直播回调成功, RequestId: {}", response.getStatusCode().toString());
    }

    /**
     * 查询域名下所有App录制配置
     *
     * @param domainName 推流域名
     * @return 录制通知配置响应对象
     * @throws TeaException 当API调用出现客户端错误时抛出
     */
    public static DescribeLiveRecordNotifyConfigResponse describeLiveRecordNotifyConfig(String domainName) throws Exception {
        DescribeLiveRecordNotifyConfigRequest request = new DescribeLiveRecordNotifyConfigRequest();
        request.setDomainName(domainName); // 推流域名

        DescribeLiveRecordNotifyConfigResponse response = client.describeLiveRecordNotifyConfig(request);
        Log.info("查询录制通知配置成功, RequestId: {}", String.valueOf(response.getBody().getLiveRecordNotifyConfig()));
        return response; // 返回录制通知配置响应
    }


    /**
     * 设置可选字段的通用方法
     *
     * @param setter 字段设置方法引用（例如request::setStreamName）
     * @param value 字段值
     * @param <T> 字段类型
     * @implNote 用于避免空指针异常和冗余参数设置
     *           仅当value非空时执行设置操作
     */
    private <T> void setOptionalField(java.util.function.Consumer<T> setter, T value) {
        if (value != null) {
            setter.accept(value); // 设置字段值
        }
    }

    /**
     * 设置录制格式
     *
     * @param request 请求对象
     * @param formats 录制格式列表
     * @implNote 深拷贝录制格式配置以防止外部修改影响内部状态
     */
    private void setRecordFormats(AddLiveAppRecordConfigRequest request, List<AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestRecordFormat> formats) {
        if (formats != null && !formats.isEmpty()) {
            List<AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestRecordFormat> recordFormats = new ArrayList<>();
            for (AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestRecordFormat format : formats) {
                AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestRecordFormat recordFormat = new AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestRecordFormat();
                recordFormat.setSliceDuration(format.getSliceDuration());         // 设置切片时长
                recordFormat.setSliceOssObjectPrefix(format.getSliceOssObjectPrefix()); // 设置切片OSS前缀
                recordFormat.setCycleDuration(format.getCycleDuration());         // 设置周期时长
                recordFormat.setOssObjectPrefix(format.getOssObjectPrefix());     // 设置OSS前缀
                recordFormat.setFormat(format.getFormat());                       // 设置格式
                recordFormats.add(recordFormat);
            }
            request.setRecordFormat(recordFormats); // 正确设置录制格式列表
        }
    }

    /**
     * 设置转码录制格式
     *
     * @param request 请求对象
     * @param formats 转码录制格式列表
     * @implNote 深拷贝转码录制格式配置以防止外部修改影响内部状态
     */
    private void setTranscodeRecordFormats(AddLiveAppRecordConfigRequest request, List<AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestTranscodeRecordFormat> formats) {
        if (formats != null && !formats.isEmpty()) {
            List<AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestTranscodeRecordFormat> transcodeFormats = new ArrayList<>();
            for (AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestTranscodeRecordFormat format : formats) {
                AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestTranscodeRecordFormat transcodeFormat = new AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestTranscodeRecordFormat();
                transcodeFormat.setSliceDuration(format.getSliceDuration());         // 设置切片时长
                transcodeFormat.setSliceOssObjectPrefix(format.getSliceOssObjectPrefix()); // 设置切片OSS前缀
                transcodeFormat.setCycleDuration(format.getCycleDuration());         // 设置周期时长
                transcodeFormat.setOssObjectPrefix(format.getOssObjectPrefix());     // 设置OSS前缀
                transcodeFormat.setFormat(format.getFormat());                       // 设置格式
                transcodeFormats.add(transcodeFormat);
            }
            request.setTranscodeRecordFormat(transcodeFormats); // 设置转码录制格式列表
        }
    }

    /**
     * 设置转码模板
     *
     * @param request 请求对象
     * @param templates 转码模板列表
     * @implNote 仅当模板列表非空时设置
     */
    private void setTranscodeTemplates(AddLiveAppRecordConfigRequest request, List<String> templates) {
        if (templates != null && !templates.isEmpty()) {
            request.setTranscodeTemplates(templates); // 设置转码模板列表
        }
    }


    /**
     * 查询直播录制回调记录
     *
     * @param request 查询请求对象
     * @return 录制通知记录响应对象
     * @throws TeaException 当API调用出现客户端错误时抛出
     */
    public static DescribeLiveRecordNotifyConfigResponse describeLiveRecordNotifyRecords(DescribeLiveRecordNotifyConfigRequest request) throws Exception {
        DescribeLiveRecordNotifyConfigResponse response = client.describeLiveRecordNotifyConfig(request);
        Log.info("查询录制通知记录成功, RequestId: {}", response.getStatusCode().toString());
        return response; // 返回录制通知记录响应
    }

//    public static void main(String[] args) throws Exception {
//        DescribeLiveRecordNotifyConfigResponse describeLiveRecordNotifyConfigResponse = describeLiveRecordNotifyConfig(DOMAIN_NAME);
//        Integer statusCode = describeLiveRecordNotifyConfigResponse.getStatusCode();
//        DescribeLiveRecordNotifyConfigResponseBody.DescribeLiveRecordNotifyConfigResponseBodyLiveRecordNotifyConfig liveRecordNotifyConfig = describeLiveRecordNotifyConfigResponse.getBody().getLiveRecordNotifyConfig();
//        System.out.println("查询录制通知配置成功, RequestId: {}" + liveRecordNotifyConfig.notifyUrl + statusCode);
//        System.out.println("1"+liveRecordNotifyConfig.getDomainName());
//        System.out.println("2"+liveRecordNotifyConfig.getNotifyUrl());
//        System.out.println("3"+liveRecordNotifyConfig.getOnDemandUrl());
//        System.out.println("4"+liveRecordNotifyConfig.getNeedStatusNotify());
//
//
//    }
public static void main(String[] args) throws Exception {

    AddLiveAppRecordConfigRequest addLiveAppRecordConfigRequest=new AddLiveAppRecordConfigRequest();
    addLiveAppRecordConfigRequest.setAppName("ydwl_live");
    addLiveAppRecordConfigRequest.setStreamName("*");
    addLiveAppRecordConfigRequest.setDomainName(DOMAIN_NAME);
    addLiveAppRecordConfigRequest.setOssEndpoint(OSS_ENDPOINT);
    addLiveAppRecordConfigRequest.setOssBucket(OSS_BUCKET);
    addLiveAppRecordConfigRequest.setOnDemand(1);

    List<AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestRecordFormat> formatList = new ArrayList<>();

    AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestRecordFormat m3u8Format=new AddLiveAppRecordConfigRequest.AddLiveAppRecordConfigRequestRecordFormat();
    m3u8Format.setFormat("mp4");
    m3u8Format.setCycleDuration(7200);
    //OSS存储的录制文件名
    m3u8Format.setOssObjectPrefix("record/{AppName}/{StreamName}/{EscapedStartTime}_{EscapedEndTime}-{StreamName}");
    formatList.add(m3u8Format);
    addLiveAppRecordConfigRequest.setRecordFormat(formatList);
    AddLiveAppRecordConfigResponse addLiveAppRecordConfigResponse = null;
    AddLiveAppRecordConfigResponse addLiveAppRecordConfigResponse1 = client.addLiveAppRecordConfig(addLiveAppRecordConfigRequest);
    Log.info("添加录制配置成功, RequestId: {}", String.valueOf(addLiveAppRecordConfigResponse1.getStatusCode()));

}
}
