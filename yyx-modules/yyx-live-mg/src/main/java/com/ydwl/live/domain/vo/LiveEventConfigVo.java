package com.ydwl.live.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ydwl.common.excel.annotation.ExcelDictFormat;
import com.ydwl.common.excel.convert.ExcelDictConvert;
import com.ydwl.live.domain.LiveEventConfig;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 事件配置视图对象 live_event_config
 *
 * <AUTHOR> <PERSON>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveEventConfig.class)
public class LiveEventConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @ExcelProperty(value = "配置ID")
    private Long id;

    /**
     * 事件类型（live）
     */
    @ExcelProperty(value = "事件类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "l=ive")
    private String eventType;

    /**
     * 事件ID
     */
    @ExcelProperty(value = "事件ID")
    private Long eventId;

    /**
     * 报名开始时间
     */
    @ExcelProperty(value = "报名开始时间")
    private Date signupStart;

    /**
     * 报名截止时间
     */
    @ExcelProperty(value = "报名截止时间")
    private Date signupEnd;

    /**
     * 最大参与人数
     */
    @ExcelProperty(value = "最大参与人数")
    private Long maxParticipants;

    /**
     * 报名需填字段配置
     */
    @ExcelProperty(value = "报名需填字段配置")
    private String signupFields;


}
