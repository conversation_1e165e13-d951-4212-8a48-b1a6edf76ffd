package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveReplayVo;
import com.ydwl.live.domain.bo.LiveReplayBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 直播回放Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveReplayService {

    /**
     * 查询直播回放
     *
     * @param id 主键
     * @return 直播回放
     */
    LiveReplayVo queryById(Long id);

    /**
     * 分页查询直播回放列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播回放分页列表
     */
    TableDataInfo<LiveReplayVo> queryPageList(LiveReplayBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的直播回放列表
     *
     * @param bo 查询条件
     * @return 直播回放列表
     */
    List<LiveReplayVo> queryList(LiveReplayBo bo);

    /**
     * 新增直播回放
     *
     * @param bo 直播回放
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveReplayBo bo);

    /**
     * 修改直播回放
     *
     * @param bo 直播回放
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveReplayBo bo);

    /**
     * 校验并批量删除直播回放信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
