package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveUserSubscribeBo;
import com.ydwl.live.domain.vo.LiveUserSubscribeVo;
import com.ydwl.live.domain.LiveUserSubscribe;
import com.ydwl.live.mapper.LiveUserSubscribeMapper;
import com.ydwl.live.service.ILiveUserSubscribeService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 用户订阅Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveUserSubscribeServiceImpl implements ILiveUserSubscribeService {

    private final LiveUserSubscribeMapper baseMapper;

    /**
     * 查询用户订阅
     *
     * @param id 主键
     * @return 用户订阅
     */
    @Override
    public LiveUserSubscribeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户订阅列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户订阅分页列表
     */
    @Override
    public TableDataInfo<LiveUserSubscribeVo> queryPageList(LiveUserSubscribeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveUserSubscribe> lqw = buildQueryWrapper(bo);
        Page<LiveUserSubscribeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户订阅列表
     *
     * @param bo 查询条件
     * @return 用户订阅列表
     */
    @Override
    public List<LiveUserSubscribeVo> queryList(LiveUserSubscribeBo bo) {
        LambdaQueryWrapper<LiveUserSubscribe> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveUserSubscribe> buildQueryWrapper(LiveUserSubscribeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveUserSubscribe> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveUserSubscribe::getId);
        lqw.eq(bo.getUserId() != null, LiveUserSubscribe::getUserId, bo.getUserId());
        lqw.eq(bo.getLiveId() != null, LiveUserSubscribe::getLiveId, bo.getLiveId());
        lqw.eq(bo.getSubscribeTime() != null, LiveUserSubscribe::getSubscribeTime, bo.getSubscribeTime());
        lqw.eq(bo.getIsNotified() != null, LiveUserSubscribe::getIsNotified, bo.getIsNotified());
        return lqw;
    }

    /**
     * 新增用户订阅
     *
     * @param bo 用户订阅
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveUserSubscribeBo bo) {
        LiveUserSubscribe add = MapstructUtils.convert(bo, LiveUserSubscribe.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户订阅
     *
     * @param bo 用户订阅
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveUserSubscribeBo bo) {
        LiveUserSubscribe update = MapstructUtils.convert(bo, LiveUserSubscribe.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveUserSubscribe entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户订阅信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
