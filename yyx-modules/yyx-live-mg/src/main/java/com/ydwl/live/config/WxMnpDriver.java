package com.ydwl.live.config;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;


/**
 * 微信基础驱动
 */
@Component
@Configuration
public class WxMnpDriver {

    private static WxMaService wxMaService;


    /**
     * 微信小程序依赖注入
     */
    @Resource
    public void setWxMaService(WxMaService wxMaService) {
        WxMnpDriver.wxMaService = wxMaService;
    }


    /**
     * 微信小程序
     *
     * @return WxMaService
     * <AUTHOR>
     */
    @Bean
    public static WxMaService mnp() {
        WxMaDefaultConfigImpl wxConfig = new WxMaDefaultConfigImpl();
        wxConfig.setAppid("wxcec8d03702edff0b");
        wxConfig.setSecret("97dfbdda6aa3935d9bfe672118ba8f93");
        WxMaService wxMaService = new WxMaServiceImpl();
        wxMaService.setWxMaConfig(wxConfig);
        return wxMaService;
    }


    /**
     * Sha1算法
     *
     * @param orderedString 字符串
     * @return String
     * @throws Exception 异常
     * <AUTHOR>
     */
    private static String sha1(String orderedString) throws Exception {
        String ciphertext;
        MessageDigest md = MessageDigest.getInstance("SHA-1");
        byte[] digest = md.digest(orderedString.getBytes());
        ciphertext = byteToStr(digest);
        return ciphertext.toLowerCase();
    }

    /**
     * 字节转字符
     *
     * @param byteArray 字节
     * @return String
     * <AUTHOR>
     */
    private static String byteToStr(byte[] byteArray) {
        StringBuilder strDigest = new StringBuilder();
        for (byte b : byteArray) {
            strDigest.append(byteToHexStr(b));
        }
        return strDigest.toString();
    }

    /**
     * 字节转Hex
     *
     * @param mByte 字节
     * @return String
     * <AUTHOR>
     */
    private static String byteToHexStr(byte mByte) {
        char[] Digit = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        char[] tempArr = new char[2];
        tempArr[0] = Digit[(mByte >>> 4) & 0X0F];
        tempArr[1] = Digit[mByte & 0X0F];
        return new String(tempArr);
    }

}
