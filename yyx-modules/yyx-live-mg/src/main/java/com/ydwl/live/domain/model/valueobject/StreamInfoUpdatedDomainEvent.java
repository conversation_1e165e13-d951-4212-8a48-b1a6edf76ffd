package com.ydwl.live.domain.model.valueobject;

import lombok.Getter;

/**
 * 推流信息更新领域事件
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
public class StreamInfoUpdatedDomainEvent extends DomainEvent {
    
    private final StreamInfo streamInfo;
    
    public StreamInfoUpdatedDomainEvent(LiveId liveId, StreamInfo streamInfo) {
        super(liveId);
        this.streamInfo = streamInfo;
    }
    
    @Override
    public String getEventType() {
        return "StreamInfoUpdated";
    }
}
