package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveTransaction;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 交易记录业务对象 live_transaction
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveTransaction.class, reverseConvertGenerate = false)
public class LiveTransactionBo extends BaseEntity {

    /**
     * 交易记录ID
     */
    @NotNull(message = "交易记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 关联ID（订单/礼物记录）
     */
    private Long relatedId;

    /**
     * 交易类型（1-充值，2-消费，3-收入，4-退款）
     */
    @NotNull(message = "交易类型（1-充值，2-消费，3-收入，4-退款）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long type;

    /**
     * 交易金额
     */
    @NotNull(message = "交易金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 交易后余额
     */
    @NotNull(message = "交易后余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long balance;

    /**
     * 交易详情
     */
    private String detail;

    /**
     * 目标类型
     */
    private Long targetType;

    /**
     * 目标ID
     */
    private Long targetId;


}
