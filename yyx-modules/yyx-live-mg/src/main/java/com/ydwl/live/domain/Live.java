package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 直播信息对象 live
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live")
public class Live extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 直播ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 直播分类ID
     */
    private Long categoryId;

    /**
     * 直播标题
     */
    private String title;

    /**
     * 直播封面图片URL
     */
    private String coverImgUrl;

    /**
     * 计划开始时间
     */
    private Date planStartTime;

    /**
     * 实际开始时间
     */
    private Date actualStartTime;

    /**
     * 实际结束时间
     */
    private Date actualEndTime;

    /**
     * 实际直播时长(分钟)
     */
    private Long durationMinutes;

    /**
     * 直播状态(0-未开始,1-直播中,2-已结束,3-回放中)
     */
    private Long status;

    /**
     * 关联推流ID
     */
    private Long streamId;

    /**
     * 是否需要报名(0-否,1-是)
     */
    private Long settingSignupRequired;

    /**
     * 是否启用回放(0-否,1-是)
     */
    private Long settingReplayEnabled;

    /**
     * 是否自动录制(0-否,1-是)
     */
    private Long settingAutoRecord;

    /**
     * 是否启用聊天(0-否,1-是)
     */
    private Long settingChatEnabled;

    /**
     * 聊天延迟时间(秒)
     */
    private Long settingChatDelay;

    /**
     * 是否启用礼物(0-否,1-是)
     */
    private Long settingGiftEnabled;

    /**
     * 默认画质(HD-高清,SD-标清)
     */
    private String settingDefaultQuality;

    /**
     * 访问权限(0-公开,1-需登录,2-需报名,3-会员专享)
     */
    private Long settingAccessLevel;

    /**
     * 直播描述
     */
    private String description;

    /**
     * 标签列表,多个标签用逗号分隔
     */
    private String tagList;

    /**
     * 录制视频存储地址
     */
    private String recordVideoUrl;

    /**
     * 观看人数
     */
    private Long viewCount;

    /**
     * 点赞数
     */
    private Long likeCount;

    /**
     * 分享数
     */
    private Long shareCount;

    /**
     * 最大同时在线人数
     */
    private Long maxOnlineCount;

    /**
     * 是否精选(0-否,1-是)
     */
    private Long isFeatured;

    /**
     * 是否有回放(0-无,1-有)
     */
    private Long hasReplay;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
