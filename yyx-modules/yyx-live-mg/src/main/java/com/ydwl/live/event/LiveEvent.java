package com.ydwl.live.event;

import lombok.Getter;
import java.time.LocalDateTime;

/**
 * 直播事件基类
 * <p>所有直播相关事件的基类，用于事件驱动架构设计</p>
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Getter
public abstract class LiveEvent {
    
    /**
     * 事件ID
     */
    private final String eventId;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime timestamp;
    
    /**
     * 事件相关的直播ID
     */
    private final Long liveId;

    protected LiveEvent(Long liveId) {
        this.eventId = java.util.UUID.randomUUID().toString();
        this.timestamp = LocalDateTime.now();
        this.liveId = liveId;
    }
} 