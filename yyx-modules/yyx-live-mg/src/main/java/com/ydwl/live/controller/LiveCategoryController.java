package com.ydwl.live.controller;

import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveCategoryVo;
import com.ydwl.live.domain.bo.LiveCategoryBo;
import com.ydwl.live.service.ILiveCategoryService;

/**
 * 直播分类
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/category")
public class LiveCategoryController extends BaseController {

    private final ILiveCategoryService liveCategoryService;

    /**
     * 查询直播分类列表
     */
    @SaCheckPermission("live:category:list")
    @GetMapping("/list")
    public R<List<LiveCategoryVo>> list(LiveCategoryBo bo) {
        List<LiveCategoryVo> list = liveCategoryService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出直播分类列表
     */
    @SaCheckPermission("live:category:export")
    @Log(title = "直播分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveCategoryBo bo, HttpServletResponse response) {
        List<LiveCategoryVo> list = liveCategoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "直播分类", LiveCategoryVo.class, response);
    }

    /**
     * 获取直播分类详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:category:query")
    @GetMapping("/{id}")
    public R<LiveCategoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveCategoryService.queryById(id));
    }

    /**
     * 新增直播分类
     */
    @SaCheckPermission("live:category:add")
    @Log(title = "直播分类", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveCategoryBo bo) {
        return toAjax(liveCategoryService.insertByBo(bo));
    }

    /**
     * 修改直播分类
     */
    @SaCheckPermission("live:category:edit")
    @Log(title = "直播分类", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveCategoryBo bo) {
        return toAjax(liveCategoryService.updateByBo(bo));
    }

    /**
     * 删除直播分类
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:category:remove")
    @Log(title = "直播分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveCategoryService.deleteWithValidByIds(List.of(ids), true));
    }
    
    /**
     * 获取分类树形结构
     */
    @GetMapping("/tree")
    public R<List<Map<String, Object>>> getCategoryTree(@RequestParam(required = false) Long status) {
        List<Map<String, Object>> tree = liveCategoryService.getCategoryTree(status);
        return R.ok(tree);
    }
    
    /**
     * 获取指定层级的分类
     */
    @GetMapping("/level/{level}")
    public R<List<LiveCategoryVo>> getCategoriesByLevel(
            @PathVariable @NotNull(message = "层级不能为空") Long level,
            @RequestParam(required = false) Long status) {
        List<LiveCategoryVo> list = liveCategoryService.getByLevel(level, status);
        return R.ok(list);
    }
    
    /**
     * 获取子分类
     */
    @GetMapping("/children")
    public R<List<LiveCategoryVo>> getChildCategories(
            @RequestParam(required = false) Long parentId,
            @RequestParam(required = false) Long status) {
        List<LiveCategoryVo> list = liveCategoryService.getChildren(parentId, status);
        return R.ok(list);
    }
    
    /**
     * 获取热门分类
     */
    @GetMapping("/hot")
    public R<List<LiveCategoryVo>> getHotCategories(@RequestParam(required = false, defaultValue = "10") Integer limit) {
        List<LiveCategoryVo> list = liveCategoryService.getHotCategories(limit);
        return R.ok(list);
    }
    
    /**
     * 增加分类浏览次数
     */
    @PutMapping("/view/{id}")
    public R<Void> incrementViewCount(@PathVariable @NotNull(message = "分类ID不能为空") Long id) {
        return toAjax(liveCategoryService.incrementViewCount(id));
    }
}
