package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveUserSubscribeVo;
import com.ydwl.live.domain.bo.LiveUserSubscribeBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 用户订阅Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveUserSubscribeService {

    /**
     * 查询用户订阅
     *
     * @param id 主键
     * @return 用户订阅
     */
    LiveUserSubscribeVo queryById(Long id);

    /**
     * 分页查询用户订阅列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户订阅分页列表
     */
    TableDataInfo<LiveUserSubscribeVo> queryPageList(LiveUserSubscribeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户订阅列表
     *
     * @param bo 查询条件
     * @return 用户订阅列表
     */
    List<LiveUserSubscribeVo> queryList(LiveUserSubscribeBo bo);

    /**
     * 新增用户订阅
     *
     * @param bo 用户订阅
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveUserSubscribeBo bo);

    /**
     * 修改用户订阅
     *
     * @param bo 用户订阅
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveUserSubscribeBo bo);

    /**
     * 校验并批量删除用户订阅信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
