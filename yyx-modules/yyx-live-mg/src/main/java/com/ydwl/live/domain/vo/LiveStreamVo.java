package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveStream;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 推流信息视图对象 live_stream
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveStream.class)
public class LiveStreamVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 推流信息ID
     */
    @ExcelProperty(value = "推流信息ID")
    private Long id;

    /**
     * 直播ID
     */
    @ExcelProperty(value = "直播ID")
    private Long liveId;

    /**
     * 推流地址
     */
    @ExcelProperty(value = "推流地址")
    private String pushUrl;

    /**
     * 推流密钥
     */
    @ExcelProperty(value = "推流密钥")
    private String pushKey;

    /**
     * 状态(0-创建中,1-正常,2-异常)
     */
    @ExcelProperty(value = "状态(0-创建中,1-正常,2-异常)")
    private Long streamStatus;


}
