package com.ydwl.live.util;

import com.ydwl.live.config.LiveStreamConfig;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 阿里云直播推流与播放URL生成工具类
 * 提供推流地址和播放地址的生成功能
 * 
 * 使用示例:
 * 
 * // 生成推流URL（自动使用推流密钥）
 * PushUrlRequest pushRequest = new PushUrlRequest("STREAM_123", "live_app");
 * PushInfo pushResult = AliLiveUtil.generatePushUrl(pushRequest);
 *
 * // 生成播放URL（显式使用播放密钥）
 * PlayUrlRequest playRequest = new PlayUrlRequest("STREAM_123", 86400, "live_app");
 * PlayInfo playResult = AliLiveUtil.generatePlayUrls(playRequest);
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Component
public class AliLiveUtil {

    private static final Logger logger = LoggerFactory.getLogger(AliLiveUtil.class);

    // 配置实例，通过Spring注入
    private static LiveStreamConfig config;

    // 构造函数注入配置
    public AliLiveUtil(LiveStreamConfig config) {
        AliLiveUtil.config = config;
    }

    // 请求参数类
    @Getter
    public static class PushUrlRequest {
        private final long liveStreamId;
        private final String appName;

        public PushUrlRequest(String liveStreamId, String appName) {
            validateParams(liveStreamId, appName);
            this.liveStreamId = Long.parseLong(liveStreamId);
            this.appName = appName;
        }
    }

    @Getter
    public static class PlayUrlRequest {
        private final String liveStreamId;
        private final long expireSeconds;
        private final String appName;

        public PlayUrlRequest(String liveStreamId, long expireSeconds, String appName) {
            validateParams(liveStreamId, appName);
            this.liveStreamId = liveStreamId;
            this.expireSeconds = expireSeconds;
            this.appName = appName;
        }
    }

    // 响应结果类
    @Getter
    public static class PushInfo {
        private final String pushUrl;
        private final String authKey;

        public PushInfo(String pushUrl, String authKey) {
            this.pushUrl = pushUrl;
            this.authKey = authKey;
        }
    }

    @Getter
    public static class PlayInfo {
        private final String playUrls;

        public PlayInfo(String playUrls) {
            this.playUrls = playUrls;
        }
    }

    // 核心方法
    /**
     * 生成推流URL和鉴权密钥（使用推流专用密钥）
     * @param request 推流请求参数
     * @return 推流信息对象
     */
    public static PushInfo generatePushUrl(PushUrlRequest request) {
        String streamName = buildStreamName(String.valueOf(request.getLiveStreamId()));
        // 使用推流密钥生成鉴权
        String authKey = generateAuthKey(request.getAppName(), streamName, config.getPushSecretKey());
        String pushUrl = buildRtmpUrl(request.getAppName(), streamName, authKey);

        return new PushInfo(pushUrl, authKey);
    }

    /**
     * 生成带时效的播放URL集合（使用播放专用密钥）
     * @param request 播放请求参数
     * @return 播放URL信息对象
     */
    public static PlayInfo generatePlayUrls(PlayUrlRequest request) {
        String streamName = buildStreamName(request.getLiveStreamId());
        long expireTime = calculateExpireTime(request.getExpireSeconds());

        // 使用播放密钥生成鉴权
        String authKeyRtmp = generateAuthKey(request.getAppName(), streamName, expireTime, config.getPlaySecretKey());
        String authKeyHttp = generateAuthKey(request.getAppName(), streamName + ".flv", expireTime, config.getPlaySecretKey());
        String authKeyM3u8 = generateAuthKey(request.getAppName(), streamName + ".m3u8", expireTime, config.getPlaySecretKey());
        String authKeyRts = generateAuthKey(request.getAppName(), streamName, expireTime, config.getPlaySecretKey());

        return new PlayInfo(buildAllProtocolsUrl(
            request.getAppName(),
            streamName,
            authKeyRtmp,
            authKeyHttp,
            authKeyM3u8,
            authKeyRts
        ));
    }

    // 辅助方法
    public static String buildStreamName(String liveStreamId) {
        return liveStreamId;
    }

    private static String buildRtmpUrl(String appName, String streamName, String authKey) {
        return String.format(
            "rtmp://%s/%s/%s?auth_key=%s",
            config.getPushDomain(),
            appName,
            streamName,
            authKey
        );
    }

    private static long calculateExpireTime(long expireSeconds) {
        return System.currentTimeMillis() / 1000 + expireSeconds;
    }

    // 推流鉴权密钥生成（默认1小时过期）
    private static String generateAuthKey(String appName, String streamName, String secretKey) {
        long expireTime = System.currentTimeMillis() / 1000 + 3600;
        return generateAuthKey(appName, streamName, expireTime, secretKey);
    }

    // 公共鉴权生成方法
    private static String generateAuthKey(
        String appName,
        String path,
        long expireTime,
        String secretKey
    ) {
        String fullPath = "/" + appName + "/" + path;
        String authString = String.format("%s-%d-0-0-%s", fullPath, expireTime, secretKey);
        String hash = md5Hex(authString);
        return String.format("%d-0-0-%s", expireTime, hash);
    }

    private static String md5Hex(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return String.format("%032x", new BigInteger(1, bytes));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    private static String buildAllProtocolsUrl(
        String appName,
        String streamName,
        String authKeyRtmp,
        String authKeyHttp,
        String authKeyM3u8,
        String authKeyRts
    ) {
        return String.format(
            "RTMP: rtmp://%s/%s/%s?auth_key=%s\n" +
            "FLV: http://%s/%s/%s.flv?auth_key=%s\n" +
            "M3U8: http://%s/%s/%s.m3u8?auth_key=%s\n" +
            "RTS: artc://%s/%s/%s?auth_key=%s",
            config.getPlayDomain(), appName, streamName, authKeyRtmp,
            config.getPlayDomain(), appName, streamName, authKeyHttp,
            config.getPlayDomain(), appName, streamName, authKeyM3u8,
            config.getPlayDomain(), appName, streamName, authKeyRts
        );
    }

    private static void validateParams(String liveStreamId, String appName) {
        if (liveStreamId == null || liveStreamId.isEmpty()) {
            throw new IllegalArgumentException("直播流ID不能为空");
        }
        if (appName == null || appName.isEmpty()) {
            throw new IllegalArgumentException("应用名称不能为空");
        }
    }
}
