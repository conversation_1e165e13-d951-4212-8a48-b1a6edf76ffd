package com.ydwl.live.domain.model.valueobject;

import com.ydwl.live.enums.LiveStatusEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;

/**
 * 直播状态值对象
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
@EqualsAndHashCode
public class LiveStatus {
    
    private final LiveStatusEnum value;
    
    /**
     * 状态转换规则
     */
    private static final Map<LiveStatusEnum, EnumSet<LiveStatusEnum>> TRANSITION_RULES = Map.of(
        LiveStatusEnum.NOT_STARTED, EnumSet.of(LiveStatusEnum.LIVE, LiveStatusEnum.ERROR),
        LiveStatusEnum.LIVE, EnumSet.of(LiveStatusEnum.ENDED, LiveStatusEnum.ERROR),
        LiveStatusEnum.ENDED, EnumSet.of(LiveStatusEnum.REPLAY),
        LiveStatusEnum.REPLAY, EnumSet.noneOf(LiveStatusEnum.class),
        LiveStatusEnum.ERROR, EnumSet.of(LiveStatusEnum.NOT_STARTED, LiveStatusEnum.ENDED)
    );
    
    private LiveStatus(LiveStatusEnum value) {
        this.value = value;
    }
    
    /**
     * 创建未开始状态
     */
    public static LiveStatus notStarted() {
        return new LiveStatus(LiveStatusEnum.NOT_STARTED);
    }
    
    /**
     * 创建直播中状态
     */
    public static LiveStatus live() {
        return new LiveStatus(LiveStatusEnum.LIVE);
    }
    
    /**
     * 创建已结束状态
     */
    public static LiveStatus ended() {
        return new LiveStatus(LiveStatusEnum.ENDED);
    }
    
    /**
     * 创建回放中状态
     */
    public static LiveStatus replay() {
        return new LiveStatus(LiveStatusEnum.REPLAY);
    }
    
    /**
     * 创建异常状态
     */
    public static LiveStatus error() {
        return new LiveStatus(LiveStatusEnum.ERROR);
    }
    
    /**
     * 从枚举值创建
     */
    public static LiveStatus of(LiveStatusEnum status) {
        return new LiveStatus(status);
    }
    
    /**
     * 从数值创建
     */
    public static LiveStatus of(Long statusValue) {
        LiveStatusEnum status = LiveStatusEnum.fromValue(statusValue);
        return new LiveStatus(status);
    }
    
    /**
     * 检查是否可以转换到目标状态
     */
    public boolean canTransitionTo(LiveStatusEnum targetStatus) {
        EnumSet<LiveStatusEnum> allowedTransitions = TRANSITION_RULES.get(this.value);
        return allowedTransitions != null && allowedTransitions.contains(targetStatus);
    }
    
    /**
     * 转换到目标状态
     */
    public LiveStatus transitionTo(LiveStatusEnum targetStatus) {
        if (!canTransitionTo(targetStatus)) {
            throw new IllegalStateException(
                String.format("不能从状态 %s 转换到状态 %s", this.value, targetStatus));
        }
        return new LiveStatus(targetStatus);
    }
    
    /**
     * 是否为直播中状态
     */
    public boolean isLive() {
        return value == LiveStatusEnum.LIVE;
    }
    
    /**
     * 是否为已结束状态
     */
    public boolean isEnded() {
        return value == LiveStatusEnum.ENDED;
    }
    
    /**
     * 是否为未开始状态
     */
    public boolean isNotStarted() {
        return value == LiveStatusEnum.NOT_STARTED;
    }
    
    /**
     * 是否为异常状态
     */
    public boolean isError() {
        return value == LiveStatusEnum.ERROR;
    }
    
    /**
     * 获取状态的数值表示
     */
    public Long getNumericValue() {
        return value.getValue();
    }
    
    @Override
    public String toString() {
        return "LiveStatus{" + value + "}";
    }
}
