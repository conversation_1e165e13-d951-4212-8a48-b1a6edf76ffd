package com.ydwl.live.domain.vo;

import com.ydwl.common.excel.annotation.ExcelDictFormat;
import com.ydwl.common.excel.convert.ExcelDictConvert;
import com.ydwl.live.domain.LiveTransaction;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 交易记录视图对象 live_transaction
 *
 * <AUTHOR> <PERSON>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveTransaction.class)
public class LiveTransactionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易记录ID
     */
    @ExcelProperty(value = "交易记录ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 关联ID（订单/礼物记录）
     */
    @ExcelProperty(value = "关联ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "订=单/礼物记录")
    private Long relatedId;

    /**
     * 交易类型（1-充值，2-消费，3-收入，4-退款）
     */
    @ExcelProperty(value = "交易类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-充值，2-消费，3-收入，4-退款")
    private Long type;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额")
    private Long amount;

    /**
     * 交易后余额
     */
    @ExcelProperty(value = "交易后余额")
    private Long balance;

    /**
     * 交易详情
     */
    @ExcelProperty(value = "交易详情")
    private String detail;

    /**
     * 目标类型
     */
    @ExcelProperty(value = "目标类型")
    private Long targetType;

    /**
     * 目标ID
     */
    @ExcelProperty(value = "目标ID")
    private Long targetId;


}
