package com.ydwl.live.domain.vo;

import lombok.Data;
import java.util.Map;

/**
 * OSS预签名上传信息
 *
 * <AUTHOR> Yi
 */
@Data
public class OssPreSignedUrlVo {

    /**
     * 预签名上传URL
     */
    private String uploadUrl;

    /**
     * 对象键（文件路径）
     */
    private String objectKey;

    /**
     * 上传表单参数
     */
    private Map<String, String> formFields;

    /**
     * 过期时间戳（秒）
     */
    private Long expirationTime;

    /**
     * 文件大小限制（字节）
     */
    private Long maxFileSize;

    /**
     * 允许的文件类型
     */
    private String[] allowedContentTypes;

    /**
     * oss上传成功后的回调URL
     */
    private String callbackUrl;
} 