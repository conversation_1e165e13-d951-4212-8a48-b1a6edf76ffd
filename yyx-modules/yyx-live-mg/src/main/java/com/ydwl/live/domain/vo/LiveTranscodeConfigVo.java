package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeConfig;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 转码配置视图对象 live_transcode_config
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LiveTranscodeConfig.class)
public class LiveTranscodeConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 配置键
     */
    @ExcelProperty(value = "配置键")
    private String key;

    /**
     * 配置值
     */
    @ExcelProperty(value = "配置值")
    private String value;

    /**
     * 配置类型(fc-函数计算,mts-媒体转码)
     */
    @ExcelProperty(value = "配置类型(fc-函数计算,mts-媒体转码)")
    private String type;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    private String description;


}
