package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveOrderBo;
import com.ydwl.live.domain.vo.LiveOrderVo;
import com.ydwl.live.domain.LiveOrder;
import com.ydwl.live.mapper.LiveOrderMapper;
import com.ydwl.live.service.ILiveOrderService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveOrderServiceImpl implements ILiveOrderService {

    private final LiveOrderMapper baseMapper;

    /**
     * 查询订单
     *
     * @param id 主键
     * @return 订单
     */
    @Override
    public LiveOrderVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单分页列表
     */
    @Override
    public TableDataInfo<LiveOrderVo> queryPageList(LiveOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveOrder> lqw = buildQueryWrapper(bo);
        Page<LiveOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单列表
     *
     * @param bo 查询条件
     * @return 订单列表
     */
    @Override
    public List<LiveOrderVo> queryList(LiveOrderBo bo) {
        LambdaQueryWrapper<LiveOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveOrder> buildQueryWrapper(LiveOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveOrder::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), LiveOrder::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getUserId() != null, LiveOrder::getUserId, bo.getUserId());
        lqw.eq(bo.getProductId() != null, LiveOrder::getProductId, bo.getProductId());
        lqw.eq(bo.getOrderType() != null, LiveOrder::getOrderType, bo.getOrderType());
        lqw.eq(bo.getAmount() != null, LiveOrder::getAmount, bo.getAmount());
        lqw.eq(bo.getCoinAmount() != null, LiveOrder::getCoinAmount, bo.getCoinAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), LiveOrder::getPaymentMethod, bo.getPaymentMethod());
        lqw.eq(bo.getPaymentTime() != null, LiveOrder::getPaymentTime, bo.getPaymentTime());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionId()), LiveOrder::getTransactionId, bo.getTransactionId());
        lqw.eq(bo.getStatus() != null, LiveOrder::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增订单
     *
     * @param bo 订单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveOrderBo bo) {
        LiveOrder add = MapstructUtils.convert(bo, LiveOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单
     *
     * @param bo 订单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveOrderBo bo) {
        LiveOrder update = MapstructUtils.convert(bo, LiveOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveOrder entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
