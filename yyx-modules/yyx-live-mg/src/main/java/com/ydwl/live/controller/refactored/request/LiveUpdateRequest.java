package com.ydwl.live.controller.refactored.request;

import com.ydwl.common.core.validate.EditGroup;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 更新直播请求
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Data
public class LiveUpdateRequest {
    
    /**
     * 直播标题
     */
    @NotBlank(message = "直播标题不能为空", groups = {EditGroup.class})
    private String title;
    
    /**
     * 直播封面图片URL
     */
    private String coverImgUrl;
    
    /**
     * 计划开始时间
     */
    private LocalDateTime planStartTime;
    
    /**
     * 直播描述
     */
    private String description;
    
    /**
     * 标签列表
     */
    private String tagList;
}
