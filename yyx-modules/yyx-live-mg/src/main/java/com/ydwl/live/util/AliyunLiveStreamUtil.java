package com.ydwl.live.util;


import com.aliyun.live20161101.models.*;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.stereotype.Component;

import com.aliyun.live20161101.Client;

/**
 * 阿里云直播转码配置管理工具类
 * 功能说明：
 * 1. 提供直播转码配置的增删查操作
 * 2. 封装阿里云直播服务的转码API接口
 * 3. 使用Spring单例模式管理客户端实例保证线程安全
 * 注意事项：
 * - 需确保阿里云账号已开通直播服务
 * - 配置参数需与阿里云控制台保持一致
 * - 转码模板需提前用AliLiveRecordUtil.addLiveRecordConfig()方法添加
 *
 * <AUTHOR>
 * @date 2025-03.26
 */
@Component
public class AliyunLiveStreamUtil {

    // 阿里云认证配置
    private static final String ACCESS_KEY_ID = "LTAI5tPMJW2hGdqLiobHACqr";
    private static final String ACCESS_KEY_SECRET = "******************************";
    private static final String REGION_ID = "cn-beijing";

    // 直播服务配置
    private static final String DOMAIN_NAME = "play.live.ycyyx.com";
    private static final String OSS_ENDPOINT = "oss-cn-beijing.aliyuncs.com";
    private static final String OSS_BUCKET = "ydwl-live-recording";

    // 静态客户端实例（线程安全）
    private static final com.aliyun.live20161101.Client client;

    /*
      获取阿里云ACS客户端
      @return 配置好的ACS客户端实例
     * @implNote 双重校验锁确保线程安全的单例模式
     */
    static {
        try {
            // 创建客户端
            Config config = new Config()
                .setAccessKeyId(ACCESS_KEY_ID)
                .setAccessKeySecret(ACCESS_KEY_SECRET)
                .setRegionId(REGION_ID);
            client = new Client(config);
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize Aliyun Live Stream client", e);
        }
    }


    // =====================================================================================
    // 转码配置管理方法
    // =====================================================================================

    /**
     * 添加直播转码配置
     *
     * @param request 转码配置请求对象
     * @return 转码配置响应对象
     * @throws Exception 网络或API调用异常
     */
    public static AddLiveStreamTranscodeResponse AddLiveStreamTranscode(AddLiveStreamTranscodeRequest request) throws Exception {

        AddLiveStreamTranscodeRequest addLiveStreamTranscodeRequest = new AddLiveStreamTranscodeRequest();
        addLiveStreamTranscodeRequest.setDomain(request.getDomain());
        addLiveStreamTranscodeRequest.setApp(request.getApp());
        addLiveStreamTranscodeRequest.setLazy(request.getLazy());
        addLiveStreamTranscodeRequest.setTemplate(request.getTemplate());
        // 执行API调用
        return client.addLiveStreamTranscode(addLiveStreamTranscodeRequest);
    }

    /**
     * 删除直播转码配置
     *
     * @param request 转码配置删除请求对象
     * @return 删除操作响应对象
     * @throws Exception 网络或API调用异常
     */
    public static DeleteLiveStreamTranscodeResponse DeleteLiveStreamTranscode(DeleteLiveStreamTranscodeRequest request) throws Exception {
        DeleteLiveStreamTranscodeRequest req = new DeleteLiveStreamTranscodeRequest();
        req.setDomain(request.getDomain());
        req.setApp(request.getApp());
        req.setTemplate(request.getTemplate());

        // 执行删除操作
        return client.deleteLiveStreamTranscode(req);
    }

    /**
     * 查询直播转码配置信息
     *
     * @param request 查询请求对象
     * @return 转码配置信息响应对象
     * @throws Exception 网络或API调用异常
     */
    public static DescribeLiveStreamTranscodeInfoResponse DescribeLiveStreamTranscodeInfo(DescribeLiveStreamTranscodeInfoRequest request) throws Exception {
        DescribeLiveStreamTranscodeInfoRequest req = new DescribeLiveStreamTranscodeInfoRequest();
        req.setDomainTranscodeName(request.getDomainTranscodeName());
        req.setOwnerId(request.getOwnerId());
        // 执行查询操作
        return client.describeLiveStreamTranscodeInfo(req);
    }

    // =====================================================================================
    // 测试方法（main函数）
    // =====================================================================================

    public static void main(String[] args) throws Exception {
        // ============= 添加转码配置 =============
        AddLiveStreamTranscodeRequest addRequest = new AddLiveStreamTranscodeRequest();
        addRequest.setDomain(DOMAIN_NAME); // 推流域名
        addRequest.setApp("live-3");         // 应用名称
        addRequest.setLazy("true");        // 延迟转码模式
        addRequest.setTemplate("sd");      // 转码模板名称
        addRequest.setOwnerId(1L);

        AddLiveStreamTranscodeResponse response = AddLiveStreamTranscode(addRequest);
        if (response.getStatusCode() == 200) {
            System.out.println("转码配置添加成功，RequestId: " + response.getStatusCode());
        } else {
            System.out.println("转码配置添加失败"+ response.getStatusCode()+ response.getBody().getRequestId());
        }

        // ============= 查询转码配置 =============
        DescribeLiveStreamTranscodeInfoRequest describeRequest = new DescribeLiveStreamTranscodeInfoRequest();
        describeRequest.setDomainTranscodeName(DOMAIN_NAME); // 需要查询的域名

        DescribeLiveStreamTranscodeInfoResponse describeResponse = DescribeLiveStreamTranscodeInfo(describeRequest);
        DescribeLiveStreamTranscodeInfoResponseBody body = describeResponse.getBody();
        DescribeLiveStreamTranscodeInfoResponseBody.DescribeLiveStreamTranscodeInfoResponseBodyDomainTranscodeList domainTranscodeList = body.getDomainTranscodeList();
        domainTranscodeList.domainTranscodeInfo.forEach(
                transcodeInfo -> {
                    System.out.println("域名: " + transcodeInfo.getTranscodeApp());
                    System.out.println("应用: " + transcodeInfo.getTranscodeName());
                    System.out.println("模板: " + transcodeInfo.getTranscodeTemplate());
                }
        );
    }
}
