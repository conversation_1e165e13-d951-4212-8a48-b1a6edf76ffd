package com.ydwl.live.domain.service;

import com.ydwl.live.domain.model.valueobject.LiveId;
import com.ydwl.live.domain.model.valueobject.StreamInfo;

/**
 * 推流生成服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public interface StreamGenerationService {
    
    /**
     * 生成推流信息
     */
    StreamInfo generateStreamInfo(LiveId liveId, String title);
    
    /**
     * 刷新推流信息
     */
    StreamInfo refreshStreamInfo(LiveId liveId, String title);
}
