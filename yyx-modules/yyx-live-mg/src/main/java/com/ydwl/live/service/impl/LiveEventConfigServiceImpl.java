package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveEventConfigBo;
import com.ydwl.live.domain.vo.LiveEventConfigVo;
import com.ydwl.live.domain.LiveEventConfig;
import com.ydwl.live.mapper.LiveEventConfigMapper;
import com.ydwl.live.service.ILiveEventConfigService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 事件配置Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveEventConfigServiceImpl implements ILiveEventConfigService {

    private final LiveEventConfigMapper baseMapper;

    /**
     * 查询事件配置
     *
     * @param id 主键
     * @return 事件配置
     */
    @Override
    public LiveEventConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询事件配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 事件配置分页列表
     */
    @Override
    public TableDataInfo<LiveEventConfigVo> queryPageList(LiveEventConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveEventConfig> lqw = buildQueryWrapper(bo);
        Page<LiveEventConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的事件配置列表
     *
     * @param bo 查询条件
     * @return 事件配置列表
     */
    @Override
    public List<LiveEventConfigVo> queryList(LiveEventConfigBo bo) {
        LambdaQueryWrapper<LiveEventConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveEventConfig> buildQueryWrapper(LiveEventConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveEventConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveEventConfig::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getEventType()), LiveEventConfig::getEventType, bo.getEventType());
        lqw.eq(bo.getEventId() != null, LiveEventConfig::getEventId, bo.getEventId());
        lqw.eq(bo.getSignupStart() != null, LiveEventConfig::getSignupStart, bo.getSignupStart());
        lqw.eq(bo.getSignupEnd() != null, LiveEventConfig::getSignupEnd, bo.getSignupEnd());
        lqw.eq(bo.getMaxParticipants() != null, LiveEventConfig::getMaxParticipants, bo.getMaxParticipants());
        lqw.eq(StringUtils.isNotBlank(bo.getSignupFields()), LiveEventConfig::getSignupFields, bo.getSignupFields());
        return lqw;
    }

    /**
     * 新增事件配置
     *
     * @param bo 事件配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveEventConfigBo bo) {
        LiveEventConfig add = MapstructUtils.convert(bo, LiveEventConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改事件配置
     *
     * @param bo 事件配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveEventConfigBo bo) {
        LiveEventConfig update = MapstructUtils.convert(bo, LiveEventConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveEventConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除事件配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
