package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveVideoPlayRecord;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 视频播放记录业务对象 live_video_play_record
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveVideoPlayRecord.class, reverseConvertGenerate = false)
public class LiveVideoPlayRecordBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long liveId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 播放时间
     */
    @NotNull(message = "播放时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date playTime;

    /**
     * 播放时长(单位:秒)
     */
    private Long playDurationSeconds;

    /**
     * 最后播放位置(单位:秒)
     */
    private Long lastPositionSeconds;

    /**
     * 是否看完(0-否,1-是)
     */
    private Long isFinished;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 播放清晰度
     */
    private String videoQuality;


}
