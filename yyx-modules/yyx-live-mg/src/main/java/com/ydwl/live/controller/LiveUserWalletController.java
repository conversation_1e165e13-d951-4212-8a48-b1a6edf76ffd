package com.ydwl.live.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ydwl.common.idempotent.annotation.RepeatSubmit;
import com.ydwl.common.log.annotation.Log;
import com.ydwl.common.web.core.BaseController;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.core.domain.R;
import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.common.log.enums.BusinessType;
import com.ydwl.common.excel.utils.ExcelUtil;
import com.ydwl.live.domain.vo.LiveUserWalletVo;
import com.ydwl.live.domain.bo.LiveUserWalletBo;
import com.ydwl.live.service.ILiveUserWalletService;
import com.ydwl.common.mybatis.core.page.TableDataInfo;

/**
 * 用户钱包
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/live/userWallet")
public class LiveUserWalletController extends BaseController {

    private final ILiveUserWalletService liveUserWalletService;

    /**
     * 查询用户钱包列表
     */
    @SaCheckPermission("live:userWallet:list")
    @GetMapping("/list")
    public TableDataInfo<LiveUserWalletVo> list(LiveUserWalletBo bo, PageQuery pageQuery) {
        return liveUserWalletService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户钱包列表
     */
    @SaCheckPermission("live:userWallet:export")
    @Log(title = "用户钱包", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LiveUserWalletBo bo, HttpServletResponse response) {
        List<LiveUserWalletVo> list = liveUserWalletService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户钱包", LiveUserWalletVo.class, response);
    }

    /**
     * 获取用户钱包详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("live:userWallet:query")
    @GetMapping("/{id}")
    public R<LiveUserWalletVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(liveUserWalletService.queryById(id));
    }

    /**
     * 新增用户钱包
     */
    @SaCheckPermission("live:userWallet:add")
    @Log(title = "用户钱包", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LiveUserWalletBo bo) {
        return toAjax(liveUserWalletService.insertByBo(bo));
    }

    /**
     * 修改用户钱包
     */
    @SaCheckPermission("live:userWallet:edit")
    @Log(title = "用户钱包", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LiveUserWalletBo bo) {
        return toAjax(liveUserWalletService.updateByBo(bo));
    }

    /**
     * 删除用户钱包
     *
     * @param ids 主键串
     */
    @SaCheckPermission("live:userWallet:remove")
    @Log(title = "用户钱包", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(liveUserWalletService.deleteWithValidByIds(List.of(ids), true));
    }
}
