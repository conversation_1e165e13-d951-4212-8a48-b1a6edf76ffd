package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveUserSubscribe;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户订阅业务对象 live_user_subscribe
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveUserSubscribe.class, reverseConvertGenerate = false)
public class LiveUserSubscribeBo extends BaseEntity {

    /**
     * 订阅ID
     */
    @NotNull(message = "订阅ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 直播ID
     */
    @NotNull(message = "直播ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long liveId;

    /**
     * 订阅时间
     */
    @NotNull(message = "订阅时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date subscribeTime;

    /**
     * 是否已通知(0-未通知,1-已通知)
     */
    private Long isNotified;


}
