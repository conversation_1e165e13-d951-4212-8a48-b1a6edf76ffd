package com.ydwl.live.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

/**
 * 转码事件
 * 用于解耦转码服务调用，通过事件机制触发转码任务
 */
@Getter
public class TranscodeEvent extends ApplicationEvent {
    
    /**
     * 转码参数
     */
    private final Map<String, Object> transcodeParams;
    
    public TranscodeEvent(Object source, Map<String, Object> transcodeParams) {
        super(source);
        this.transcodeParams = transcodeParams;
    }
    
    public TranscodeEvent(Map<String, Object> transcodeParams) {
        super(transcodeParams);
        this.transcodeParams = transcodeParams;
    }
} 