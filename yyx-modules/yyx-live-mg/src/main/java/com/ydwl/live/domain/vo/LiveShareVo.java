package com.ydwl.live.domain.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.ydwl.live.domain.LiveShare;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 直播分享记录视图对象 live_share
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@AutoMapper(target = LiveShare.class)
public class LiveShareVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分享记录ID
     */
    @ExcelProperty(value = "分享记录ID")
    private Long id;

    /**
     * 直播ID
     */
    @ExcelProperty(value = "直播ID")
    private Long liveId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 分享平台
     */
    @ExcelProperty(value = "分享平台")
    private String sharePlatform;


}
