package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveTranscodeConfig;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 转码配置业务对象 live_transcode_config
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveTranscodeConfig.class, reverseConvertGenerate = false)
public class LiveTranscodeConfigBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 配置键
     */
    @NotBlank(message = "配置键不能为空", groups = { AddGroup.class, EditGroup.class })
    private String key;

    /**
     * 配置值
     */
    @NotBlank(message = "配置值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String value;

    /**
     * 配置类型(fc-函数计算,mts-媒体转码)
     */
    private String type;

    /**
     * 说明
     */
    private String description;


}
