package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveTransactionBo;
import com.ydwl.live.domain.vo.LiveTransactionVo;
import com.ydwl.live.domain.LiveTransaction;
import com.ydwl.live.mapper.LiveTransactionMapper;
import com.ydwl.live.service.ILiveTransactionService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 交易记录Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveTransactionServiceImpl implements ILiveTransactionService {

    private final LiveTransactionMapper baseMapper;

    /**
     * 查询交易记录
     *
     * @param id 主键
     * @return 交易记录
     */
    @Override
    public LiveTransactionVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询交易记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 交易记录分页列表
     */
    @Override
    public TableDataInfo<LiveTransactionVo> queryPageList(LiveTransactionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveTransaction> lqw = buildQueryWrapper(bo);
        Page<LiveTransactionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的交易记录列表
     *
     * @param bo 查询条件
     * @return 交易记录列表
     */
    @Override
    public List<LiveTransactionVo> queryList(LiveTransactionBo bo) {
        LambdaQueryWrapper<LiveTransaction> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveTransaction> buildQueryWrapper(LiveTransactionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveTransaction> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveTransaction::getId);
        lqw.eq(bo.getUserId() != null, LiveTransaction::getUserId, bo.getUserId());
        lqw.eq(bo.getRelatedId() != null, LiveTransaction::getRelatedId, bo.getRelatedId());
        lqw.eq(bo.getType() != null, LiveTransaction::getType, bo.getType());
        lqw.eq(bo.getAmount() != null, LiveTransaction::getAmount, bo.getAmount());
        lqw.eq(bo.getBalance() != null, LiveTransaction::getBalance, bo.getBalance());
        lqw.eq(StringUtils.isNotBlank(bo.getDetail()), LiveTransaction::getDetail, bo.getDetail());
        lqw.eq(bo.getTargetType() != null, LiveTransaction::getTargetType, bo.getTargetType());
        lqw.eq(bo.getTargetId() != null, LiveTransaction::getTargetId, bo.getTargetId());
        return lqw;
    }

    /**
     * 新增交易记录
     *
     * @param bo 交易记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveTransactionBo bo) {
        LiveTransaction add = MapstructUtils.convert(bo, LiveTransaction.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改交易记录
     *
     * @param bo 交易记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveTransactionBo bo) {
        LiveTransaction update = MapstructUtils.convert(bo, LiveTransaction.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveTransaction entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除交易记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
