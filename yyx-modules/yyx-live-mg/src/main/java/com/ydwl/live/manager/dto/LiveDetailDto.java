package com.ydwl.live.manager.dto;

import com.ydwl.live.domain.model.valueobject.LiveSettings;
import com.ydwl.live.domain.model.valueobject.StreamInfo;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 直播详情DTO
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Data
@Builder
public class LiveDetailDto {
    
    /**
     * 直播ID
     */
    private Long liveId;
    
    /**
     * 直播分类ID
     */
    private Long categoryId;
    
    /**
     * 直播标题
     */
    private String title;
    
    /**
     * 直播封面图片URL
     */
    private String coverImgUrl;
    
    /**
     * 计划开始时间
     */
    private LocalDateTime planStartTime;
    
    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;
    
    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;
    
    /**
     * 实际直播时长(分钟)
     */
    private Long durationMinutes;
    
    /**
     * 直播状态
     */
    private Long status;
    
    /**
     * 状态文本
     */
    private String statusText;
    
    /**
     * 直播描述
     */
    private String description;
    
    /**
     * 标签列表
     */
    private String tagList;
    
    /**
     * 观看人数
     */
    private Long viewCount;
    
    /**
     * 点赞数
     */
    private Long likeCount;
    
    /**
     * 分享数
     */
    private Long shareCount;
    
    /**
     * 最大同时在线人数
     */
    private Long maxOnlineCount;
    
    /**
     * 当前在线人数
     */
    private Long currentOnlineCount;
    
    /**
     * 推流信息
     */
    private StreamInfo streamInfo;
    
    /**
     * 直播设置
     */
    private LiveSettings settings;
}
