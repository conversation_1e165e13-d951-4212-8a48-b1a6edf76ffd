package com.ydwl.live.infrastructure.service;

import com.ydwl.live.domain.model.valueobject.LiveId;
import com.ydwl.live.domain.model.valueobject.StreamInfo;
import com.ydwl.live.domain.service.StreamGenerationService;
import com.ydwl.live.util.AliLiveUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 推流生成服务实现
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StreamGenerationServiceImpl implements StreamGenerationService {
    
    @Override
    public StreamInfo generateStreamInfo(LiveId liveId, String title) {
        try {
            // 使用现有的阿里云直播工具生成推流信息
            AliLiveUtil.PushUrlRequest request = new AliLiveUtil.PushUrlRequest(
                liveId.getValue().toString(), "live");
            AliLiveUtil.PushInfo pushInfo = AliLiveUtil.generatePushUrl(request);
            
            // 生成播放地址
            Map<String, String> playUrls = generatePlayUrls(liveId.getValue().toString());
            
            // 创建推流信息值对象
            StreamInfo streamInfo = StreamInfo.create(
                generateStreamId(), // 生成推流ID
                pushInfo.getPushUrl(),
                pushInfo.getAuthKey(),
                playUrls
            );
            
            log.info("生成推流信息成功: liveId={}, streamId={}", 
                    liveId.getValue(), streamInfo.getStreamId());
            
            return streamInfo;
        } catch (Exception e) {
            log.error("生成推流信息失败: liveId={}", liveId.getValue(), e);
            throw new RuntimeException("生成推流信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public StreamInfo refreshStreamInfo(LiveId liveId, String title) {
        try {
            // 重新生成推流信息
            return generateStreamInfo(liveId, title);
        } catch (Exception e) {
            log.error("刷新推流信息失败: liveId={}", liveId.getValue(), e);
            throw new RuntimeException("刷新推流信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成播放地址
     */
    private Map<String, String> generatePlayUrls(String streamName) {
        Map<String, String> playUrls = new HashMap<>();
        
        // 这里应该根据实际的CDN配置生成不同格式的播放地址
        String baseUrl = "https://your-cdn-domain.com/live/" + streamName;
        
        playUrls.put("rtmp", "rtmp://your-cdn-domain.com/live/" + streamName);
        playUrls.put("flv", baseUrl + ".flv");
        playUrls.put("m3u8", baseUrl + ".m3u8");
        
        return playUrls;
    }
    
    /**
     * 生成推流ID
     */
    private Long generateStreamId() {
        // 简化实现，实际应该使用分布式ID生成器
        return System.currentTimeMillis();
    }
}
