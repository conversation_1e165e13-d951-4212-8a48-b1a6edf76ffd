package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveTranscodeOutputBo;
import com.ydwl.live.domain.vo.LiveTranscodeOutputVo;
import com.ydwl.live.domain.LiveTranscodeOutput;
import com.ydwl.live.mapper.LiveTranscodeOutputMapper;
import com.ydwl.live.service.ILiveTranscodeOutputService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 转码输出文件Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveTranscodeOutputServiceImpl implements ILiveTranscodeOutputService {

    private final LiveTranscodeOutputMapper baseMapper;

    /**
     * 查询转码输出文件
     *
     * @param id 主键
     * @return 转码输出文件
     */
    @Override
    public LiveTranscodeOutputVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询转码输出文件列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转码输出文件分页列表
     */
    @Override
    public TableDataInfo<LiveTranscodeOutputVo> queryPageList(LiveTranscodeOutputBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveTranscodeOutput> lqw = buildQueryWrapper(bo);
        Page<LiveTranscodeOutputVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的转码输出文件列表
     *
     * @param bo 查询条件
     * @return 转码输出文件列表
     */
    @Override
    public List<LiveTranscodeOutputVo> queryList(LiveTranscodeOutputBo bo) {
        LambdaQueryWrapper<LiveTranscodeOutput> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据任务ID和分辨率以及模板ID查询转码输出记录
     * 
     * @param taskId 任务ID
     * @param resolution 分辨率
     * @param templateId 模板ID
     * @return 转码输出记录
     */
    @Override
    public LiveTranscodeOutputVo queryByTaskIdAndDefinition(Long taskId, String resolution, String templateId) {
        LambdaQueryWrapper<LiveTranscodeOutput> lqw = Wrappers.lambdaQuery();
        lqw.eq(LiveTranscodeOutput::getTaskId, taskId);
        lqw.eq(StringUtils.isNotBlank(resolution), LiveTranscodeOutput::getResolution, resolution);
        // providerTemplateId在LiveTranscodeOutput中不存在
        // 使用分辨率和定义作为唯一性判断
        
        // 只返回一条记录
        List<LiveTranscodeOutputVo> list = baseMapper.selectVoList(lqw);
        return list.isEmpty() ? null : list.get(0);
    }

    private LambdaQueryWrapper<LiveTranscodeOutput> buildQueryWrapper(LiveTranscodeOutputBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveTranscodeOutput> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveTranscodeOutput::getId);
        lqw.eq(bo.getTaskId() != null, LiveTranscodeOutput::getTaskId, bo.getTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getDefinition()), LiveTranscodeOutput::getDefinition, bo.getDefinition());
        lqw.eq(StringUtils.isNotBlank(bo.getResolution()), LiveTranscodeOutput::getResolution, bo.getResolution());
        lqw.eq(StringUtils.isNotBlank(bo.getFormat()), LiveTranscodeOutput::getFormat, bo.getFormat());
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), LiveTranscodeOutput::getUrl, bo.getUrl());
        lqw.eq(bo.getSegmentTime() != null, LiveTranscodeOutput::getSegmentTime, bo.getSegmentTime());
        lqw.eq(bo.getFileSize() != null, LiveTranscodeOutput::getFileSize, bo.getFileSize());
        lqw.eq(bo.getBitRate() != null, LiveTranscodeOutput::getBitRate, bo.getBitRate());
        lqw.eq(bo.getDuration() != null, LiveTranscodeOutput::getDuration, bo.getDuration());
        return lqw;
    }

    /**
     * 新增转码输出文件
     *
     * @param bo 转码输出文件
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveTranscodeOutputBo bo) {
        LiveTranscodeOutput add = MapstructUtils.convert(bo, LiveTranscodeOutput.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改转码输出文件
     *
     * @param bo 转码输出文件
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveTranscodeOutputBo bo) {
        LiveTranscodeOutput update = MapstructUtils.convert(bo, LiveTranscodeOutput.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveTranscodeOutput entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除转码输出文件信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
