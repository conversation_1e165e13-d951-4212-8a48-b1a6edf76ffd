package com.ydwl.live.service;

import com.ydwl.live.domain.vo.LiveTranscodeTemplateVo;
import com.ydwl.live.domain.bo.LiveTranscodeTemplateBo;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 视频转码模板Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface ILiveTranscodeTemplateService {

    /**
     * 查询视频转码模板
     *
     * @param id 主键
     * @return 视频转码模板
     */
    LiveTranscodeTemplateVo queryById(Long id);

    /**
     * 分页查询视频转码模板列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频转码模板分页列表
     */
    TableDataInfo<LiveTranscodeTemplateVo> queryPageList(LiveTranscodeTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的视频转码模板列表
     *
     * @param bo 查询条件
     * @return 视频转码模板列表
     */
    List<LiveTranscodeTemplateVo> queryList(LiveTranscodeTemplateBo bo);

    /**
     * 新增视频转码模板
     *
     * @param bo 视频转码模板
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveTranscodeTemplateBo bo);

    /**
     * 修改视频转码模板
     *
     * @param bo 视频转码模板
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveTranscodeTemplateBo bo);

    /**
     * 校验并批量删除视频转码模板信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
