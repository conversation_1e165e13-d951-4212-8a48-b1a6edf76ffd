package com.ydwl.live.service.impl;

import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveVideoUploadBo;
import com.ydwl.live.domain.vo.LiveVideoUploadVo;
import com.ydwl.live.domain.LiveVideoUpload;
import com.ydwl.live.mapper.LiveVideoUploadMapper;
import com.ydwl.live.service.ILiveVideoUploadService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 视频上传记录Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class LiveVideoUploadServiceImpl implements ILiveVideoUploadService {

    private final LiveVideoUploadMapper baseMapper;

    /**
     * 查询视频上传记录
     *
     * @param id 主键
     * @return 视频上传记录
     */
    @Override
    public LiveVideoUploadVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询视频上传记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频上传记录分页列表
     */
    @Override
    public TableDataInfo<LiveVideoUploadVo> queryPageList(LiveVideoUploadBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveVideoUpload> lqw = buildQueryWrapper(bo);
        Page<LiveVideoUploadVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的视频上传记录列表
     *
     * @param bo 查询条件
     * @return 视频上传记录列表
     */
    @Override
    public List<LiveVideoUploadVo> queryList(LiveVideoUploadBo bo) {
        LambdaQueryWrapper<LiveVideoUpload> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LiveVideoUpload> buildQueryWrapper(LiveVideoUploadBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveVideoUpload> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveVideoUpload::getId);
        lqw.eq(bo.getLiveId() != null, LiveVideoUpload::getLiveId, bo.getLiveId());
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), LiveVideoUpload::getFileName, bo.getFileName());
        lqw.eq(bo.getFileSizeBytes() != null, LiveVideoUpload::getFileSizeBytes, bo.getFileSizeBytes());
        lqw.eq(StringUtils.isNotBlank(bo.getFileType()), LiveVideoUpload::getFileType, bo.getFileType());
        lqw.eq(StringUtils.isNotBlank(bo.getOssUrl()), LiveVideoUpload::getOssUrl, bo.getOssUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getFileMd5()), LiveVideoUpload::getFileMd5, bo.getFileMd5());
        lqw.eq(bo.getVideoDurationSeconds() != null, LiveVideoUpload::getVideoDurationSeconds, bo.getVideoDurationSeconds());
        lqw.eq(StringUtils.isNotBlank(bo.getVideoResolution()), LiveVideoUpload::getVideoResolution, bo.getVideoResolution());
        lqw.eq(bo.getVideoBitrateKbps() != null, LiveVideoUpload::getVideoBitrateKbps, bo.getVideoBitrateKbps());
        lqw.eq(bo.getUploadStatus() != null, LiveVideoUpload::getUploadStatus, bo.getUploadStatus());
        lqw.eq(bo.getUploadProgressPercent() != null, LiveVideoUpload::getUploadProgressPercent, bo.getUploadProgressPercent());
        lqw.eq(bo.getUploadCompleteTime() != null, LiveVideoUpload::getUploadCompleteTime, bo.getUploadCompleteTime());
        lqw.eq(StringUtils.isNotBlank(bo.getErrorMessage()), LiveVideoUpload::getErrorMessage, bo.getErrorMessage());
        return lqw;
    }

    /**
     * 新增视频上传记录
     *
     * @param bo 视频上传记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveVideoUploadBo bo) {
        LiveVideoUpload add = MapstructUtils.convert(bo, LiveVideoUpload.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改视频上传记录
     *
     * @param bo 视频上传记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveVideoUploadBo bo) {
        LiveVideoUpload update = MapstructUtils.convert(bo, LiveVideoUpload.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveVideoUpload entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除视频上传记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
