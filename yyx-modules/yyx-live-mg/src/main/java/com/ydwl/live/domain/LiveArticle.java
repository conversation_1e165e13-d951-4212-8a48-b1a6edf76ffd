package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 直播文章对象 live_article
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_article")
public class LiveArticle extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文章ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联业务ID
     */
    private Long relatedId;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 文章内容
     */
    private String content;

    /**
     * 发布状态(0-草稿,1-已发布,2-已归档)
     */
    private Long publishStatus;

    /**
     * 关联类型(live-直播,course-课程,product-产品)
     */
    private String relatedType;

    /**
     * 内容格式(html-网页,markdown-标记,plain-纯文本)
     */
    private String contentFormat;

    /**
     * 封面图片URL
     */
    private String coverImgUrl;

    /**
     * 文章摘要
     */
    private String summary;

    /**
     * 最后编辑时间
     */
    private Date lastEditTime;

    /**
     * 是否精选(0-否,1-是)
     */
    private Long isFeatured;

    /**
     * 是否允许评论(0-否,1-是)
     */
    private Long isCommentAllowed;

    /**
     * 浏览次数
     */
    private Long viewCount;

    /**
     * 点赞次数
     */
    private Long likeCount;

    /**
     * 分享次数
     */
    private Long shareCount;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
