package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 事件配置对象 live_event_config
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_event_config")
public class LiveEventConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 事件类型（live）
     */
    private String eventType;

    /**
     * 事件ID
     */
    private Long eventId;

    /**
     * 报名开始时间
     */
    private Date signupStart;

    /**
     * 报名截止时间
     */
    private Date signupEnd;

    /**
     * 最大参与人数
     */
    private Long maxParticipants;

    /**
     * 报名需填字段配置
     */
    private String signupFields;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;


}
