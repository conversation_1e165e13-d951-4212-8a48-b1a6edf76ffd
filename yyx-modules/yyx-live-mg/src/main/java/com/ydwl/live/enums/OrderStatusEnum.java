package com.ydwl.live.enums;

import lombok.Getter;

/**
 * 枚举类，表示直播订单状态
 */
@Getter
public enum OrderStatusEnum {
    /**
     * 订单待处理
     */
    PENDING(0),
    /**
     * 订单已支付
     */
    PAID(1),
    /**
     * 订单已退款
     */
    REFUNDED(2),
    /**
     * 订单已取消
     */
    CANCELLED(3);

    private final int status;

    // 定义带参构造函数
    OrderStatusEnum(int status) {
        this.status = status;
    }

    /**
     * 根据状态码获取枚举实例
     *
     * @param status 状态码
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果状态码未知
     */
    public static OrderStatusEnum fromStatus(int status) {
        return java.util.Arrays.stream(values())
            .filter(orderStatus -> orderStatus.getStatus() == status)
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Unknown status: " + status));
    }

    /**
     * 返回枚举值的字符串表示
     *
     * @return 枚举值的字符串表示
     */
    @Override
    public String toString() {
        return this.name();
    }

    /**
     * 返回枚举值的描述信息
     *
     * @return 枚举值的描述信息
     */
    public String getDescription() {
        return switch (this) {
            case PENDING -> "订单待处理";
            case PAID -> "订单已支付";
            case REFUNDED -> "订单已退款";
            case CANCELLED -> "订单已取消";
            default -> "未知状态";
        };
    }
}
