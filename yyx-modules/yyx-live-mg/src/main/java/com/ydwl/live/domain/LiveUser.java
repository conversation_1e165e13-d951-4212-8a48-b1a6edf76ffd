package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 用户对象 live_user
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_user")
public class LiveUser extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 微信唯一标识
     */
    private String openid;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户状态(0-禁用,1-正常)
     */
    private Long userStatus;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 性别(0-未知,1-男,2-女)
     */
    private Long gender;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 通知偏好设置
     */
    private String notificationSettings;

    /**
     * 用户行为统计
     */
    private String userStatistics;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
