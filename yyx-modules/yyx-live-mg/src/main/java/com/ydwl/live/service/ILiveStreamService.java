package com.ydwl.live.service;

import com.ydwl.common.mybatis.core.page.PageQuery;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.live.domain.bo.LiveStreamBo;
import com.ydwl.live.domain.vo.GetStreamVo;
import com.ydwl.live.domain.vo.LiveStreamVo;
import com.ydwl.live.service.model.LiveStreamInfo;

import java.util.Collection;
import java.util.List;

/**
 * 推流信息Service接口
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
public interface ILiveStreamService {

    /**
     * 查询推流信息
     *
     * @param id 主键
     * @return 推流信息
     */
    LiveStreamVo queryById(Long id);

    /**
     * 分页查询推流信息列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 推流信息分页列表
     */
    TableDataInfo<LiveStreamVo> queryPageList(LiveStreamBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的推流信息列表
     *
     * @param bo 查询条件
     * @return 推流信息列表
     */
    List<LiveStreamVo> queryList(LiveStreamBo bo);

    /**
     * 新增推流信息
     *
     * @param bo 推流信息
     * @return 是否新增成功
     */
    Boolean insertByBo(LiveStreamBo bo);

    /**
     * 修改推流信息
     *
     * @param bo 推流信息
     * @return 是否修改成功
     */
    Boolean updateByBo(LiveStreamBo bo);

    /**
     * 校验并批量删除推流信息信息
     *
     * @param ids 待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量新增或修改推流信息
     *
     * @param bo 推流信息DTO
     * @return 操作是否成功
     */
    Boolean InsertOrUpdateLiveStreamDTO(com.ydwl.live.callback.dto.InsertOrUpdateLiveStreamDTO bo);

    /**
     * 根据直播ID查询推流信息
     *
     * @param liveId 直播ID
     * @return 推流信息视图对象
     */
    GetStreamVo queryByLiveId(Long liveId);
    
    /**
     * 根据直播ID查询推流信息（使用共享数据模型）
     *
     * @param liveId 直播ID
     * @return 推流信息数据模型
     */
    LiveStreamInfo getStreamInfoByLiveId(Long liveId);
    
    /**
     * 为直播创建推流信息
     *
     * @param liveId 直播ID
     * @param title 直播标题
     * @return 创建的推流信息
     */
    LiveStreamInfo createStreamForLive(Long liveId, String title);

    /**
     * 刷新推流信息
     *
     * @param liveId 直播ID
     * @return 更新后的推流信息
     */
    GetStreamVo refreshPushStream(Long liveId);
    
    /**
     * 刷新推流信息（使用共享数据模型）
     *
     * @param liveId 直播ID
     * @return 更新后的推流信息
     */
    LiveStreamInfo refreshStreamInfo(Long liveId);
}
