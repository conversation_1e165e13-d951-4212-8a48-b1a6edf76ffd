package com.ydwl.live.callback.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 直播状态枚举
 */
@Getter
@AllArgsConstructor
public enum LiveStatusEnum {

    NOT_STARTED(0L, "未开始"),
    LIVING(1L, "直播中"),
    ENDED(2L, "已结束"),
    REPLAY_PROCESSING(3L, "回放处理中"),
    REPLAY_READY(4L, "回放就绪"),
    REPLAY_EXPIRED(5L, "回放已过期"),
    CANCELLED(6L, "已取消");

    private final Long code;
    private final String desc;

    public static LiveStatusEnum getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (LiveStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    public static boolean isValidTransition(LiveStatusEnum from, LiveStatusEnum to) {
        if (from == null || to == null) {
            return false;
        }

        // 定义状态转换规则
        switch (from) {
            case NOT_STARTED:
                return to == LIVING || to == CANCELLED;
            case LIVING:
                return to == ENDED || to == CANCELLED;
            case ENDED:
                return to == REPLAY_PROCESSING || to == CANCELLED;
            case REPLAY_PROCESSING:
                return to == REPLAY_READY || to == CANCELLED;
            case REPLAY_READY:
                return to == REPLAY_EXPIRED || to == CANCELLED;
            case REPLAY_EXPIRED:
            case CANCELLED:
                return false;
            default:
                return false;
        }
    }
}
