package com.ydwl.live.domain;

import com.ydwl.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 用户钱包对象 live_user_wallet
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("live_user_wallet")
public class LiveUserWallet extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 钱包ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 余额(单位:元)
     */
    private Long balance;

    /**
     * 累计充值(单位:元)
     */
    private Long totalRecharge;

    /**
     * 累计消费(单位:元)
     */
    private Long totalConsume;

    /**
     * 是否删除(0-否,1-是)
     */
    @TableLogic
    private Long delFlag;


}
