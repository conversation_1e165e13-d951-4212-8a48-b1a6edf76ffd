package com.ydwl.live.service.optimized;

import com.ydwl.live.callback.dto.FcTranscodeCallbackResponseDto;
import com.ydwl.live.callback.vo.UniversalTranscodeCallbackVo;
import com.ydwl.live.domain.bo.LiveTranscodeTaskBo;
import com.ydwl.live.domain.vo.LiveTranscodeTaskVo;
import com.ydwl.live.service.ILiveTranscodeTaskService;
import com.ydwl.live.service.ITranscodeCallbackService;
import com.ydwl.live.util.DistributedLockUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 优化后的转码回调服务
 * 
 * 解决幂等性、并发安全等问题
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OptimizedTranscodeCallbackService implements ITranscodeCallbackService {
    
    private final ILiveTranscodeTaskService liveTranscodeTaskService;
    private final DistributedLockUtil distributedLockUtil;
    
    private static final String CALLBACK_LOCK_PREFIX = "transcode:callback:";
    private static final long CALLBACK_LOCK_TIMEOUT = 30; // 30秒回调锁
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFcTranscodeSuccess(FcTranscodeCallbackResponseDto callbackData) {
        String bizId = callbackData.getUserData();
        if (bizId == null || bizId.trim().isEmpty()) {
            log.error("FC转码成功回调：BizId为空");
            return;
        }
        
        // 使用分布式锁保证回调幂等性
        String lockKey = CALLBACK_LOCK_PREFIX + bizId;
        String lockValue = UUID.randomUUID().toString();
        
        if (!distributedLockUtil.lock(lockKey, lockValue, CALLBACK_LOCK_TIMEOUT, TimeUnit.SECONDS)) {
            log.warn("FC转码回调正在处理中，跳过重复处理: BizId={}", bizId);
            return;
        }
        
        try {
            // 1. 查询任务状态，确保幂等性
            LiveTranscodeTaskVo taskVo = liveTranscodeTaskService.queryByBizId(bizId);
            if (taskVo == null) {
                log.error("FC转码成功回调：找不到对应的转码任务，BizId: {}", bizId);
                return;
            }
            
            // 检查任务是否已经处理完成
            if (isTaskCompleted(taskVo)) {
                log.info("FC转码回调：任务已完成，避免重复处理。BizID: {}", bizId);
                return;
            }
            
            // 检查回调状态，避免重复处理
            if (taskVo.getCallbackStatus() != null && taskVo.getCallbackStatus() == 1L) {
                log.info("FC转码回调：回调已处理，避免重复处理。BizID: {}", bizId);
                return;
            }
            
            // 2. 更新任务状态
            LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
            taskBo.setId(taskVo.getId());
            taskBo.setStatus(2L); // 成功
            taskBo.setEndTime(new Date());
            taskBo.setCallbackStatus(1L); // 已回调
            
            // 设置输出信息
            if (callbackData.getOutput() != null && !callbackData.getOutput().isEmpty()) {
                String outputUrl = callbackData.getOutput().get(0).getObjectName();
                taskBo.setOutputUrl(outputUrl);
            }
            
            boolean updated = liveTranscodeTaskService.updateByBo(taskBo);
            if (!updated) {
                log.error("FC转码成功回调：更新任务状态失败，BizId: {}", bizId);
                throw new RuntimeException("更新任务状态失败");
            }
            
            // 3. 触发后续处理（如生成回放记录）
            handleTranscodeSuccess(taskVo, callbackData);
            
            log.info("FC转码成功回调处理完成，BizId: {}", bizId);
            
        } finally {
            distributedLockUtil.unlock(lockKey, lockValue);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFcTranscodeError(FcTranscodeCallbackResponseDto callbackData) {
        String bizId = callbackData.getUserData();
        if (bizId == null || bizId.trim().isEmpty()) {
            log.error("FC转码失败回调：BizId为空");
            return;
        }
        
        String lockKey = CALLBACK_LOCK_PREFIX + bizId;
        String lockValue = UUID.randomUUID().toString();
        
        if (!distributedLockUtil.lock(lockKey, lockValue, CALLBACK_LOCK_TIMEOUT, TimeUnit.SECONDS)) {
            log.warn("FC转码回调正在处理中，跳过重复处理: BizId={}", bizId);
            return;
        }
        
        try {
            // 1. 查询任务状态
            LiveTranscodeTaskVo taskVo = liveTranscodeTaskService.queryByBizId(bizId);
            if (taskVo == null) {
                log.error("FC转码失败回调：找不到对应的转码任务，BizId: {}", bizId);
                return;
            }
            
            // 检查是否已经处理
            if (isTaskFailed(taskVo) && taskVo.getCallbackStatus() != null && taskVo.getCallbackStatus() == 1L) {
                log.info("FC转码失败回调：任务已标记为失败，避免重复处理。BizID: {}", bizId);
                return;
            }
            
            // 2. 更新任务状态
            String errorMessage = extractErrorMessage(callbackData);
            Long retryCount = taskVo.getRetryCount() != null ? taskVo.getRetryCount() : 0L;
            
            LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
            taskBo.setId(taskVo.getId());
            taskBo.setStatus(3L); // 失败
            taskBo.setErrorMsg(errorMessage);
            taskBo.setEndTime(new Date());
            taskBo.setCallbackStatus(1L); // 已回调
            taskBo.setRetryCount(retryCount + 1);
            
            boolean updated = liveTranscodeTaskService.updateByBo(taskBo);
            if (!updated) {
                log.error("FC转码失败回调：更新任务状态失败，BizId: {}", bizId);
                throw new RuntimeException("更新任务状态失败");
            }
            
            // 3. 处理重试逻辑
            handleTranscodeFailure(taskVo, retryCount + 1, errorMessage);
            
            log.info("FC转码失败回调处理完成，BizId: {}", bizId);
            
        } finally {
            distributedLockUtil.unlock(lockKey, lockValue);
        }
    }
    
    @Override
    public void handleMtsTranscodeSuccess(UniversalTranscodeCallbackVo callbackData) {
        // MTS转码成功处理逻辑
        log.info("MTS转码成功回调：{}", callbackData);
        // 实现类似的幂等性和并发控制逻辑
    }
    
    @Override
    public void handleMtsTranscodeError(UniversalTranscodeCallbackVo callbackData) {
        // MTS转码失败处理逻辑
        log.info("MTS转码失败回调：{}", callbackData);
        // 实现类似的幂等性和并发控制逻辑
    }
    
    /**
     * 检查任务是否已完成
     */
    private boolean isTaskCompleted(LiveTranscodeTaskVo taskVo) {
        return taskVo.getStatus() != null && taskVo.getStatus() == 2L;
    }
    
    /**
     * 检查任务是否已失败
     */
    private boolean isTaskFailed(LiveTranscodeTaskVo taskVo) {
        return taskVo.getStatus() != null && taskVo.getStatus() == 3L;
    }
    
    /**
     * 提取错误信息
     */
    private String extractErrorMessage(FcTranscodeCallbackResponseDto callbackData) {
        if (callbackData.getErrorMessage() != null) {
            return callbackData.getErrorMessage();
        }
        return "转码失败，未知错误";
    }
    
    /**
     * 处理转码成功后续逻辑
     */
    private void handleTranscodeSuccess(LiveTranscodeTaskVo taskVo, FcTranscodeCallbackResponseDto callbackData) {
        try {
            // 这里可以添加成功后的处理逻辑
            // 如：生成回放记录、发送通知等
            log.info("转码成功，开始后续处理: taskId={}", taskVo.getId());
            
        } catch (Exception e) {
            log.error("转码成功后续处理异常: taskId={}", taskVo.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }
    
    /**
     * 处理转码失败重试逻辑
     */
    private void handleTranscodeFailure(LiveTranscodeTaskVo taskVo, Long retryCount, String errorMessage) {
        final int MAX_RETRY_COUNT = 3;
        
        if (retryCount < MAX_RETRY_COUNT) {
            log.info("转码失败，准备重试: taskId={}, 当前重试次数: {}", taskVo.getId(), retryCount);
            // 这里可以实现重试逻辑
            // 如：重新提交转码任务
        } else {
            log.warn("转码失败，已达到最大重试次数: taskId={}, 错误信息: {}", taskVo.getId(), errorMessage);
            // 这里可以实现最终失败的处理逻辑
            // 如：发送告警通知
        }
    }
}
