package com.ydwl.live.callback.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 直播回放状态枚举
 */
@Getter
@AllArgsConstructor
public enum ReplayStatusEnum {

    PROCESSING(0L, "处理中"),
    READY(1L, "可观看"),
    EXPIRED(2L, "已过期");

    private final Long code;
    private final String desc;

    public static ReplayStatusEnum getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (ReplayStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
