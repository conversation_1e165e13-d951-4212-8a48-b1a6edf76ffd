package com.ydwl.live.domain.bo;

import com.ydwl.common.core.validate.AddGroup;
import com.ydwl.common.core.validate.EditGroup;
import com.ydwl.live.domain.LiveArticle;
import com.ydwl.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 直播文章业务对象 live_article
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LiveArticle.class, reverseConvertGenerate = false)
public class LiveArticleBo extends BaseEntity {

    /**
     * 文章ID
     */
    @NotNull(message = "文章ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 关联业务ID
     */
    private Long relatedId;

    /**
     * 文章标题
     */
    @NotBlank(message = "文章标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 文章内容
     */
    @NotBlank(message = "文章内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 发布状态(0-草稿,1-已发布,2-已归档)
     */
    @NotNull(message = "发布状态(0-草稿,1-已发布,2-已归档)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long publishStatus;

    /**
     * 关联类型(live-直播,course-课程,product-产品)
     */
    private String relatedType;

    /**
     * 内容格式(html-网页,markdown-标记,plain-纯文本)
     */
    @NotBlank(message = "内容格式(html-网页,markdown-标记,plain-纯文本)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contentFormat;

    /**
     * 封面图片URL
     */
    private String coverImgUrl;

    /**
     * 文章摘要
     */
    private String summary;

    /**
     * 最后编辑时间
     */
    private Date lastEditTime;

    /**
     * 是否精选(0-否,1-是)
     */
    private Long isFeatured;

    /**
     * 是否允许评论(0-否,1-是)
     */
    private Long isCommentAllowed;

    /**
     * 浏览次数
     */
    private Long viewCount;

    /**
     * 点赞次数
     */
    private Long likeCount;

    /**
     * 分享次数
     */
    private Long shareCount;


}
