package com.ydwl.live.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁工具类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DistributedLockUtil {

    private final RedisTemplate<String, Object> redisTemplate;

    private static final String LOCK_PREFIX = "lock:";
    private static final long DEFAULT_TIMEOUT = 30;
    private static final TimeUnit DEFAULT_TIME_UNIT = TimeUnit.SECONDS;

    private static final DefaultRedisScript<Long> LOCK_SCRIPT = new DefaultRedisScript<>();
    private static final DefaultRedisScript<Long> UNLOCK_SCRIPT = new DefaultRedisScript<>();

    static {
        // 加锁脚本
        LOCK_SCRIPT.setScriptText(
            "if redis.call('setnx', KEYS[1], ARGV[1]) == 1 then " +
            "redis.call('pexpire', KEYS[1], ARGV[2]) " +
            "return 1 " +
            "else " +
            "return 0 " +
            "end"
        );
        LOCK_SCRIPT.setResultType(Long.class);

        // 解锁脚本
        UNLOCK_SCRIPT.setScriptText(
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
            "return redis.call('del', KEYS[1]) " +
            "else " +
            "return 0 " +
            "end"
        );
        UNLOCK_SCRIPT.setResultType(Long.class);
    }

    /**
     * 获取分布式锁
     *
     * @param key 锁的key
     * @param value 锁的值（建议使用UUID）
     * @return 是否获取成功
     */
    public boolean lock(String key, String value) {
        return lock(key, value, DEFAULT_TIMEOUT, DEFAULT_TIME_UNIT);
    }

    /**
     * 获取分布式锁
     *
     * @param key 锁的key
     * @param value 锁的值（建议使用UUID）
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否获取成功
     */
    public boolean lock(String key, String value, long timeout, TimeUnit unit) {
        try {
            String lockKey = LOCK_PREFIX + key;
            Long result = redisTemplate.execute(
                LOCK_SCRIPT,
                Collections.singletonList(lockKey),
                value,
                String.valueOf(unit.toMillis(timeout))
            );
            return result != null && result == 1;
        } catch (Exception e) {
            log.error("获取分布式锁失败", e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     *
     * @param key 锁的key
     * @param value 锁的值
     * @return 是否释放成功
     */
    public boolean unlock(String key, String value) {
        try {
            String lockKey = LOCK_PREFIX + key;
            Long result = redisTemplate.execute(
                UNLOCK_SCRIPT,
                Collections.singletonList(lockKey),
                value
            );
            return result != null && result == 1;
        } catch (Exception e) {
            log.error("释放分布式锁失败", e);
            return false;
        }
    }
}
