//package com.ydwl.live.controller;
//
//import com.ydwl.common.core.domain.R;
//import com.ydwl.common.log.annotation.Log;
//import com.ydwl.common.log.enums.BusinessType;
//import com.ydwl.common.web.core.BaseController;
//import com.ydwl.live.callback.dto.ChatMessageDTO;
//import com.ydwl.live.response.ApiResult;
//import com.ydwl.live.service.LiveChatService;
//import jakarta.validation.constraints.NotNull;
//import lombok.RequiredArgsConstructor;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
//
//import java.util.List;
//
///**
// * 直播间聊天控制器
// */
//@Validated
//@RestController
//@RequiredArgsConstructor
//@RequestMapping("/live/live/chat")
//public class LiveChatController extends BaseController {
//
//    private final LiveChatService liveChatService;
//
//    /**
//     * 获取直播间聊天记录
//     * 使用SSE实时推送聊天记录
//     */
//    @GetMapping("/{liveId}")
//    public SseEmitter getLiveChat(@PathVariable("liveId") @NotNull(message = "直播ID不能为空") String liveId) {
//        return liveChatService.getLiveChat(liveId);
//    }
//
//    /**
//     * 发送聊天消息
//     */
//    @Log(title = "发送聊天消息", businessType = BusinessType.INSERT)
//    @PostMapping("/send")
//    public R<Void> sendMessage(@Validated @RequestBody ChatMessageDTO message) {
//        return toAjax(liveChatService.sendChatMessage(message));
//    }
//
//    /**
//     * 开启直播间聊天室
//     */
//    @Log(title = "开启直播间聊天室", businessType = BusinessType.UPDATE)
//    @PostMapping("/open/{liveId}")
//    public R<Void> openChat(@PathVariable("liveId") @NotNull(message = "直播ID不能为空") String liveId) {
//        return toAjax(liveChatService.openLiveChat(liveId));
//    }
//
//    /**
//     * 关闭直播间聊天室
//     */
//    @Log(title = "关闭直播间聊天室", businessType = BusinessType.UPDATE)
//    @PostMapping("/close/{liveId}")
//    public R<Void> closeChat(@PathVariable("liveId") @NotNull(message = "直播ID不能为空") String liveId) {
//        return toAjax(liveChatService.closeLiveChat(liveId));
//    }
//
//    /**
//     * 检查聊天室状态
//     */
//    @GetMapping("/status/{liveId}")
//    public R<Boolean> checkChatStatus(@PathVariable("liveId") @NotNull(message = "直播ID不能为空") String liveId) {
//        return R.ok(liveChatService.isLiveChatOpen(liveId));
//    }
//
//    /**
//     * 获取历史聊天记录
//     */
//    @GetMapping("/history/{liveId}")
//    public ApiResult<List<Object>> getChatHistory(
//        @PathVariable("liveId") @NotNull(message = "直播ID不能为空") String liveId,
//        @RequestParam @NotNull(message = "开始位置不能为空") long start,
//        @RequestParam @NotNull(message = "结束位置不能为空") long end
//    ) {
//        List<Object> chatHistory = liveChatService.getChatHistory(liveId, start, end);
//        return ApiResult.data(chatHistory);
//    }
//
//    /**
//     * 获取历史聊天记录总数
//     */
//    @GetMapping("/history/count/{liveId}")
//    public R<Long> getChatHistorySize(@PathVariable("liveId") @NotNull(message = "直播ID不能为空") String liveId) {
//        return R.ok(liveChatService.getChatHistorySize(liveId));
//    }
//
//    /**
//     * 获取最新聊天记录
//     */
//    @GetMapping("/latest/{liveId}")
//    public R<List<Object>> getLatestChatMessages(
//        @PathVariable("liveId") @NotNull(message = "直播ID不能为空") String liveId,
//        @RequestParam(defaultValue = "20") @NotNull(message = "消息数量不能为空") int count
//    ) {
//        return R.ok(liveChatService.getLatestChatMessages(liveId, count));
//    }
//}
