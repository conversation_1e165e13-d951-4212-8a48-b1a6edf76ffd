package com.ydwl.live.mapper;

import com.ydwl.live.domain.LiveCategory;
import com.ydwl.live.domain.vo.LiveCategoryVo;
import com.ydwl.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 直播分类Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface LiveCategoryMapper extends BaseMapperPlus<LiveCategory, LiveCategoryVo> {

    /**
     * 根据分类路径查询所有子分类ID
     *
     * @param path 分类路径
     * @return 子分类ID列表
     */
    @Select("SELECT id FROM live_category WHERE path LIKE CONCAT(#{path},',%') OR path = #{path}")
    List<Long> selectChildIdsByPath(@Param("path") String path);
    
    /**
     * 根据父分类ID查询所有子分类ID
     *
     * @param parentId 父分类ID
     * @return 子分类ID列表
     */
    @Select("SELECT id FROM live_category WHERE parent_id = #{parentId}")
    List<Long> selectChildIdsByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询热门分类列表
     *
     * @param limit 限制数量
     * @return 热门分类列表
     */
    @Select("SELECT * FROM live_category WHERE status = 1 ORDER BY view_count DESC, live_count DESC LIMIT #{limit}")
    List<LiveCategoryVo> selectHotCategories(@Param("limit") Integer limit);
}
