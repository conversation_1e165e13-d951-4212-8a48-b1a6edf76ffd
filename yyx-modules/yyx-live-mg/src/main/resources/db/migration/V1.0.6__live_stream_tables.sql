-- 推流信息表
CREATE TABLE IF NOT EXISTS `live_stream` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '推流信息ID',
  `live_id` bigint(20) NOT NULL COMMENT '直播ID',
  `push_url` varchar(500) NOT NULL COMMENT '推流地址',
  `push_key` varchar(100) NOT NULL COMMENT '推流密钥',
  `stream_status` bigint(20) NOT NULL DEFAULT '0' COMMENT '状态(0-创建中,1-正常,2-异常)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` bigint(20) DEFAULT '0' COMMENT '是否删除(0-否,1-是)',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_live_id` (`live_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推流信息表';

-- 更新直播表，添加stream_id字段关联
ALTER TABLE `live` 
ADD COLUMN IF NOT EXISTS `stream_id` bigint(20) DEFAULT NULL COMMENT '关联推流ID' AFTER `status`;

-- 系统配置表添加推流配置项
INSERT INTO `sys_config` (`config_name`, `config_key`, `config_value`, `config_type`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`, `tenant_id`) 
VALUES 
('直播推流域名', 'ydwl.live.stream.push-domain', 'push.live.ycyyx.com', 'Y', '推流服务域名', NOW(), NOW(), 'admin', 'admin', '0', '000000'),
('直播播放域名', 'ydwl.live.stream.play-domain', 'play.live.ycyyx.com', 'Y', '播放服务域名', NOW(), NOW(), 'admin', 'admin', '0', '000000'),
('直播应用名称', 'ydwl.live.stream.app-name', 'live', 'Y', '直播应用名称', NOW(), NOW(), 'admin', 'admin', '0', '000000'),
('推流鉴权密钥', 'ydwl.live.stream.push-secret-key', '2p5n2R8aA6tTQtu2', 'Y', '推流鉴权密钥', NOW(), NOW(), 'admin', 'admin', '0', '000000'),
('播放鉴权密钥', 'ydwl.live.stream.play-secret-key', 'LznJujBX81mOJM7E', 'Y', '播放鉴权密钥', NOW(), NOW(), 'admin', 'admin', '0', '000000'),
('播放URL有效期', 'ydwl.live.stream.play-url-expire-seconds', '86400', 'Y', '播放URL有效期（秒）', NOW(), NOW(), 'admin', 'admin', '0', '000000')
ON DUPLICATE KEY UPDATE 
  `config_value` = VALUES(`config_value`),
  `update_time` = NOW(),
  `update_by` = 'admin'; 