# Spring配置
spring:
  application:
    name: ydwl-live
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  # jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false

# MyBatis配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.ydwl.**.domain
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      # 逻辑删除字段名
      logic-delete-field: delFlag
      # 逻辑删除字面值：未删除为0
      logic-not-delete-value: 0
      # 逻辑删除字面值：删除为1
      logic-delete-value: 1
      # 更新策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      update-strategy: NOT_NULL
  # 原生配置
  configuration:
    # 控制台打印完整带参数SQL语句
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启驼峰命名法映射
    map-underscore-to-camel-case: true
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
    # 返回map的值不做自动映射
    auto-mapping-behavior: full
    # 设置当查询结果值为null时是否调用映射对象的setter方法
    return-instance-for-empty-row: false

# 直播服务配置
ydwl:
  live:
    # 推流配置
    stream:
      # 推流域名
      push-domain: push.live.ycyyx.com
      # 播放域名
      play-domain: play.live.ycyyx.com
      # 应用名称
      app-name: live
      # 推流鉴权密钥
      push-secret-key: 2p5n2R8aA6tTQtu2
      # 播放鉴权密钥
      play-secret-key: LznJujBX81mOJM7E
      # 播放URL有效期（秒）
      play-url-expire-seconds: 86400
    # 任务配置
    task:
      # 是否启用定时任务
      enabled: true
      # 直播状态检查间隔（秒）
      status-check-interval: 60
      # 推流超时时间（秒），超过此时间无推流则自动结束直播
      push-timeout: 300 