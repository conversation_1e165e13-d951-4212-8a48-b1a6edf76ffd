package ${packageName}.domain;

#foreach ($column in $columns)
#if($column.javaField=='tenantId')
#set($IsTenant=1)
#end
#end
#if($IsTenant==1)
import core.com.ydwl.common.tenant.TenantEntity;
#else
#end
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
#foreach ($import in $importList)
import ${import};
#end

import java.io.Serial;

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($IsTenant==1)
#set($Entity="TenantEntity")
#else
#set($Entity="BaseEntity")
#end
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("${tableName}")
public class ${ClassName} extends ${Entity} {

    @Serial
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /**
     * $column.columnComment
     */
#if($column.javaField=='delFlag')
    @TableLogic
#end
#if($column.javaField=='version')
    @Version
#end
#if($column.isPk==1)
    @TableId(value = "$column.columnName")
#end
    private $column.javaType $column.javaField;

#end
#end

}
