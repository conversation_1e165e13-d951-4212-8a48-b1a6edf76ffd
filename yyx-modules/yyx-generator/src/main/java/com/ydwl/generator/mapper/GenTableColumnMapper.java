package com.ydwl.generator.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ydwl.common.mybatis.core.mapper.BaseMapperPlus;
import com.ydwl.generator.domain.GenTableColumn;

/**
 * 业务字段 数据层
 *
 * <AUTHOR>
 */
@InterceptorIgnore(dataPermission = "true", tenantLine = "true")
public interface GenTableColumnMapper extends BaseMapperPlus<GenTableColumn, GenTableColumn> {

}
