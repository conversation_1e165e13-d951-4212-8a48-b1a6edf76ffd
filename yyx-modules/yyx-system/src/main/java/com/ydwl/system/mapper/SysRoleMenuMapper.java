package com.ydwl.system.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ydwl.common.mybatis.core.mapper.BaseMapperPlus;
import com.ydwl.system.domain.SysRoleMenu;

import java.util.List;

/**
 * 角色与菜单关联表 数据层
 *
 * <AUTHOR>
 */
public interface SysRoleMenuMapper extends BaseMapperPlus<SysRoleMenu, SysRoleMenu> {

    /**
     * 根据菜单ID串删除关联关系
     *
     * @return 结果
     */
    default int deleteByMenuIds(List<Long> menuIds) {
        LambdaUpdateWrapper<SysRoleMenu> lqw = new LambdaUpdateWrapper<SysRoleMenu>()
            .in(SysRoleMenu::getMenuId, menuIds);
        return this.delete(lqw);
    }

}
