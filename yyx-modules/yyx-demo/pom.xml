<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ydwl</groupId>
        <artifactId>yyx-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yyx-demo</artifactId>

    <description>
        demo模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ydwl</groupId>
            <artifactId>yyx-common-websocket</artifactId>
        </dependency>

    </dependencies>

</project>
